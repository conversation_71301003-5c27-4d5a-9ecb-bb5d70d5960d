<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yf</groupId>
    <artifactId>yf-exam-server</artifactId>
    <packaging>pom</packaging>
    <version>6.4.0.23042803</version>
    <name>yf-exam-server</name>

    <modules>
        <module>yf-core</module>
        <module>yf-ability</module>
        <module>yf-job</module>
        <module>yf-modules</module>
        <module>yf-web</module>
        <module>deploy</module>
    </modules>

    <properties>
        <spring-cloud.version>Hoxton.SR3</spring-cloud.version>
        <sping-boot.version>2.2.8.RELEASE</sping-boot.version>
        <!-- alibaba版本。注：https://github.com/alibaba/spring-cloud-alibaba/wiki/版本说明 -->
        <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>
        <!-- spring-cloud 2020* 版本把bootstrap禁用了，所以单独引用 -->
        <spring-cloud-bootstrap.version>4.0.4</spring-cloud-bootstrap.version>
        <spring.version>5.2.8.RELEASE</spring.version>
        <tomcat.version>9.0.36</tomcat.version>
        <fastjson.version>1.2.83</fastjson.version>
        <oss.version>3.7.0</oss.version>
        <aliyun.sdk.version>4.4.9</aliyun.sdk.version>
        <swagger.version>2.9.2</swagger.version>
        <dozer.version>5.5.1</dozer.version>
        <apache.commons.version>3.8</apache.commons.version>
        <mysql.driver.version>8.0.11</mysql.driver.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <lombok.version>1.18.4</lombok.version>
        <thymeleaf.version>3.0.15.RELEASE</thymeleaf.version>
        <alicloud.version>2.1.1.RELEASE</alicloud.version>
        <poi.version>4.1.2</poi.version>
        <aspectj.version>1.9.5</aspectj.version>
        <hutool.version>5.7.17</hutool.version>
        <shiro.version>1.13.0</shiro.version>
        <log4j2.version>2.19.0</log4j2.version>
        <jackson.version>2.13.4</jackson.version>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.8.RELEASE</version>
    </parent>

    <!-- 内部项目版本引用，统一使用外层版本号 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-ability</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-job</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-modules</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-book</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-cert</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-course</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-exam</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-openapi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-pay</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-repo</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-stat</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-system</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-train</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-notify</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-mall</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-sign</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-jiandanai</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-xxkj</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-ai</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yf</groupId>
                <artifactId>yf-module-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- spring-cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring-cloud-alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring-cloud对bootstrap文件加载支持 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>${spring-cloud-bootstrap.version}</version>
            </dependency>

            <!-- 三方版本引用-->
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>1.9.5</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- poi office -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!--JWT-->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>4.4.0</version>
            </dependency>

            <!-- SpringBoot Redis支持 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-tomcat</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <!--shiro-->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring-boot-starter</artifactId>
                <version>${shiro.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- shiro-redis -->
            <dependency>
                <groupId>org.crazycake</groupId>
                <artifactId>shiro-redis</artifactId>
                <version>3.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.shiro</groupId>
                        <artifactId>shiro-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 文件转换，通过OpenOffice或LibreOffice来转换 -->
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-core</artifactId>
                <version>4.4.4</version>
            </dependency>
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-spring-boot-starter</artifactId>
                <version>4.4.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-local</artifactId>
                <version>4.4.4</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.6.1</version>
            </dependency>


            <!-- OSS文件上传及文档转换 -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${oss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-imm</artifactId>
                <version>2.1.17</version>
            </dependency>

            <!-- 阿里云视频转码 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-mts</artifactId>
                <version>2.5.2</version>
            </dependency>
            <!-- jedis -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>2.9.3</version>
            </dependency>

            <!-- 直播SDK -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-live</artifactId>
                <version>3.9.0</version>
            </dependency>

            <!-- 腾讯云 -->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>5.6.52</version>
            </dependency>

            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos-sts_api</artifactId>
                <version>3.1.0</version>
            </dependency>

            <!-- 七牛云 -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>[7.14.0, 7.14.99]</version>
            </dependency>


            <!-- 图形验证码 -->
            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>1.6.2</version>
            </dependency>


            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>2.0.1</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.11.0</version>
            </dependency>


            <!-- 阿里云人脸认证 start -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>facebody20191230</artifactId>
                <version>1.0.19</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>facebody20200910</artifactId>
                <version>3.0.0</version>
            </dependency>


            <!-- 用于上传临时文件的 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>viapi-utils</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-viapiutils</artifactId>
                <version>1.0.0</version>
            </dependency>

            <!-- 阿里云人脸认证 end -->


            <!-- 百度人脸认证 start -->
            <dependency>
                <groupId>com.baidu.aip</groupId>
                <artifactId>java-sdk</artifactId>
                <version>4.16.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-simple</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 百度人脸认证 end -->


            <!-- 腾讯云人脸识别 -->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-iai</artifactId>
                <version>3.1.471</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-faceid</artifactId>
                <version>3.1.471</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-ses</artifactId>
                <version>3.1.471</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>3.1.471</version>
            </dependency>


            <!-- 通用工具包 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-poi</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- 证书模板 -->
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>1.10.0</version>
            </dependency>

            <!-- 文件工具获取MineType -->
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>2.4.0</version>
            </dependency>



            <!-- 获取视频时长 -->
            <dependency>
                <groupId>com.googlecode.mp4parser</groupId>
                <artifactId>isoparser</artifactId>
                <version>1.1.22</version>
            </dependency>


            <!-- 钉钉授权登录 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>1.2.21</version>
            </dependency>


            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>3.1.0</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>net.sf.dozer</groupId>
                <artifactId>dozer</artifactId>
                <version>${dozer.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.driver.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.4</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-aop</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.plugin</groupId>
                        <artifactId>spring-plugin-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>org.springframework.plugin</groupId>
                <artifactId>spring-plugin-core</artifactId>
                <version>1.2.0.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-ui</artifactId>
                <version>2.0.9</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.1</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!--spring quartz依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>

            <!-- 缓存支持 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>


            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>

            <!-- WEB支持 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>

            <!-- WebSocket用于文件阅读 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.2.14</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf-spring5</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>${sping-boot.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
 

    <build>
        <finalName>${project.name}</finalName>
        <defaultGoal>compile</defaultGoal>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.1</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

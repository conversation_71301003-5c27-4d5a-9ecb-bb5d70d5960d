package com.maliang;

import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugins.annotations.Mojo;
import org.apache.maven.plugins.annotations.Parameter;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

@Mojo(name = "aliyun")
public class AliyunMojo extends AbstractMojo {

    @Parameter(defaultValue = "${project.basedir}/src/main/resources/aliyun-deploy.json", property = "configFile", required = true)
    private File configFile;

    public void execute() throws MojoExecutionException {
        Properties config = new Properties();
        try (InputStream input = new FileInputStream(configFile)) {
            config.load(input);
            String ip = config.getProperty("ip");
            System.out.println(ip);
        } catch (IOException ex) {
            throw new MojoExecutionException("Failed to load configuration file", ex);
        }
    }
}
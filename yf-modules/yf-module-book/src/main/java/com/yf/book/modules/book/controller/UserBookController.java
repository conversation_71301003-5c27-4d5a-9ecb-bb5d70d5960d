package com.yf.book.modules.book.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import com.yf.base.api.api.dto.BaseIdsReqDTO;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.book.modules.book.dto.UserBookDTO;
import com.yf.book.modules.book.service.UserBookService;
import com.yf.system.modules.user.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 错题本控制器
 * </p>
 * <AUTHOR>
 * @since 2020-05-27 17:56
 */
@Api(tags = {"错题本"})
@RestController
@RequestMapping("/api/user/wrong-book")
public class UserBookController extends BaseController {

    @Autowired
    private UserBookService baseService;


    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "批量删除")
    @PostMapping("/delete")
    public ApiRest delete(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        baseService.delete(UserUtils.getUserId(), reqDTO.getIds());
        return super.success();
    }

    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "分页查找")
    @PostMapping("/paging")
    public ApiRest<IPage<UserBookDTO>> paging(@RequestBody PagingReqDTO<UserBookDTO> reqDTO) {

        //分页查询并转换
        IPage<UserBookDTO> page = baseService.paging(reqDTO);

        return super.success(page);
    }
}

package com.yf.ai.modules.ai.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 聊天消息请求DTO
 */
@Data
@ApiModel(value="聊天消息请求参数", description="聊天消息请求参数")
public class ChatMessageReqDTO {

    @ApiModelProperty(value = "输入参数")
    private Map<String, Object> inputs;

    @ApiModelProperty(value = "用户问题")
    private String query;

    @ApiModelProperty(value = "响应模式")
    private String responseMode = "streaming";

    @ApiModelProperty(value = "会话ID")
    private String conversationId;

    @ApiModelProperty(value = "用户ID")
    private String user;
} 
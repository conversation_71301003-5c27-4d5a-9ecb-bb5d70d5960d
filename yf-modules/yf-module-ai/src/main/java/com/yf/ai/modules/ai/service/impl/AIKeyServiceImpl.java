package com.yf.ai.modules.ai.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yf.ai.modules.ai.dto.request.AiKeyDTO;
import com.yf.ai.modules.ai.entity.AiKey;
import com.yf.ai.modules.ai.mapper.AiKeyMapper;
import com.yf.ai.modules.ai.service.AiKeyService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.utils.BeanMapper;
import com.yf.base.utils.jackson.JsonHelper;
import com.yf.system.modules.user.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ai key 管理业务逻辑层
 * </p>
 * <AUTHOR>
 */
@Slf4j
@Service
public class AIKeyServiceImpl extends ServiceImpl<AiKeyMapper, AiKey> implements AiKeyService {


    @Override
    public IPage<AiKeyDTO> paging(PagingReqDTO<AiKeyDTO> reqDTO) {
        String userId = UserUtils.getUserId();
        // 创建分页对象
        Page query = reqDTO.toPage();
        AiKeyDTO params = reqDTO.getParams();
        //查询条件
        QueryWrapper<AiKey> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AiKey::getCreateBy,userId);
        if (params != null) {
            if (!StringUtils.isEmpty(params.getName())) {
                wrapper.lambda().like(AiKey::getName, params.getName());
            }
        }
        // 按更新时间倒序
        wrapper.lambda().orderByDesc(AiKey::getCreateTime);
        //获得数据
        IPage<AiKey> page = this.page(query, wrapper);
        //转换结果
        IPage<AiKeyDTO> pageData = JsonHelper.parseObject(page, new TypeReference<Page<AiKeyDTO>>() {
        });
        return pageData;
    }

    @Override
    public void saveAiKey(AiKeyDTO reqDTO) {
        String userId = UserUtils.getUserId();
        // 复制基本信息
        AiKey entity = new AiKey();
        BeanMapper.copy(reqDTO, entity);
        // ID
        boolean add = StringUtils.isBlank(entity.getId());
        if (add) {
            entity.setId(IdWorker.getIdStr());
            entity.setCreateBy(userId);
            // 生成32位随机字符作为key，并确保不重复
            while (true) {
                String apiKey = RandomStringUtils.randomAlphanumeric(12);
                // 检查key是否已存在
                LambdaQueryWrapper<AiKey> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AiKey::getApiKey, apiKey);
                if (this.count(wrapper) == 0) {
                    entity.setApiKey(apiKey);
                    break;
                }
            }
        }
        // 添加或更新
        if (add) {
            this.save(entity);
        } else {
            // 更新数据
            this.updateById(entity);
        }
    }

    @Override
    public Object deleteById(String id) {
        String userId = UserUtils.getUserId();
        AiKey selectById = baseMapper.selectById(id);
        if (null != selectById && !selectById.getCreateBy().equals(userId)) {
            throw new ServiceException("您不能删除他人数据！");
        }
        return baseMapper.deleteById(id);
    }

    @Override
    public Object disableKey(String id) {
        String userId = UserUtils.getUserId();
        AiKey selectById = baseMapper.selectById(id);
        if (null != selectById && !selectById.getCreateBy().equals(userId)) {
            throw new ServiceException("您不能操作他人数据！");
        }
        selectById.setStatus(1);
        return baseMapper.updateById(selectById);
    }

    @Override
    public Object enableKey(String id) {
        String userId = UserUtils.getUserId();
        AiKey selectById = baseMapper.selectById(id);
        if (null != selectById && !selectById.getCreateBy().equals(userId)) {
            throw new ServiceException("您不能操作他人数据！");
        }
        selectById.setStatus(0);
        return baseMapper.updateById(selectById);
    }
}

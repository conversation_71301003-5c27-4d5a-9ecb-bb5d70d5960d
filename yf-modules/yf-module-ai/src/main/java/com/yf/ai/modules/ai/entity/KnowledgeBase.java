package com.yf.ai.modules.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 知识库实体类
 * </p>
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@TableName(value = "knowledge_base", autoResultMap = true)
public class KnowledgeBase implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 知识库名称
     */
    @TableField("name")
    private String name;
    
    /**
     * 知识库描述
     */
    @TableField("description")
    private String description;
    
    /**
     * Dify知识库ID
     */
    @TableField("dify_dataset_id")
    private String difyDatasetId;

    /**
     * 文档个数
     */
    @TableField("doc_count")
    @DefaultValue("0")
    private Integer docCount;

    /**
     * 使用空间(KB)
     */
    @TableField("storage_space")
    @DefaultValue("0")
    private Long storageSpace;

    /**
     * 调用次数
     */
    @TableField("call_count")
    @DefaultValue("0")
    private Long callCount;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date updateTime;
    
    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField
    @DefaultValue("0")
    private Boolean isDeleted;
} 
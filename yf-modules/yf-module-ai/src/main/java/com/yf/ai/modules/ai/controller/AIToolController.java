package com.yf.ai.modules.ai.controller;

import com.yf.ai.modules.ai.service.AIToolService;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * ai工具
 * </p>
 * <AUTHOR>
 */
@Slf4j
@Api(tags = {"ai工具"})
@RestController
@RequestMapping("/ai/tool")
public class AIToolController extends BaseController {

    @Autowired
    private AIToolService aiToolService;

    /**
     * 手写识别
     * @param file 图片文件
     * @return 识别结果
     */
    @ApiOperation("手写文字识别")
    @PostMapping("/recognizeHandWriting")
    public ApiRest<String> recognizeHandWriting(@RequestParam("file") MultipartFile file) throws Exception {
        return super.success(aiToolService.recognizeHandWriting(file));
    }

    /**
     * 文字生成语音
     * @param content
     * @return
     */
    @ApiOperation("语音合成")
    @PostMapping("/speechSynthesis")
    public ApiRest<String> speechSynthesis(@RequestParam String content, @RequestParam  Integer sex, @RequestParam  Float rate, @RequestParam  Integer volume) throws IOException {
        return super.success(aiToolService.speechSynthesis(content, sex, rate, volume));
    }

    /**
     * 语音转文字
     * @param file
     * @return
     */
    @ApiOperation("语音识别")
    @PostMapping("/speechRecognition")
    public ApiRest<String> speechRecognition(@RequestParam("file") MultipartFile file) throws Exception {
        return super.success(aiToolService.speechRecognition(file));
    }

    @PostMapping(value = "/unifiedChat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation(value = "统一对话接口", notes = "根据chatType执行不同类型的对话")
    public Flux<String> unifiedChat(@RequestParam(name = "file",required = false) MultipartFile file,
                                  @RequestParam(name = "key",required = false) String key,
                                  @RequestParam(name = "sn",required = false) String sn,
                                  @RequestParam(name = "audio",required = false,defaultValue = "false") Boolean audio,
                                  @RequestParam(name = "audioName",required = false) String audioName,
                                  @RequestParam(value = "query", required = false) String query,
                                  @RequestParam(value = "datasetId", required = false) String datasetId,
                                  @RequestParam(value = "record", required = false) String record,
                                  HttpServletRequest request,
                                  HttpServletResponse response) throws Exception {
        return aiToolService.unifiedChat(file, key, sn, audio, audioName, query, datasetId, record, request, response);
    }

}

package com.yf.ai.modules.ai.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 图片聊天请求DTO
 */
@Data
@ApiModel(value="图片聊天请求参数", description="图片聊天请求参数")
public class ImageChatReqDTO {

    @ApiModelProperty(value = "输入参数")
    private Map<String, Object> inputs;

    @ApiModelProperty(value = "用户问题")
    private String query = "图片内容是什么？";

    @ApiModelProperty(value = "响应模式")
    private String responseMode = "streaming";

    @ApiModelProperty(value = "会话ID")
    private String conversationId;

    @ApiModelProperty(value = "用户ID")
    private String user;

    @ApiModelProperty(value = "文件列表")
    private List<ImageFile> files;

    @Data
    public static class ImageFile {
        @ApiModelProperty(value = "文件类型")
        private String type = "image";

        @ApiModelProperty(value = "传输方式")
        private String transferMethod = "local_file";

        @ApiModelProperty(value = "上传文件ID")
        private String uploadFileId;
    }
} 
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.yf</groupId>
        <artifactId>yf-modules</artifactId>
        <version>6.4.0.23042803</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yf</groupId>
    <artifactId>yf-module-ai</artifactId>
    <packaging>jar</packaging>
    <name>yf-module-ai</name>

    <dependencies>
        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-repo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-ocr_api20210707</artifactId>
            <version>3.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.16.10</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 腾讯云 TTS SDK -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-speech-sdk-java</artifactId>
            <version>1.0.53</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-xxkj</artifactId>
        </dependency>
        <!-- Spring WebFlux -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>
    </dependencies>
</project>
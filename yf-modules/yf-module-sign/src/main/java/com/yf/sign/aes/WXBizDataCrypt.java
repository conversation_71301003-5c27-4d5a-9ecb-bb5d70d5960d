package com.yf.sign.aes;

import org.apache.tomcat.util.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.AlgorithmParameters;
import java.security.Security;
import java.util.Arrays;


/**
 * <AUTHOR>
 */
public class WXBizDataCrypt {
    private String appId;
    private String sessionKey;

    public WXBizDataCrypt(String appId, String sessionKey) {
        this.appId = appId;
        this.sessionKey = sessionKey;
    }

    public String decrypt(String encryptedData, String iv) throws Exception {
        byte[] sessionKey = Base64.decodeBase64(this.sessionKey);
        byte[] ivByte = Base64.decodeBase64(iv);
        String result = decodePKCS7(sessionKey, ivByte, encryptedData);
        return result;
    }

    private static String decodePKCS7(byte[] keyByte, byte[] ivByte, String data) throws Exception {
        // 如果密钥不足16位则补足
        int base = 16;
        if (keyByte.length % base != 0) {
            int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
            byte[] temp = new byte[groups * base];
            Arrays.fill(temp, (byte) 0);
            System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
            keyByte = temp;
        }
        // 初始化
        Security.addProvider(new BouncyCastleProvider());
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
        SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
        AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
        parameters.init(new IvParameterSpec(ivByte));
        cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
        byte[] resultByte = cipher.doFinal(Base64.decodeBase64(data));
        return new String(resultByte, "UTF-8");
    }
}

package com.yf.sign.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

//签到打卡
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(autoResultMap = true, value = "sign_in")
@Table(name = "sign_in")//actable自动建表插件注解
public class SignIn extends Model<SignIn> {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;//ID
    @TableField(fill = FieldFill.INSERT)
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date createDate;//创建日期
    @TableField(fill = FieldFill.UPDATE)
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date modifyDate;//修改日期

    @TableField
    private String userId;//studentId

    @TableField
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date signDate;//签到日期

    @TableField
    private String signDateStr;//签到日期 yyyy-MM-dd 用于统计

    @TableField
    private String voice;//语音内容
    @TableField
    private String video;//视频内容
    @TableField
    private String content;//文字内容

    @TableField
    private String longitude;//经度
    @TableField
    private String latitude;//纬度
    @TableField
    private String location;//位置
    @TableField
    private Integer dayNumber;//天数，第几天打卡
    @TableField
    private Integer continuousDay;//连续打卡天数（每条记录会查询之前的天数）
    @Column(type = MySqlTypeConstant.JSON)
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> photoList;//图片

    @TableField
    @DefaultValue("0")
    private Integer status;//状态，0：待审查，1：正常，2：被举报，3：禁止查看
    @TableField
    private Integer likeNum;//点赞数

    @TableLogic
    @TableField
    @DefaultValue("0")
    private Boolean isDeleted;//是否删除

    @TableField(exist = false)
    private List<SignComment> signComments;
    @TableField(exist = false)
    private Boolean isLike;
    @TableField(exist = false)
    private String nickName;
    @TableField(exist = false)
    private String avatar;
    @TableField(exist = false)
    private Integer signCommentCount;
}
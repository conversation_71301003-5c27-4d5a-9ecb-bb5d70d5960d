package com.yf.sign.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yf.base.api.exception.ServiceException;
import com.yf.sign.utils.EncryptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class GPTServiceImpl {
    @Value("${gpt.address}")
    private String address;
    @Value("${gpt.appid}")
    private String appid;
    @Value("${gpt.appsecret}")
    private String appsecret;

    public String getAnswer(String bookName, String question, String cid) {
        return chatQuestion(bookName + "," + question, cid);
    }

    private Map generateHttpRequest(String url, Map params) {
        Map<String, Object> headparams = new HashMap<>();
        HttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/json;charset=utf-8");
        headparams.put("appid", appid);
        String sign = appid + appsecret;

        String json = JSON.toJSONString(params);
        sign = sign + json;
        headparams.put("sign", EncryptionUtils.getSHA256Code(sign)); // sha256加密
        for (Map.Entry<String, Object> entry : headparams.entrySet()) {
            httpPost.setHeader(entry.getKey(), entry.getValue().toString());
        }
        StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
        httpPost.setEntity(entity);
        Map map = null;
        try {
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            if (responseEntity != null) {
                String answerUTF8 = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                log.info("chatGPT回答结果:" + answerUTF8);
                map = JSONObject.parseObject(answerUTF8, Map.class);
            }
        } catch (IOException e) {
            log.error("chatGPT回答失败");
            e.printStackTrace();
        }
        if (map == null || map.get("code") == null)//没有获得请求结果，或者没有code
            throw new ServiceException("机器人超载冒烟了，请稍后再试");
        else {
            if ((Integer) map.get("code") != 200)//code不为200，说明出错了
                throw new ServiceException((String) map.get("msg"));
        }
        return map;
    }

    //对话，传入最新的问题和对话id
    public String chatQuestion(String question, String cid) throws ServiceException {
        Map params = new HashMap();
        params.put("question", question);
        params.put("cid", cid);
        params.put("type", "分级阅读");
        Map response = generateHttpRequest(address, params);
        return (String) response.get("content");
    }

    //删除某个对话id的对话历史
    public void clearHistory(String cid) {
        Map params = new HashMap();
        params.put("cid", cid);
        params.put("type", "分级阅读");
        generateHttpRequest(address + "/clearHistory", params);
    }
}
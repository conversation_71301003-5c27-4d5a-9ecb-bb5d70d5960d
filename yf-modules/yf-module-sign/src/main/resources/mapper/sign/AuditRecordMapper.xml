<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yf.sign.mapper.AuditRecordMapper">

    <resultMap id="BaseResultMap" type="com.yf.sign.entity.AuditRecord" >
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="type" property="type" />
        <result column="source" property="source" />
        <result column="reason" property="reason" typeHandler="com.yf.sign.utils.JsonStringArrayTypeHandler"/>
        <result column="pass" property="pass" />
        <result column="completed" property="completed" />
        <result column="create_date" property="createDate" />
        <result column="text" property="text" />
        <result column="url" property="url" />


    </resultMap>
    <sql id="Base_Column_List" >
        a.id,a.user_id,a.type,a.source,a.reason,a.pass,a.completed,a.create_date,a.text,a.url
    </sql>

    <select id="auditRecordPage" resultMap="BaseResultMap">
        select b.nickname as nickname,b.mobile as phone,
               <include refid="Base_Column_List" />
               from audit_record a left join sys_user b on a.user_id=b.id
        <where>
            <if test="req.pass!=null ">
                and a.pass=#{req.pass}
            </if>
            <if test="req.completed!=null ">
                and a.completed=#{req.completed}
            </if>
            <if test="req.type!=null and req.type != '' ">
                and  a.type = #{req.type}
            </if>
            <if test ="req.source!=null and req.source != '' ">
                and  a.source = #{req.source}
            </if>
            <if test="req.name!=null and req.name != '' ">
                and  (b.nickname like concat('%',#{req.name},'%') or b.mobile like concat('%',#{req.name},'%'))
            </if>
            <if test="req.reason!=null and req.reason != '' ">
                and  JSON_CONTAINS(a.reason,concat('"',#{req.reason},'"') )
            </if>

        </where>
        order by a.create_date desc
    </select>

</mapper>
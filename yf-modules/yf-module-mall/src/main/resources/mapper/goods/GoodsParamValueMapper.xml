<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.mall.modules.goods.mapper.GoodsParamValueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.mall.modules.goods.entity.GoodsParamValue">
        <id column="id" property="id" />
        <result column="goods_id" property="goodsId" />
        <result column="param_id" property="paramId" />
        <result column="value" property="value" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`goods_id`,`param_id`,`value`,`sort`
    </sql>

</mapper>

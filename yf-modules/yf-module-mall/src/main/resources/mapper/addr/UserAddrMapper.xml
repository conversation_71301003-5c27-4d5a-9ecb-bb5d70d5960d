<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.mall.modules.addr.mapper.UserAddrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.mall.modules.addr.entity.UserAddr">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="contact" property="contact" />
        <result column="mobile" property="mobile" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="area" property="area" />
        <result column="address" property="address" />
        <result column="is_default" property="isDefault" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`user_id`,`contact`,`mobile`,`province`,`city`,`area`,`address`,`is_default`,`create_time`,`update_time`
    </sql>

</mapper>

package com.yf.mall.modules.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.mall.modules.goods.dto.response.GoodsParamExtDTO;
import com.yf.mall.modules.goods.entity.GoodsParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品属性Mapper
 * </p>
 * <AUTHOR>
 * @since 2020-03-12 10:10
 */
public interface GoodsParamMapper extends BaseMapper<GoodsParam> {

    /**
     * 根据商品获得商品列表内容
     * @param goodsId
     * @return
     */
    List<GoodsParamExtDTO> listByGoods(@Param("goodsId") String goodsId);
}

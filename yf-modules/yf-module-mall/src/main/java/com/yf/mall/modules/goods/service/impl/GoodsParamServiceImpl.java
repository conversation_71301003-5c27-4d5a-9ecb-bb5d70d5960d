package com.yf.mall.modules.goods.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.mall.modules.goods.dto.response.GoodsParamExtDTO;
import com.yf.mall.modules.goods.entity.GoodsParam;
import com.yf.mall.modules.goods.mapper.GoodsParamMapper;
import com.yf.mall.modules.goods.service.GoodsParamService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 语言设置 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2020-03-12 10:10
 */
@Service
public class GoodsParamServiceImpl extends ServiceImpl<GoodsParamMapper, GoodsParam> implements GoodsParamService {

    @Override
    public List<GoodsParamExtDTO> listByGoods(String goodsId) {
        return baseMapper.listByGoods(goodsId);
    }

    @Override
    public Map<String, String> findVariantMap(boolean insert, String spuId) {

        //返回结果，名称--ID
        Map<String, String> map = new HashMap<>(16);

        //添加直接返回
        if (insert) {
            return map;
        }
        QueryWrapper<GoodsParam> wrapper = new QueryWrapper();
        wrapper.lambda().eq(GoodsParam::getGoodsId, spuId);
        List<GoodsParam> list = this.list(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            for (GoodsParam item : list) {
                map.put(item.getTitle(), item.getId());
            }
        }
        return map;
    }

}

package com.yf.mall.modules.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.mall.modules.goods.dto.request.GoodsSkuSaveDTO;
import com.yf.mall.modules.goods.entity.GoodsSku;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品规格Mapper
 * </p>
 * <AUTHOR>
 * @since 2020-03-12 10:10
 */
public interface GoodsSkuMapper extends BaseMapper<GoodsSku> {

    /**
     * 查找完整的SKU列表，包含属性值内容等
     * @param goodsId
     * @return
     */
    List<GoodsSkuSaveDTO> findFullSkuList(@Param("goodsId") String goodsId);
}

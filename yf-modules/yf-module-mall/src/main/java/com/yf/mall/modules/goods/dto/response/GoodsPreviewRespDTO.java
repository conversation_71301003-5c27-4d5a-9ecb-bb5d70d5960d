package com.yf.mall.modules.goods.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yf.base.api.api.ApiError;
import com.yf.base.utils.DecimalUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 商品订单预览响应类
 * </p>
 * <AUTHOR>
 * @since 2020-03-12 10:10
 */
@Data
@ApiModel(value = "商品订单预览响应类", description = "商品订单预览响应类")
public class GoodsPreviewRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "商品ID", required = true)
    private String goodsId;

    @ApiModelProperty(value = "规格ID", required = true)
    private String skuId;

    @ApiModelProperty(value = "SKU标题", required = true)
    private String skuTitle;

    @ApiModelProperty(value = "商品名称", required = true)
    private String goodsTitle;

    @ApiModelProperty(value = "封面图片", required = true)
    private String goodsImage;

    @ApiModelProperty(value = "商品类型", required = true)
    private String extraType;

    @ApiModelProperty(value = "销售价", required = true)
    private BigDecimal salePrice;

    @ApiModelProperty(value = "积分", required = true)
    private Integer costPoints;

    @ApiModelProperty(value = "购买数量", required = true)
    private Integer buyCount;

    /**
     * 商品状态
     */
    @JsonIgnore
    private Integer goodsState;

    /**
     * 商品库存
     */
    @JsonIgnore
    private Integer leftStock;


    /**
     * 获取商品总价
     * @return
     */
    @ApiModelProperty(value = "商品总价格", required = true)
    public BigDecimal getTotalAmount() {
        return DecimalUtils.multiply(salePrice, new BigDecimal(buyCount));
    }


    /**
     * 获得总消耗积分
     * @return
     */
    @ApiModelProperty(value = "总消耗积分", required = true)
    public Integer getTotalPoints() {
        return this.costPoints * this.buyCount;
    }

    @ApiModelProperty(value = "购买校验信息", required = true)
    public VerifyData getBuyVerify() {

        VerifyData verify = new VerifyData();

        if (this.buyCount > this.leftStock) {
            verify.setVerify(false);
            verify.setMsg(ApiError.ERROR_40000002.msg);
            verify.setApiError(ApiError.ERROR_40000002);
            return verify;
        }

        if (!goodsState.equals(0)) {
            verify.setVerify(false);
            verify.setMsg(ApiError.ERROR_40000003.msg);
            verify.setApiError(ApiError.ERROR_40000003);
            return verify;
        }

        verify.setVerify(true);
        verify.setMsg("可继续购买");
        return verify;
    }

    @Data
    @ApiModel(value = "购买校验信息", description = "购买校验信息")
    public static class VerifyData {
        @ApiModelProperty(value = "是否可购买", required = true)
        private Boolean verify;

        @ApiModelProperty(value = "购买提示，如：库存不足，已下架等", required = true)
        private String msg;

        @JsonIgnore
        private ApiError apiError;
    }

}

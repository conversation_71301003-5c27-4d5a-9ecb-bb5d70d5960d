package com.yf.mall.modules.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.mall.modules.goods.dto.request.GoodsPagingReqDTO;
import com.yf.mall.modules.goods.dto.response.GoodsListRespDTO;
import com.yf.mall.modules.goods.dto.response.GoodsPreviewRespDTO;
import com.yf.mall.modules.goods.entity.Goods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商城商品Mapper
 * </p>
 * <AUTHOR>
 * @since 2020-03-12 10:10
 */
public interface GoodsMapper extends BaseMapper<Goods> {

    /**
     * 普通的分类响应
     * @param page
     * @param reqDTO
     * @return
     */
    IPage<GoodsListRespDTO> paging(Page page, @Param("params") GoodsPagingReqDTO reqDTO);


    /**
     * 查找商品用于下单
     * @param skuIds
     * @return
     */
    List<GoodsPreviewRespDTO> listForOrder(@Param("skuIds") List<String> skuIds);


    /**
     * 查找购物车列表
     * @param userId
     * @return
     */
    List<GoodsPreviewRespDTO> listForCart(@Param("userId") String userId);


}

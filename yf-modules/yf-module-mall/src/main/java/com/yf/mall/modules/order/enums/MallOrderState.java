package com.yf.mall.modules.order.enums;

/**
 * 订单状态
 * <AUTHOR>
 * @date 2020-04-05 10:59
 */
public interface MallOrderState {

    /**
     * 待支付
     */
    Integer PREPARE = 0;

    /**
     * 待发货
     */
    Integer WAIT_SEND = 1;

    /**
     * 待收货
     */
    Integer WAIT_RECEIVE = 2;

    /**
     * 待评价
     */
    Integer WAIT_COMMENT = 3;

    /**
     * 已完成
     */
    Integer FINISHED = 4;

    /**
     * 已取消
     */
    Integer CANCELED = 100;

    /**
     * 退货中
     */
    Integer REFOUNDING = 101;

    /**
     * 已退货
     */
    Integer REFOUNDED = 102;


}

package com.yf.mall.modules.order.dto.response;

import com.yf.mall.modules.order.dto.OrderDTO;
import com.yf.mall.modules.order.dto.OrderDeliveryDTO;
import com.yf.mall.modules.order.dto.OrderGoodsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 订单信息请求类
 * </p>
 * <AUTHOR>
 * @since 2020-03-12 12:44
 */
@Data
@ApiModel(value = "订单详情响应类", description = "订单详情响应类")
public class OrderDetailRespDTO extends OrderDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品列表", required = true)
    private List<OrderGoodsDTO> goodsList;

    @ApiModelProperty(value = "物流信息", required = true)
    private OrderDeliveryDTO delivery;

    @ApiModelProperty(value = "订单过期剩余秒数", required = true)
    public Long getExpireSeconds() {

        if (getExpireTime() == null) {
            return 0L;
        }

        Long current = System.currentTimeMillis();
        Long expire = getExpireTime().getTime();
        if (expire < current) {
            return 0L;
        }

        return (expire - current) / 1000;
    }
}

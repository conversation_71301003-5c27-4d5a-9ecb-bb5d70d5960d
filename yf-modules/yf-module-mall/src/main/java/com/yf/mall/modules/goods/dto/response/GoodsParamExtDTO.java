package com.yf.mall.modules.goods.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 商品属性扩展类
 * </p>
 * <AUTHOR>
 * @since 2020-03-12 10:10
 */
@Data
@ApiModel(value = "商品属性扩展类", description = "商品属性扩展类")
public class GoodsParamExtDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "序号", required = true)
    private String id;

    @ApiModelProperty(value = "属性名", required = true)
    private String title;

    @ApiModelProperty(value = "属性值列表", required = true)
    private List<GoodsParamValueExtDTO> values;

}

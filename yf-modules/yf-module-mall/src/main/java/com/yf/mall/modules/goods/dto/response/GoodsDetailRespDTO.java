package com.yf.mall.modules.goods.dto.response;

import com.yf.mall.modules.goods.dto.GoodsSkuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 商品详情响应类
 * </p>
 * <AUTHOR>
 * @since 2020-03-12 10:10
 */
@Data
@ApiModel(value = "商品详情响应类", description = "商品详情响应类")
public class GoodsDetailRespDTO extends GoodsListRespDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "多SKU，如果=0则可以直接购买，无需选择属性", required = true)
    private Boolean multiSku;

    @ApiModelProperty(value = "商品详情，图文HTML", required = true)
    private String content;

    @ApiModelProperty(value = "商品图片列表", required = true)
    private List<String> images;

    @ApiModelProperty(value = "商品属性值表", required = true)
    private List<GoodsParamExtDTO> params;


    @ApiModelProperty(value = "默认商品", required = true)
    private GoodsSkuDTO defaultSku;
}

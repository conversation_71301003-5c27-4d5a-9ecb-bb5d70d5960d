<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.exam.mapper.ExamVenueMapper">

    <select id="paging" resultType="com.yf.exam.modules.exam.dto.ExamVenueDTO">
        select * from el_exam_venue
        <where>
        pid = '0' and is_deleted = 0
            <if test="query.name != null and query.name != ''">
                AND `name` LIKE CONCAT('%',#{query.name},'%')
            </if>
        </where>
        ORDER BY id DESC
    </select>
</mapper>

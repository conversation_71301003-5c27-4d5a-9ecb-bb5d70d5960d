package com.yf.exam.modules.paper.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.exam.modules.paper.dto.PaperDTO;
import com.yf.exam.modules.paper.dto.PaperQuDTO;
import com.yf.exam.modules.paper.dto.ext.PaperQuDetailDTO;
import com.yf.exam.modules.paper.dto.request.PaperListReqDTO;
import com.yf.exam.modules.paper.dto.request.PaperMarkReqDTO;
import com.yf.exam.modules.paper.dto.response.*;
import com.yf.exam.modules.paper.entity.Paper;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 试卷业务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-25 16:33
 */
public interface PaperService extends IService<Paper> {

    /**
     * 创建试卷
     * @param userId
     * @param departCode
     * @param examId
     * @param password
     * @param courseId
     * @param activityId
     * @return
     */
    String createPaper(String userId, String departCode, String examId, String password, String courseId, String activityId);


    @Transactional(rollbackFor = Exception.class)
    String batchCreatePaper(List<String> examIds);

    /**
     * 查找详情
     * @param paperId
     * @return
     */
    ExamDetailRespDTO paperDetail(String paperId);


    /**
     * 考试结果
     * @param paperId
     * @return
     */
    ExamResultRespDTO paperResult(String paperId);

    /**
     * 查找题目详情
     * @param paperId
     * @param quId
     * @return
     */
    PaperQuDetailDTO findQuDetail(String paperId, String quId);

    /**
     * 填充答案
     * @param reqDTO
     * @return
     */
    FillRespDTO fillAnswer(PaperQuDetailDTO reqDTO);

    /**
     * 交卷操作
     * @param paperId
     * @param exState @see com.yf.exam.enums.PaperExState
     * @param message
     */
    void handExam(String paperId, Integer exState, String message);

    /**
     * 阅卷完成
     * @param reqDTO
     */
    void reviewPaper(ExamResultRespDTO reqDTO);


    /**
     * 试卷列表响应类
     * @param reqDTO
     * @return
     */
    IPage<PaperListRespDTO> paging(PagingReqDTO<PaperListReqDTO> reqDTO);

    /**
     * 试卷列表响应类
     * @param reqDTO
     * @return
     */
    IPage<PaperListRespDTO> myPaging(PagingReqDTO<PaperListReqDTO> reqDTO);


    /**
     * 检测是否有进行中的考试
     * @param userId
     * @param examId
     * @return
     */
    PaperDTO checkProcess(String userId,String examId);


    /**
     * 作弊离开检测
     * @param id
     * @return
     */
    LeaveCheckRespDTO leave(String id);

    /**
     * 统计正在考试的数量
     * @param examId
     * @return
     */
    int countProcess(String examId);

    /**
     * 统计正在考试的数量，根据试卷ID
     * @param tmplId
     * @return
     */
    int countByTmpl(String tmplId);


    /**
     * 全部交卷
     * @param examId
     * @param exState
     * @param message
     */
    void handAll(String examId, Integer exState, String message);

    /**
     * 查找数据用于导出存档
     * @param examId
     * @param ids
     * @return
     */
    List<ExamResultRespDTO> exportZip(String examId, List<String> ids);


    /**
     * 标记成绩
     * @param reqDTO
     */
    void markAs(PaperMarkReqDTO reqDTO);

    /**
     * 删除考试记录
     * @param id
     */
    void delete(String id);

    /**
     * 保存试卷编程题
     * @param reqDTO
     */
    void answerCode(PaperQuDTO reqDTO);


    /**
     * 编程题阅卷
     * @param paperId
     * @return
     */
    BigDecimal judgeCode(String paperId) throws Exception;


    /**
     * 统计用户考试的次数
     * @param examId
     * @return
     */
    Integer countPaper(String examId);

    boolean checkRefCourseThrow(String courseId, String userId);

    boolean checkRefCourse(String courseId, String userId);

    /**
     * 切题
     * @param reqDTO
     * @return
     */
    void changeQu(PaperQuDetailDTO reqDTO);

    /**
     * 主系统导入脚本调用，更改数据库特定表url前缀
     * @param
     * @return
     */
    void updateDatabaseWithServerIp();
}

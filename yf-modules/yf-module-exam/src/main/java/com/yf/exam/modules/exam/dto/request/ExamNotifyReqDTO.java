package com.yf.exam.modules.exam.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 考试安排通知发送请求类
 * </p>
 * <AUTHOR>
 * @since 2020-07-25 16:18
 */
@Data
@ApiModel(value = "考试安排通知发送请求类", description = "考试安排通知发送请求类")
public class ExamNotifyReqDTO implements Serializable {


    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "考试ID", required = true)
    private String examId;

    @ApiModelProperty(value = "使用站内信", required = true)
    private Boolean useIm;

    @ApiModelProperty(value = "使用短信", required = true)
    private Boolean useSms;

    @ApiModelProperty(value = "使用邮件", required = true)
    private Boolean useEmail;

    @ApiModelProperty(value = "1为全部2为未考试", required = true)
    private Integer scope;

    @ApiModelProperty(value = "定时发送时间", required = true)
    private Date sendTime;

    @ApiModelProperty(value = "是否立即发送", required = true)
    private String trigger;

}

package com.yf.exam.modules.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.base.api.api.dto.BaseIdsReqDTO;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.exam.modules.exam.dto.request.ExamPersonReqDTO;
import com.yf.exam.modules.exam.entity.ExamPerson;
import com.yf.exam.modules.exam.mapper.ExamPersonMapper;
import com.yf.exam.modules.exam.service.ExamPersonService;
import com.yf.system.modules.user.dto.SysUserDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 定员考试业务实现类
 * </p>
 * <AUTHOR>
 * @since 2020-09-03 17:24
 */
@Service
public class ExamPersonServiceImpl extends ServiceImpl<ExamPersonMapper, ExamPerson> implements ExamPersonService {


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> join(BaseIdsReqDTO reqDTO) {

        // 如果已经存在了，则不重复添加
        QueryWrapper<ExamPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(ExamPerson::getExamId, reqDTO.getRefId());
        List<ExamPerson> allList = this.list(wrapper);

        // 全部ID
        List<String> allIds = new ArrayList<>();
        // 要增加的
        List<String> addIds = reqDTO.getIds();
        // 加入数据为空
        if (CollectionUtils.isEmpty(addIds)) {
            addIds = new ArrayList<>();
        }

        if (!CollectionUtils.isEmpty(allList)) {
            for (ExamPerson item : allList) {
                // 现有的全部列表
                allIds.add(item.getUserId());
                // 移除已有的
                addIds.remove(item.getUserId());
            }
        }

        if (CollectionUtils.isEmpty(addIds)) {
            return allIds;
        }

        List<ExamPerson> list = new ArrayList<>();
        for (String id : addIds) {
            ExamPerson person = new ExamPerson();
            person.setExamId(reqDTO.getRefId());
            person.setUserId(id);
            list.add(person);

            // 增加范湖数据
            allIds.add(id);
        }

        this.saveBatch(list);

        return allIds;
    }

    @Override
    public void remove(BaseIdsReqDTO reqDTO) {

        // 移除数据
        QueryWrapper<ExamPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(ExamPerson::getUserId, reqDTO.getIds())
                .eq(ExamPerson::getExamId, reqDTO.getRefId());
        this.remove(wrapper);
    }

    @Override
    public IPage<SysUserDTO> paging(PagingReqDTO<ExamPersonReqDTO> reqDTO) {
        return baseMapper.paging(reqDTO.toPage(), reqDTO.getParams());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modifyAll(String tempId, String prodId) {

        // 无需操作
        if (!tempId.startsWith("temp_")) {
            return;
        }

        // 修改引用关系
        QueryWrapper<ExamPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ExamPerson::getExamId, tempId);

        ExamPerson person = new ExamPerson();
        person.setExamId(prodId);
        this.update(person, wrapper);
    }

    @Override
    public void removeByUserIds(List<String> ids) {
        QueryWrapper<ExamPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ExamPerson::getUserId, ids);
        this.remove(wrapper);
    }

    @Override
    public List<String> listUserIdByExam(String examId, Integer scope) {

        return baseMapper.listUserIdByExam(examId, scope);
    }

    @Override
    public boolean inList(String examId, String userId) {

        QueryWrapper<ExamPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(ExamPerson::getExamId, examId)
                .eq(ExamPerson::getUserId, userId);
        return this.count(wrapper) > 0;
    }

    /**
     * 查找已存在的用户表
     * @param examId
     * @return
     */
    private List<String> exists(String examId) {
        QueryWrapper<ExamPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ExamPerson::getExamId, examId);
        List<ExamPerson> list = this.list(wrapper);
        List<String> ids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (ExamPerson person : list) {
                ids.add(person.getUserId());
            }
        }
        return ids;
    }
}

package com.yf.exam.job;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yf.base.utils.jackson.JsonHelper;
import com.yf.exam.modules.exam.dto.ExamApplyDTO;
import com.yf.exam.modules.exam.entity.Exam;
import com.yf.exam.modules.exam.mapper.ExamMapper;
import com.yf.exam.modules.exam.service.ExamService;
import com.yf.job.service.JobService;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 考试异步生成考情分析任务
 * <AUTHOR>
 */
@Log4j2
@Component
public class ExamBatchCreateReportJob implements Job {

    @Autowired
    private ExamService examService;

    @Autowired
    private ExamMapper examMapper;

    @SneakyThrows
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        String group = detail.getKey().getGroup();
        String data = String.valueOf(detail.getJobDataMap().get(JobService.TASK_DATA));

        log.info("++++++++++异步任务：生成考情分析");
        log.info("++++++++++jobName:{}", name);
        log.info("++++++++++jobGroup:{}", group);
        log.info("++++++++++taskData:{}", data);

        ExamApplyDTO dto = JsonHelper.parseObject(data, ExamApplyDTO.class);

        try {
            examService.createReportTask(dto.getExamIds());
        } catch (Exception e) {
            e.printStackTrace();
            LambdaUpdateWrapper<Exam> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(Exam::getId, dto.getExamIds());
            updateWrapper.set(Exam::getReportStatus, 3);
            examMapper.update(null, updateWrapper);
        }
    }



}

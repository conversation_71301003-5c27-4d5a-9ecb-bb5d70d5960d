package com.yf.exam.modules.tmpl.dto.ext;

import com.yf.exam.modules.tmpl.dto.TmplQuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 试卷题目数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-04-25 13:59
 */
@Data
@ApiModel(value = "子题目扩展类", description = "子题目扩展类")
public class TmplSubDetailDTO extends TmplQuDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "题目内容", required = true)
    private String content;
}

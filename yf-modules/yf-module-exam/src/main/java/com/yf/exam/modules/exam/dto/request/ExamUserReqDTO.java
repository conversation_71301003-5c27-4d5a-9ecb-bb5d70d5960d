package com.yf.exam.modules.exam.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 考试用户数据传输类
 * </p>
 * <AUTHOR>
 * @since 2020-07-25 16:18
 */
@Data
@ApiModel(value = "考试用户请求类", description = "考试用户请求类")
public class ExamUserReqDTO {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "考试id", required = true)
    private String examId;

    @ApiModelProperty(value = "用户ids")
    private List<String> userIds;


    @ApiModelProperty(value = "用户id")
    private String userId;


    @ApiModelProperty(value = "座位号")
    private Integer seatNumber;

    @ApiModelProperty(value = "考点id", required = true)
    private String examVenueId;

    @ApiModelProperty(value = "考场id", required = true)
    private String examVenueChildId;

}

package com.yf.exam.modules.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.modules.exam.entity.ExamActivity;
import com.yf.exam.modules.exam.mapper.ExamActivityMapper;
import com.yf.exam.modules.exam.service.ExamActivityService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 活动考试业务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-05-11 11:11
 */
@Service
public class ExamActivityServiceImpl extends ServiceImpl<ExamActivityMapper, ExamActivity> implements ExamActivityService {

    @Override
    public void merge(String userId, String activityId, String examId) {

        if (this.exists(userId, activityId, examId)) {
            return;
        }
        ExamActivity ca = new ExamActivity();
        ca.setActivityId(activityId);
        ca.setUserId(userId);
        ca.setExamId(examId);
        ca.setCreateTime(new Date());
        this.save(ca);

    }

    @Override
    public boolean exists(String userId, String activityId, String examId) {
        QueryWrapper<ExamActivity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(ExamActivity::getUserId, userId)
                .eq(ExamActivity::getExamId, examId)
                .eq(ExamActivity::getActivityId, activityId);
        return this.count(wrapper) > 0;
    }
}

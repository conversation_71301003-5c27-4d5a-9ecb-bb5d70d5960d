package com.yf.exam.modules.exam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 考题目目数据传输类
 * </p>
 * <AUTHOR>
 * @since 2020-09-05 11:14
 */
@Data
@ApiModel(value = "考题目目", description = "考题目目")
public class ExamQuDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @ApiModelProperty(value = "考试ID", required = true)
    private String examId;

    @ApiModelProperty(value = "题目ID", required = true)
    private String quId;

    @ApiModelProperty(value = "题目类型", required = true)
    private String quType;

    @ApiModelProperty(value = "分数", required = true)
    private BigDecimal score;

    @ApiModelProperty(value = "排序", required = true)
    private Integer sort;

}

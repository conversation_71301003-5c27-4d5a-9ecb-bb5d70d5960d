package com.yf.exam.modules.cdkey.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yf.ability.Constant;
import com.yf.ability.excel.ExportExcel;
import com.yf.ability.redis.service.RedisService;
import com.yf.ability.shiro.dto.SysUserLoginDTO;
import com.yf.ability.word.utils.WordUtil;
import com.yf.base.api.api.ApiError;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.modules.cdkey.dto.RedeemCodeDTO;
import com.yf.base.modules.cdkey.dto.SapCodeExportDTO;
import com.yf.base.modules.cdkey.dto.request.RedeemCodeReqDTO;
import com.yf.base.modules.cdkey.dto.response.*;
import com.yf.base.modules.cdkey.entity.RedeemCode;
import com.yf.base.modules.cdkey.mapper.RedeemCodeMapper;
import com.yf.base.modules.xkorder.entity.AccountDateRecord;
import com.yf.base.modules.xkorder.mapper.AccountDateRecordMapper;
import com.yf.base.utils.BeanMapper;
import com.yf.base.utils.jackson.JsonHelper;
import com.yf.course.modules.course.entity.Course;
import com.yf.course.modules.course.service.CourseService;
import com.yf.exam.modules.cdkey.service.RedeemCodeService;
import com.yf.exam.modules.exam.entity.ExamCategory;
import com.yf.exam.modules.exam.service.ExamCategoryService;
import com.yf.mall.modules.goods.entity.Goods;
import com.yf.mall.modules.goods.entity.GoodsSku;
import com.yf.mall.modules.goods.service.GoodsService;
import com.yf.mall.modules.goods.service.GoodsSkuService;
import com.yf.notify.modules.notify.service.MsgService;
import com.yf.system.modules.dict.dto.ext.DicValueTreeDTO;
import com.yf.system.modules.dict.entity.SysDicValue;
import com.yf.system.modules.dict.service.SysDicValueService;
import com.yf.system.modules.user.UserUtils;
import com.yf.system.modules.user.entity.SysRole;
import com.yf.system.modules.user.entity.SysUser;
import com.yf.system.modules.user.enums.SysRoleId;
import com.yf.system.modules.user.service.SysUserRoleService;
import com.yf.system.modules.user.service.SysUserService;
import com.yf.system.modules.user.utils.StrChineseToEnglishUtils;
import com.yf.system.utils.CopyUtils;
import com.yf.system.utils.DateUtils;
import com.yf.system.utils.RandomCodeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/***
 * @title RedeemCodeServiceImpl
 * @Description
 * <AUTHOR>
 * @create 2024/1/17 17:06
 **/
@Service
public class RedeemCodeServiceImpl extends ServiceImpl<RedeemCodeMapper, RedeemCode> implements RedeemCodeService {


    @Autowired
    private GoodsSkuService goodsSkuService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private SysUserService sysUserService;


    @Autowired
    private CourseService courseService;

    @Autowired
    private ExamCategoryService examCategoryService;

    @Autowired
    private SysDicValueService sysDicValueService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AccountDateRecordMapper accountDateRecordMapper;

    @Autowired
    private MsgService msgService;

    @Resource
    private WordUtil wordUtil;

    @Autowired
    private ExportExcel exportExcel;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRedeemCode(RedeemCodeDTO reqDTO) {
        Integer type = reqDTO.getType();
        if (type == null) {
            type = 2;
        }
        String title = "";
        BigDecimal price = reqDTO.getSalePrice();
        if (type == 1 || type == 2) {
            GoodsSku goodsSku = goodsSkuService.getById(reqDTO.getSkuId());
            Goods goods = goodsService.getById(goodsSku.getGoodsId());
            title = goods.getTitle();
            Integer costPoints = goodsSku.getCostPoints();
            if (costPoints == 0) {
                title = title + goodsSku.getTitle();
                type = 1;
            }
            if (price == null || price.compareTo(BigDecimal.ZERO) == 0){
                price = goodsSku.getSalePrice();
            }
        } else if (type == 3) {
            //课程兑换券
            String courseId = reqDTO.getCourseId();
            Course course = courseService.getById(courseId);
            title = course.getTitle();
            if (price == null || price.compareTo(BigDecimal.ZERO) == 0){
                price = course.getPrice();
            }
        } else if (type == 4) {
            //考试兑换券
            ExamCategory examCategory = examCategoryService.getById(reqDTO.getExamCatId());
            title = examCategory.getName();
            if (price == null || price.compareTo(BigDecimal.ZERO) == 0){
                price = examCategory.getPrice();
            }
        } else if (type == 5) {
            //课考兑换券
            String courseId = reqDTO.getCourseId();
            Course course = courseService.getById(courseId);
            String examCatId = course.getExamCatId();
            ExamCategory examCategory = examCategoryService.getById(examCatId);
            title = course.getTitle() + "课考联报";
            if (price == null || price.compareTo(BigDecimal.ZERO) == 0) {
                BigDecimal coursePrice = course.getPrice();
                BigDecimal examCategoryPrice = examCategory.getPrice();
                BigDecimal uniteDiscounts = course.getUniteDiscounts();
                if (uniteDiscounts == null) {
                    uniteDiscounts = BigDecimal.ZERO;
                }
                BigDecimal subtract = coursePrice.add(examCategoryPrice).subtract(uniteDiscounts);
                price = subtract;
            }
        }
        if (StringUtils.isNotBlank(reqDTO.getId())) {
            String id = reqDTO.getId();
            RedeemCode redeemCode = baseMapper.selectById(id);
            BeanMapper.copy(reqDTO, redeemCode);
            redeemCode.setTitle(title);
            redeemCode.setSalePrice(price);
            redeemCode.setType(type);
            baseMapper.updateById(redeemCode);
        } else {
            RedeemCode redeemCode = new RedeemCode();
            BeanMapper.copy(reqDTO, redeemCode);
            redeemCode.setPid("0");
            redeemCode.setTitle(title);
            redeemCode.setUsedNum(0);
            redeemCode.setGetNum(0);
            redeemCode.setState(0);
            redeemCode.setType(type);
            redeemCode.setSalePrice(price);
            baseMapper.insert(redeemCode);

        }

    }

    @Override
    public List<RedeemCode> generateRedeemCode(RedeemCodeReqDTO reqDTO) {
        String id = reqDTO.getId();
        RedeemCode selectById = baseMapper.selectById(id);
        String initialCode = "";
        if (StringUtils.isNotBlank(selectById.getCode())){
            initialCode = selectById.getCode();
        }
        List<String> gradeValueIds = selectById.getGradeValueIds();
        Integer codeCount = reqDTO.getCodeCount();
        List<RedeemCode> redeemCodes = new ArrayList<>();
        List<String> userIds = reqDTO.getUserIds();
        if (Objects.nonNull(userIds) && userIds.size() > 0){
            Date date = new Date();
            for (int i = 0; i < userIds.size(); i++) {
                String userId = userIds.get(i);
                RedeemCode redeemCode = new RedeemCode();
                CopyUtils.copyPropertiesIgnoreNull(selectById, redeemCode);
                redeemCode.setId(null);
                if (Objects.nonNull(gradeValueIds) && gradeValueIds.size() > 0) {
                    redeemCode.setGradeValueIds(gradeValueIds);
                }
                redeemCode.setPid(id);
                redeemCode.setCode(RandomCodeUtils.getCode(5)+"-"+RandomCodeUtils.getCode(5)+"-"+RandomCodeUtils.getCode(5));
                redeemCode.setUserId(userId);
                redeemCode.setGetTime(date);
                redeemCode.setUseTime(date);
                redeemCode.setState(1);
                //baseMapper.insert(redeemCode);
                redeemCodes.add(redeemCode);
            }
        }else {
            for (int i = 0; i < codeCount; i++) {
                RedeemCode redeemCode = new RedeemCode();
                CopyUtils.copyPropertiesIgnoreNull(selectById, redeemCode);
                redeemCode.setId(null);
                if (Objects.nonNull(gradeValueIds) && gradeValueIds.size() > 0) {
                    redeemCode.setGradeValueIds(gradeValueIds);
                }
                redeemCode.setPid(id);
                redeemCode.setCode(RandomCodeUtils.getCode(5)+"-"+RandomCodeUtils.getCode(5)+"-"+RandomCodeUtils.getCode(5));
                //baseMapper.insert(redeemCode);
                redeemCodes.add(redeemCode);
            }

        }
        if (redeemCodes.size() > 0){
            this.saveBatch(redeemCodes);
        }
        return redeemCodes;
    }

    @Override
    public IPage<RedeemCodeDTO> paging(PagingReqDTO<RedeemCodeDTO> reqDTO) {
        // 创建分页对象
        Page query = reqDTO.toPage();
        //查询条件
        QueryWrapper<RedeemCode> wrapper = new QueryWrapper<>();
        // 请求参数
        RedeemCodeDTO params = reqDTO.getParams();
        wrapper.lambda().eq(RedeemCode::getPid, "0");
        if (params != null) {
            if (StringUtils.isNotBlank(params.getId())) {
                wrapper.lambda().eq(RedeemCode::getId, params.getId());
            }
            if (params.getType() != null) {
                wrapper.lambda().eq(RedeemCode::getType, params.getType());
            }
            if (StringUtils.isNotBlank(params.getSapOrderId())) {
                wrapper.lambda().like(RedeemCode::getSapOrderId, params.getSapOrderId());
            }
            if (StringUtils.isNotBlank(params.getTitle())) {
                wrapper.lambda().like(RedeemCode::getTitle, params.getTitle());
            }
            if (StringUtils.isNotBlank(params.getProvince())) {
                wrapper.lambda().eq(RedeemCode::getProvince, params.getProvince());
            }
            if (StringUtils.isNotBlank(params.getCity())) {
                wrapper.lambda().eq(RedeemCode::getCity, params.getCity());
            }
            if (params.getIsImport() != null) {
                wrapper.lambda().eq(RedeemCode::getIsImport, params.getIsImport());
            }else {
                wrapper.lambda().eq(RedeemCode::getIsImport, 0);
            }
        }
        // 按更新时间倒序
        wrapper.lambda().orderByDesc(RedeemCode::getCreateDate);

        //获得数据
        IPage<RedeemCode> page = this.page(query, wrapper);
        //转换结果
        IPage<RedeemCodeDTO> pageData = JsonHelper.parseObject(page, new TypeReference<Page<RedeemCodeDTO>>() {
        });
        List<RedeemCodeDTO> records = pageData.getRecords();
        Map<String, String> createNameMap = new HashMap<>();
        //创建人
        Set<String> createByIds = records.stream().map(RedeemCodeDTO::getCreateBy).collect(Collectors.toSet());
        if (createByIds.size() > 0){
            LambdaQueryWrapper<SysUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.select(SysUser::getId,SysUser::getRealName);
            userLambdaQueryWrapper.in(SysUser::getId, createByIds);
            List<SysUser> userList = sysUserService.list(userLambdaQueryWrapper);
            createNameMap = userList.stream()
                    .collect(Collectors.toMap(
                            SysUser::getId,
                            SysUser::getRealName,
                            (existingValue, newValue) -> existingValue
                    ));

        }
        // 1. 获取所有需要的SysDicValue对象，并映射到ID
        Map<String, SysDicValue> sysDicValueMap = new HashMap<>();
        for (RedeemCodeDTO record : records) {
            List<String> gradeValueIds = record.getGradeValueIds();
            if (gradeValueIds != null && !gradeValueIds.isEmpty()) {
                List<SysDicValue> sysDicValues = getAllSysDicValuesByIds(gradeValueIds);
                for (SysDicValue sysDicValue : sysDicValues) {
                    sysDicValueMap.put(sysDicValue.getId(), sysDicValue);
                }
            }
        }

        // 2. 缓存所有parentId对应的SysDicValue标题
        Map<String, String> parentTitleMap = new HashMap<>();
        for (SysDicValue sysDicValue : sysDicValueMap.values()) {
            if (sysDicValue.getParentId() != null) {
                parentTitleMap.put(sysDicValue.getParentId(), getSysDicValueTitleById(sysDicValue.getParentId()));
            }
        }

        // 3. 使用缓存的值来设置RedeemCodeDTO的内容
        for (RedeemCodeDTO record : records) {
            if (!record.getGradeValueIds().isEmpty() && record.getAllVolumes() == 0) {
                //去重
                List<String> list = record.getGradeValueIds().stream().distinct().collect(Collectors.toList());
                // 为每个record创建一个新的dtoList，仅包含当前record的gradeValueIds对应的SysDicValue
                List<DicValueTreeDTO> dtoList = list.stream()
                        // 使用Optional来避免可能的null值
                        .map(gradeValueId -> Optional.ofNullable(sysDicValueMap.get(gradeValueId)))
                        .filter(Optional::isPresent) // 过滤掉空的Optional对象
                        .map(Optional::get) // 获取实际的sysDicValue对象
                        .map(sysDicValue -> BeanMapper.map(sysDicValue, DicValueTreeDTO.class))
                        .collect(Collectors.toList());
                for (DicValueTreeDTO dicValueTreeDTO : dtoList) {
                    String parentId = dicValueTreeDTO.getParentId();
                    dicValueTreeDTO.setWholeName(parentTitleMap.get(parentId) + dicValueTreeDTO.getTitle());
                }
                record.setContent(dtoList.stream().map(DicValueTreeDTO::getWholeName).collect(Collectors.joining(",")));
            } else if (record.getAllVolumes() != null && record.getAllVolumes() == 1) {
                record.setContent("全年级");
            }
            String createBy = record.getCreateBy();
            if (StringUtils.isNotBlank(createBy)){
                record.setCreateByName(createNameMap.get(createBy));
            }
        }
        return pageData;
    }


    //获取SysDicValue列表
    private List<SysDicValue> getAllSysDicValuesByIds(List<String> gradeValueIds) {
        LambdaQueryWrapper<SysDicValue> sysDicValueLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysDicValueLambdaQueryWrapper.in(SysDicValue::getId, gradeValueIds);
        return sysDicValueService.list(sysDicValueLambdaQueryWrapper);
    }

    //获取SysDicValue的标题，这里使用parentId来获取
    private String getSysDicValueTitleById(String parentId) {
        return sysDicValueService.getById(parentId).getTitle();
    }

    @Override
    public void setOperationUser(RedeemCodeReqDTO reqDTO) {
        String id = reqDTO.getId();
        RedeemCode selectById = baseMapper.selectById(id);
        Integer codeCount = reqDTO.getCodeCount();
        String operationUserId = reqDTO.getOperationUserId();
        List<String> codeIds = reqDTO.getCodeIds();
        if (codeCount == null || codeCount == 0) {
            LambdaUpdateWrapper<RedeemCode> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(RedeemCode::getState, 0);
            updateWrapper.in(RedeemCode::getId, codeIds);
            updateWrapper.set(RedeemCode::getOperationUserId, operationUserId);
            baseMapper.update(null, updateWrapper);
        } else {
            for (int i = 0; i < codeCount; i++) {
                RedeemCode redeemCode = new RedeemCode();
                CopyUtils.copyPropertiesIgnoreNull(selectById, redeemCode);
                redeemCode.setId(null);
                redeemCode.setPid(id);
                String code = RandomCodeUtils.getCode(12);
                redeemCode.setCode(code);
                redeemCode.setOperationUserId(operationUserId);
                baseMapper.insert(redeemCode);
            }
        }
    }

    @Override
    public void grantRedeemCode(RedeemCodeReqDTO reqDTO) {

        String userId = UserUtils.getUserId();
        String codeId = reqDTO.getCodeId();
        RedeemCode redeemCode = baseMapper.selectById(codeId);
        redeemCode.setProvideUserId(userId);
        redeemCode.setUserId(reqDTO.getUserId());
        redeemCode.setGetTime(new Date());
        baseMapper.updateById(redeemCode);
        baseMapper.updateGetNum(redeemCode.getPid(), 1);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String grantNewRedeemCode(RedeemCodeReqDTO reqDTO) {
        String remark = reqDTO.getRemark();
        String provideUserId = UserUtils.getUserId();
        List<String> userIds = reqDTO.getUserIds();
        String id = reqDTO.getId();
        RedeemCode redeemCode = baseMapper.selectById(id);
        Integer maxGetCount = redeemCode.getMaxGetCount();
        StringBuffer result = new StringBuffer();
        result.append("保存成功！");
        List<String> alreadyExistedNames = new ArrayList<>();
        int num = 0;
        List<RedeemCode> redeemCodeList = new ArrayList<>();
        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            if (maxGetCount > 0) {
                QueryWrapper<RedeemCode> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("pid", id).eq("user_id", userId);
                Integer integer = baseMapper.selectCount(queryWrapper).intValue();
                if (integer >= maxGetCount) {
                    SysUser sysUser = sysUserService.getById(userId);
                    alreadyExistedNames.add(sysUser.getRealName());
                    continue;
                }
            }
            RedeemCode newRedeemCode = new RedeemCode();
            CopyUtils.copyPropertiesIgnoreNull(redeemCode, newRedeemCode);
            newRedeemCode.setId(null);
            newRedeemCode.setPid(id);
            String code = RandomCodeUtils.getCode(12);
            newRedeemCode.setCode(code);
            newRedeemCode.setProvideUserId(provideUserId);
            newRedeemCode.setGetTime(new Date());
            newRedeemCode.setUserId(userId);
            if (StringUtils.isNotBlank(remark)) {
                newRedeemCode.setRemark(remark);
            }
            baseMapper.insert(newRedeemCode);
            redeemCodeList.add(newRedeemCode);
            num++;
        }

        if (redeemCodeList.size() > 0) {
            baseMapper.updateGetNum(id, num);
        }

        if (alreadyExistedNames.size() != 0) {
            result.append("如下用户已经发过该兑换码：");
            for (int i = 0; i < alreadyExistedNames.size(); i++) {
                if (i != alreadyExistedNames.size() - 1) {
                    result.append(alreadyExistedNames.get(i) + "，");
                } else {
                    result.append(alreadyExistedNames.get(i) + "!");
                }
            }
            return result.toString();
        } else {
            return result.toString();
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exchange(String code,String school) {
        LambdaQueryWrapper<RedeemCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RedeemCode::getCode, code);
        RedeemCode redeemCode = baseMapper.selectOne(queryWrapper);
        Boolean updateExpired = false;
        if (redeemCode == null) {
            throw new ServiceException("激活码错误，请检查后操作");
        }
        String userId = redeemCode.getUserId();
        //指定人员的兑换码
        if (StringUtils.isNotBlank(userId) && !userId.equals(UserUtils.getUserId())) {
            throw new ServiceException("激活码错误，请检查后操作");
        }

        if (redeemCode.getState() == 1) {
            throw new ServiceException("激活码已使用");
        }

        //未指定人员的兑换码，兑换即赋值
        if (StringUtils.isBlank(userId)) {
            userId = UserUtils.getUserId();
        }

        Boolean isUpdate = false;
        LambdaUpdateWrapper<SysUser> sysUserLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sysUserLambdaUpdateWrapper.eq(SysUser::getId,userId);
        redeemCode.setSchool(school);
        //1-永久有效，2-指定日期 3-指定年限
        Integer accountType = redeemCode.getAccountType();
        AccountDateRecord accountDateRecord = new AccountDateRecord();
        accountDateRecord.setType(1);
        if (redeemCode.getUserType() == 1){
            accountDateRecord.setRoleId(SysRoleId.TEACHER);
        }else {
            accountDateRecord.setRoleId(SysRoleId.STUDENT);
        }

        accountDateRecord.setCode(redeemCode.getCode());
        accountDateRecord.setCodeId(redeemCode.getPid());
        accountDateRecord.setUserId(userId);
        accountDateRecord.setAccountType(accountType);
        if (accountType != 1 ) {
            // 设置账号有效期
            Date accountExpirationDate = redeemCode.getAccountExpirationDate();
            if (accountType == 3) {
                Integer yearCount = redeemCode.getYearCount();
                accountExpirationDate = DateUtils.getNextDay(new Date(), yearCount);
            }
            redeemCode.setAccountExpirationDate(accountExpirationDate);

            accountDateRecord.setExpireDate(accountExpirationDate);
        }
        accountDateRecordMapper.insert(accountDateRecord);

        redeemCode.setState(1);
        redeemCode.setUseTime(new Date());
        if (StringUtils.isBlank(redeemCode.getUserId())) {
            redeemCode.setUserId(userId);
            baseMapper.updateGetNum(redeemCode.getPid(), 1);
        }
        baseMapper.updateById(redeemCode);
        baseMapper.updateUseNum(redeemCode.getPid(), 1);

        List<String> userGradeValueIds = sysUserService.findGradeValueIds(userId,null,null);
        if (userGradeValueIds == null){
            userGradeValueIds = new ArrayList<>();
        }
        List<String> gradeValueIds = redeemCode.getGradeValueIds();
        userGradeValueIds.addAll(gradeValueIds);
        if (userGradeValueIds.size() > 0){
            List<String> collect = userGradeValueIds.stream().distinct().collect(Collectors.toList());
            // 使用Fastjson将List转换为JSON字符串
            String jsonOrderIds = JSON.toJSONString(collect);
            sysUserLambdaUpdateWrapper.set(SysUser::getGradeValueIds,jsonOrderIds);
            isUpdate = true;
            try {
                //更新缓存
                redisService.set(Constant.ACTIVATE_CODE+userId, JsonHelper.toJson(gradeValueIds), Constant.EXPIRE_TIME);
                redisService.deleteKeysByPrefix(Constant.PORTAL_TEA_RESOURCE +"login:"+userId+":");
                redisService.deleteKeysByPrefix(Constant.PORTAL_STU_RESOURCE +"login:"+userId+":");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (isUpdate){
            sysUserService.update(sysUserLambdaUpdateWrapper);
        }
    }

    @Override
    public IPage<RedeemCodeReqDTO> exchangePaging(PagingReqDTO<RedeemCodeDTO> reqDTO) {
        String userId = UserUtils.getUserId();
        // 创建分页对象
        Page query = reqDTO.toPage();
        //查询条件
        QueryWrapper<RedeemCode> wrapper = new QueryWrapper<>();
        // 请求参数
        RedeemCodeDTO params = reqDTO.getParams();
        wrapper.lambda().ne(RedeemCode::getPid, "0");
        wrapper.lambda().eq(RedeemCode::getUserId, userId);
        if (params != null) {
            if (params.getType() != null) {
                wrapper.lambda().eq(RedeemCode::getType, params.getType());
            }
            if (params.getState() != null) {
                wrapper.lambda().eq(RedeemCode::getState, params.getState());
            } else {
                wrapper.lambda().eq(RedeemCode::getState, 1);
            }
        }
        // 按更新时间倒序
        wrapper.lambda().orderByDesc(RedeemCode::getCreateDate);

        //获得数据
        IPage<RedeemCode> page = this.page(query, wrapper);
        //转换结果
        IPage<RedeemCodeReqDTO> pageData = JsonHelper.parseObject(page, new TypeReference<Page<RedeemCodeReqDTO>>() {
        });
        return pageData;
    }

    @Override
    public IPage<RedeemCodeDTO> codePaging(PagingReqDTO<RedeemCodeDTO> reqDTO) {
        // 创建分页对象
        Page query = reqDTO.toPage();
        //查询条件
        QueryWrapper<RedeemCode> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(RedeemCode::getId, RedeemCode::getTitle, RedeemCode::getState, RedeemCode::getType,
                RedeemCode::getUseTime, RedeemCode::getUserId, RedeemCode::getCode, RedeemCode::getCreateDate,RedeemCode::getAccountType,
        RedeemCode::getUserType,RedeemCode::getExpirationDate,RedeemCode::getAccountExpirationDate,RedeemCode::getYearCount);
        // 请求参数
        RedeemCodeDTO params = reqDTO.getParams();
        if (params != null && StringUtils.isNotBlank(params.getId())) {
            wrapper.lambda().eq(RedeemCode::getPid, params.getId());
            if (params.getType() != null) {
                wrapper.lambda().eq(RedeemCode::getType, params.getType());
            }
            if (params.getState() != null) {
                wrapper.lambda().eq(RedeemCode::getState, params.getState());
            }
        } else {
            throw new ServiceException(ApiError.ERROR_10010001);
        }
        // 按更新时间倒序
        wrapper.lambda().orderByAsc(RedeemCode::getId);
        //获得数据
        IPage<RedeemCode> page = this.page(query, wrapper);
        //转换结果
        IPage<RedeemCodeDTO> pageData = JsonHelper.parseObject(page, new TypeReference<Page<RedeemCodeDTO>>() {
        });

        List<RedeemCodeDTO> records = pageData.getRecords();
        for (RedeemCodeDTO record : records) {
            String userId = record.getUserId();
            if (StringUtils.isNotBlank(userId)){
                List<SysRole> sysRoles = sysUserRoleService.listRoles(userId);
                record.setRole(sysRoles.stream().map(SysRole::getRoleName).collect(Collectors.joining(",")));
            }
        }
        return pageData;
    }


    @Override
    public List<SysDicValue> redeemCodeTypeList() {
        LambdaQueryWrapper<SysDicValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDicValue::getDicCode, "redeem_type");
        List<SysDicValue> sysDicValues = sysDicValueService.list(queryWrapper);
        return sysDicValues;
    }

    @Override
    public List<RedeemCode> saveCode(RedeemCodeDTO reqDTO) {
        RedeemCode redeemCode = new RedeemCode();
        BeanMapper.copy(reqDTO, redeemCode);
        redeemCode.setPid("0");
        redeemCode.setState(0);
        redeemCode.setSalePrice(BigDecimal.ZERO);
        String province = redeemCode.getProvince();
        if (StringUtils.isNotBlank(province)){
            String city = redeemCode.getCity();
            String provinceFirstStr = province.substring(0, 1);
            String provinceSecondStr = province.substring(1, 2);
            String provinceFirstPy = StrChineseToEnglishUtils.getPinYinHeadChar(provinceFirstStr);
            String provinceSecondPy = StrChineseToEnglishUtils.getPinYinHeadChar(provinceSecondStr);
            String provinceFirstChar = provinceFirstPy.toUpperCase();
            String provinceSecondChar = provinceSecondPy.toUpperCase();

            String cityFirstStr = city.substring(0, 1);
            String citySecondStr = city.substring(1, 2);
            String cityFirstPy = StrChineseToEnglishUtils.getPinYinHeadChar(cityFirstStr);
            String citySecondPy = StrChineseToEnglishUtils.getPinYinHeadChar(citySecondStr);
            String cityFirstChar = cityFirstPy.toUpperCase();
            String citySecondChar = citySecondPy.toUpperCase();
            redeemCode.setCode(provinceFirstChar+provinceSecondChar+cityFirstChar+citySecondChar);
        }else {
            redeemCode.setCode("");
        }

        redeemCode.setType(6);

        baseMapper.insert(redeemCode);

        RedeemCodeReqDTO redeemCodeReqDTO = new RedeemCodeReqDTO();
        redeemCodeReqDTO.setId(redeemCode.getId());
        redeemCodeReqDTO.setCodeCount(reqDTO.getCodeCount());
        redeemCodeReqDTO.setUserIds(reqDTO.getUserIds());
        List<RedeemCode> redeemCodes = generateRedeemCode(redeemCodeReqDTO);
        return redeemCodes;
    }

    @Override
    public void delRedeemCode(String id) {
        LambdaUpdateWrapper<RedeemCode> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RedeemCode::getPid, id).eq(RedeemCode::getState, 0);
        baseMapper.delete(updateWrapper);
        baseMapper.deleteById(id);
    }

    @Override
    public List<CodeExportDTO> listForExport(RedeemCodeDTO reqDTO) {
        LambdaQueryWrapper<RedeemCode> redeemCodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        redeemCodeLambdaQueryWrapper.select(RedeemCode::getId, RedeemCode::getCode, RedeemCode::getTitle,RedeemCode::getExpirationDate,
                RedeemCode::getAccountExpirationDate,RedeemCode::getAccountType,RedeemCode::getYearCount);
        redeemCodeLambdaQueryWrapper.eq(RedeemCode::getPid, reqDTO.getId());
        redeemCodeLambdaQueryWrapper.eq(RedeemCode::getState, 0);
        List<RedeemCode> redeemCodes = baseMapper.selectList(redeemCodeLambdaQueryWrapper);
        List<CodeExportDTO> codeExportDTOS = BeanMapper.mapList(redeemCodes, CodeExportDTO.class);
        // 手动设置日期，以触发日期字符串的更新
        for (CodeExportDTO codeExportDTO : codeExportDTOS) {
            codeExportDTO.setExpirationDate(codeExportDTO.getExpirationDate());
            codeExportDTO.setAccountExpirationDate(codeExportDTO.getAccountExpirationDate());
        }
        return codeExportDTOS;
    }

    @Override
    public IPage<RedeemCodeDTO> redeemCodePage(PagingReqDTO<RedeemCodeDTO> reqDTO) {
        RedeemCodeDTO params = reqDTO.getParams();
        if (params == null){
            params = new RedeemCodeDTO();
        }
        List<String> roles = UserUtils.getRoles();
        if (!roles.contains(SysRoleId.SA) && roles.contains(SysRoleId.SCHOOL_ADMIN)){
            if (StringUtils.isBlank(params.getPid()) && StringUtils.isBlank(params.getUsername())
            && StringUtils.isBlank(params.getTitle()) && (params.getState() == null || params.getState() == 0)){
                return reqDTO.toPage();
            }

            String departCode = UserUtils.departCode();
            params.setDeptCode(departCode);
        }
        IPage<RedeemCodeDTO> page = baseMapper.redeemCodePage(reqDTO.toPage(), params);
        List<RedeemCodeDTO> records = page.getRecords();
        Map<String, String> createNameMap = new HashMap<>();
        //创建人
        Set<String> createByIds = records.stream().map(RedeemCodeDTO::getCreateBy).collect(Collectors.toSet());
        if (createByIds.size() > 0){
            LambdaQueryWrapper<SysUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.select(SysUser::getId,SysUser::getRealName);
            userLambdaQueryWrapper.in(SysUser::getId, createByIds);
            List<SysUser> userList = sysUserService.list(userLambdaQueryWrapper);
            createNameMap = userList.stream()
                    .collect(Collectors.toMap(
                            SysUser::getId,
                            SysUser::getRealName,
                            (existingValue, newValue) -> existingValue
                    ));

        }
        for (RedeemCodeDTO record : records) {
            String userId = record.getUserId();
            String createBy = record.getCreateBy();
            List<SysRole> sysRoles = sysUserRoleService.listRoles(userId);
            if (sysRoles.size() > 0){
                List<String> roleNames = sysRoles.stream().map(SysRole::getRoleName).collect(Collectors.toList());
                record.setRoleNames(String.join(",", roleNames));
            }
            record.setCreateByName(createNameMap.get(createBy));
        }
        return page;
    }

    @Override
    public List<RedeemCodeExportDTO> codeExport(PagingReqDTO<RedeemCodeDTO> reqDTO) {
        RedeemCodeDTO params = reqDTO.getParams();
        if (params == null){
            params = new RedeemCodeDTO();
        }
        reqDTO.setSize(999999);
        IPage<RedeemCodeDTO> page = this.redeemCodePage(reqDTO);
        List<RedeemCodeDTO> records = page.getRecords();
        List<RedeemCodeExportDTO> redeemCodeExportDTOS = BeanMapper.mapList(records, RedeemCodeExportDTO.class);
        // 手动设置日期，以触发日期字符串的更新
        for (RedeemCodeExportDTO codeExportDTO : redeemCodeExportDTOS) {
            codeExportDTO.setAccountDate(codeExportDTO.getAccountExpirationDate());
            codeExportDTO.setStatus(codeExportDTO.getState());
        }
        return redeemCodeExportDTOS;
    }


    @Override
    public List<CodeTemplateExportDTO> codeTemplateExport(PagingReqDTO<RedeemCodeDTO> reqDTO) {
        reqDTO.setSize(999999);
        RedeemCodeDTO params = reqDTO.getParams();
        if (params == null){
            params = new RedeemCodeDTO();
        }
        IPage<RedeemCodeDTO> page = this.paging(reqDTO);
        List<RedeemCodeDTO> records = page.getRecords();
        List<CodeTemplateExportDTO> redeemCodeExportDTOS = BeanMapper.mapList(records, CodeTemplateExportDTO.class);
        for (CodeTemplateExportDTO redeemCodeExportDTO : redeemCodeExportDTOS) {
            redeemCodeExportDTO.setUserTypeStr(redeemCodeExportDTO.getUserType());
            redeemCodeExportDTO.setLeftNum(redeemCodeExportDTO.getUsedNum(),redeemCodeExportDTO.getCodeCount());
            redeemCodeExportDTO.setCreateDateStr(redeemCodeExportDTO.getCreateDate());
            redeemCodeExportDTO.setExpirationDateStr(redeemCodeExportDTO.getExpirationDate());
        }
        return redeemCodeExportDTOS;
    }

    @Override
    public CodeDetailDTO findByCode(String activationCode) {

        CodeDetailDTO codeDetailDTO = new CodeDetailDTO();
        if (StringUtils.isBlank(activationCode)){
            throw new ServiceException("请输入激活码");
        }
        activationCode = activationCode.trim();

        String redeemCode = activationCode;
        if (activationCode.length() < 15){
            throw new ServiceException("请输入正确的激活码");
        }
        //如果用户没传激活码中的"-"，给补全
        if (!activationCode.contains("-")){
            String front = activationCode.substring(0, 5);
            String middle = activationCode.substring(5, 10);
            String back = activationCode.substring(10, 15);
            redeemCode = front + "-" + middle + "-" + back;
        }

        LambdaQueryWrapper<RedeemCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RedeemCode::getCode, redeemCode);
        RedeemCode code = this.getOne(queryWrapper);
        if (code == null){
            codeDetailDTO.setErrorMsg("激活码无效");
            return codeDetailDTO;
        }

        if (code.getState() == 1){
            codeDetailDTO.setErrorMsg("激活码已被使用");
        }

        Date expirationDate = code.getExpirationDate();
        if (expirationDate != null && expirationDate.before(new Date())){
            codeDetailDTO.setErrorMsg("激活码已过期");
        }

        Date accountExpirationDate = code.getAccountExpirationDate();
        if (accountExpirationDate != null && accountExpirationDate.before(new Date())){
            codeDetailDTO.setErrorMsg("激活码已过期");
        }

        if (StringUtils.isNotBlank(codeDetailDTO.getErrorMsg())){
            return codeDetailDTO;
        }



        List<String> gradeValueIds = code.getGradeValueIds();
        // 1. 获取所有需要的SysDicValue对象，并映射到ID
        Map<String, SysDicValue> sysDicValueMap = new HashMap<>();

        if (gradeValueIds != null && !gradeValueIds.isEmpty()) {
            List<SysDicValue> sysDicValues = getAllSysDicValuesByIds(gradeValueIds);
            for (SysDicValue sysDicValue : sysDicValues) {
                sysDicValueMap.put(sysDicValue.getId(), sysDicValue);
            }
        }

        // 2. 缓存所有parentId对应的SysDicValue标题
        Map<String, String> parentTitleMap = new HashMap<>();
        for (SysDicValue sysDicValue : sysDicValueMap.values()) {
            if (sysDicValue.getParentId() != null) {
                parentTitleMap.put(sysDicValue.getParentId(), getSysDicValueTitleById(sysDicValue.getParentId()));
            }
        }
        if (gradeValueIds != null && gradeValueIds.size() > 0){
            List<SysDicValue> dicValues = sysDicValueService.listByIds(gradeValueIds);
            code.setGradeValueIds(dicValues.stream().map(SysDicValue::getValue).collect(Collectors.toList()));
            if (!code.getGradeValueIds().isEmpty() && code.getAllVolumes() == 0) {
                // 为每个record创建一个新的dtoList，仅包含当前record的gradeValueIds对应的SysDicValue
                List<DicValueTreeDTO> dtoList = code.getGradeValueIds().stream()
                        // 使用Optional来避免可能的null值
                        .map(gradeValueId -> Optional.ofNullable(sysDicValueMap.get(gradeValueId)))
                        .filter(Optional::isPresent) // 过滤掉空的Optional对象
                        .map(Optional::get) // 获取实际的sysDicValue对象
                        .map(sysDicValue -> BeanMapper.map(sysDicValue, DicValueTreeDTO.class))
                        .collect(Collectors.toList());
                for (DicValueTreeDTO dicValueTreeDTO : dtoList) {
                    String parentId = dicValueTreeDTO.getParentId();
                    dicValueTreeDTO.setWholeName(parentTitleMap.get(parentId) + dicValueTreeDTO.getTitle());
                }
                codeDetailDTO.setContent(dtoList.stream().map(DicValueTreeDTO::getWholeName).collect(Collectors.joining(",")));
            } else if (code.getAllVolumes() != null && code.getAllVolumes() == 1) {
                codeDetailDTO.setContent("全年级");
            }
        }

        if (expirationDate == null){
            codeDetailDTO.setExpirationDateStr("-");
        }else {
            codeDetailDTO.setExpirationDateStr(DateUtils.formatDate(expirationDate));
        }
        Integer accountType = code.getAccountType();
        if (accountType == 1){
            codeDetailDTO.setAccountExpirationDateStr("永久有效");
        }else if (accountType == 2){
            codeDetailDTO.setAccountExpirationDateStr(DateUtils.formatDate(accountExpirationDate));
        }else {
            codeDetailDTO.setAccountExpirationDateStr(code.getYearCount()+"天");
        }

        return codeDetailDTO;
    }

    @Override
    public void exportCodeInfo(HttpServletResponse response, String codeId) throws Exception {
        RedeemCode redeemCode = baseMapper.selectById(codeId);

        CodeWordRespDTO codeWordRespDTO = new CodeWordRespDTO();
        codeWordRespDTO.setId(codeId);
        codeWordRespDTO.setTitle(redeemCode.getTitle());
        codeWordRespDTO.setCreateTime(DateUtils.formatDate(redeemCode.getCreateDate(), "yyyy年MM月dd日"));
        codeWordRespDTO.setPrintTime(DateUtils.formatDate(new Date(), "yyyy年MM月dd日"));

        //激活码失效时间
        Date expirationDate = redeemCode.getExpirationDate();
        if (expirationDate != null){
            codeWordRespDTO.setExpirationDate(DateUtils.formatDate(redeemCode.getExpirationDate(), "yyyy年MM月dd日"));
        }else {
            codeWordRespDTO.setExpirationDate("无");
        }

        //授权有效期
        Integer accountType = redeemCode.getAccountType();
        if (accountType == 1){
            codeWordRespDTO.setAccountExpirationDate("永久有效");
        }else if (accountType == 2){
            codeWordRespDTO.setAccountExpirationDate(DateUtils.formatDate(redeemCode.getAccountExpirationDate(), "yyyy年MM月dd日"));
        }else {
            codeWordRespDTO.setAccountExpirationDate(redeemCode.getYearCount()+"天");
        }

        //激活内容
        // 1. 获取所有需要的SysDicValue对象，并映射到ID
        Map<String, SysDicValue> sysDicValueMap = new HashMap<>();

        List<String> gradeValueIds = redeemCode.getGradeValueIds();
        if (gradeValueIds != null && !gradeValueIds.isEmpty()) {
            List<SysDicValue> sysDicValues = getAllSysDicValuesByIds(gradeValueIds);
            for (SysDicValue sysDicValue : sysDicValues) {
                sysDicValueMap.put(sysDicValue.getId(), sysDicValue);
            }
        }

        // 2. 缓存所有parentId对应的SysDicValue标题
        Map<String, String> parentTitleMap = new HashMap<>();
        for (SysDicValue sysDicValue : sysDicValueMap.values()) {
            if (sysDicValue.getParentId() != null) {
                parentTitleMap.put(sysDicValue.getParentId(), getSysDicValueTitleById(sysDicValue.getParentId()));
            }
        }
        if (!redeemCode.getGradeValueIds().isEmpty() && redeemCode.getAllVolumes() == 0) {
            // 为每个record创建一个新的dtoList，仅包含当前record的gradeValueIds对应的SysDicValue
            List<DicValueTreeDTO> dtoList = redeemCode.getGradeValueIds().stream()
                    // 使用Optional来避免可能的null值
                    .map(gradeValueId -> Optional.ofNullable(sysDicValueMap.get(gradeValueId)))
                    .filter(Optional::isPresent) // 过滤掉空的Optional对象
                    .map(Optional::get) // 获取实际的sysDicValue对象
                    .map(sysDicValue -> BeanMapper.map(sysDicValue, DicValueTreeDTO.class))
                    .collect(Collectors.toList());
            for (DicValueTreeDTO dicValueTreeDTO : dtoList) {
                String parentId = dicValueTreeDTO.getParentId();
                dicValueTreeDTO.setWholeName(parentTitleMap.get(parentId) + dicValueTreeDTO.getTitle());
            }
            codeWordRespDTO.setContent(dtoList.stream().map(DicValueTreeDTO::getWholeName).collect(Collectors.joining(",")));
        } else if (redeemCode.getAllVolumes() != null && redeemCode.getAllVolumes() == 1) {
            codeWordRespDTO.setContent("全年级");
        }

        //数量
        Integer userType = redeemCode.getUserType();
        if (userType == 1){
            codeWordRespDTO.setTeacherCount(redeemCode.getCodeCount()+"");
            codeWordRespDTO.setStudentCount("0");
        }else if (userType == 2){
            codeWordRespDTO.setStudentCount(redeemCode.getCodeCount()+"");
            codeWordRespDTO.setTeacherCount("0");
        }else {
            //老数据没有区分教师学生
            codeWordRespDTO.setTeacherCount(redeemCode.getCodeCount()+"");
        }

        if (StringUtils.isNotBlank(redeemCode.getSapOrderId())){
            codeWordRespDTO.setSapOrderId(redeemCode.getSapOrderId());
        }else {
            codeWordRespDTO.setSapOrderId("");
        }

        //wordUtil.generate(response,"/templates", "激活码开通服务确认单.xml", codeWordRespDTO, redeemCode.getTitle()+"-激活码开通服务确认单");
        String oss = wordUtil.generateOss("/templates", "激活码开通服务确认单.xml", codeWordRespDTO, redeemCode.getTitle()+"-激活码开通服务确认单.docx");
        try {
            SysUserLoginDTO user = UserUtils.getUser(true);
            msgService.exportNotify(redeemCode.getTitle()+"-激活码开通服务确认单",oss,user.getId(),user.getRealName());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateUseNum(String redeemCodeId, Integer num) {
        baseMapper.updateUseNum(redeemCodeId, num);
    }

    @Override
    public void updateGetNum(String redeemCodeId, Integer num) {
        baseMapper.updateGetNum(redeemCodeId, num);
    }


    @Override
    public void updateSapInfo(RedeemCodeDTO reqDTO) {
        String id = reqDTO.getId();
        RedeemCode redeemCode = baseMapper.selectById(id);
        redeemCode.setGradeValueIds(null);
        BeanMapper.copy(reqDTO, redeemCode);
        baseMapper.updateById(redeemCode);
    }


    @Override
    public void exportSapCodeInfo(HttpServletResponse response, String codeId) throws Exception {
        RedeemCode redeemCode = baseMapper.selectById(codeId);
        CodeWordRespDTO codeWordRespDTO = new CodeWordRespDTO();
        Boolean all = false;
        if (StringUtils.isNotBlank(redeemCode.getSapOrderId())){
            codeWordRespDTO.setSapOrderId(redeemCode.getSapOrderId());
            all = true;
        }else {
            codeWordRespDTO.setSapOrderId("");
        }

        codeWordRespDTO.setId(codeId);
        codeWordRespDTO.setTitle(redeemCode.getTitle());
        codeWordRespDTO.setCreateTime(DateUtils.formatDate(redeemCode.getCreateDate(), "yyyy年MM月dd日"));
        codeWordRespDTO.setPrintTime(DateUtils.formatDate(new Date(), "yyyy年MM月dd日"));

        if (all){
            LambdaQueryWrapper<RedeemCode> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(RedeemCode::getCodeCount, RedeemCode::getUsedNum);
            queryWrapper.eq(RedeemCode::getSapOrderId, redeemCode.getSapOrderId());
            queryWrapper.eq(RedeemCode::getPid, "0");
            List<RedeemCode> redeemCodes = baseMapper.selectList(queryWrapper);
            Integer codeCount = 0;
            Integer usedCount = 0;
            for (RedeemCode code : redeemCodes) {
                codeCount = codeCount + code.getCodeCount();
                usedCount = usedCount + code.getUsedNum();
            }
            codeWordRespDTO.setCodeCount(codeCount+"");
            codeWordRespDTO.setUseCount(usedCount+"");
            int i = codeCount - usedCount;
            codeWordRespDTO.setNoUseCount(i+"");
        }else {
            codeWordRespDTO.setCodeCount(redeemCode.getCodeCount()+"");
            codeWordRespDTO.setUseCount(redeemCode.getUsedNum()+"");
            int i = redeemCode.getCodeCount() - redeemCode.getUsedNum();
            codeWordRespDTO.setNoUseCount(i+"");
        }

//        //授权有效期
//        Integer accountType = redeemCode.getAccountType();
//        if (accountType == 1){
//            codeWordRespDTO.setAccountStartDate(DateUtils.formatDate(redeemCode.getCreateDate(), "yyyy年MM月dd日"));
//            codeWordRespDTO.setAccountEndDate("永久有效");
//        }else if (accountType == 2){
//            codeWordRespDTO.setAccountStartDate(DateUtils.formatDate(redeemCode.getCreateDate(), "yyyy年MM月dd日"));
//            codeWordRespDTO.setAccountEndDate(DateUtils.formatDate(redeemCode.getAccountExpirationDate(), "yyyy年MM月dd日"));
//        }else {
//            codeWordRespDTO.setAccountStartDate("自使用日期开始");
//            codeWordRespDTO.setAccountEndDate(redeemCode.getYearCount()+"天");
//        }

        codeWordRespDTO.setAccountStartDate("");
        codeWordRespDTO.setAccountEndDate("");

        if (StringUtils.isNotBlank(redeemCode.getSapType())){
            codeWordRespDTO.setSapType(redeemCode.getSapType());
        }else {
            codeWordRespDTO.setSapType("");
        }
        if (redeemCode.getSapOrderStartDate() !=null){
            codeWordRespDTO.setSapOrderStartDate(DateUtils.formatDate(redeemCode.getSapOrderStartDate(), "yyyy年MM月dd日"));
            codeWordRespDTO.setAccountStartDate(DateUtils.formatDate(redeemCode.getSapOrderStartDate(), "yyyy年MM月dd日"));
        }else {
            codeWordRespDTO.setSapOrderStartDate("");
        }
        if (redeemCode.getSapOrderEndDate() !=null){
            codeWordRespDTO.setSapOrderEndDate(DateUtils.formatDate(redeemCode.getSapOrderEndDate(), "yyyy年MM月dd日"));
            codeWordRespDTO.setAccountEndDate(DateUtils.formatDate(redeemCode.getSapOrderEndDate(), "yyyy年MM月dd日"));

        }else {
            codeWordRespDTO.setSapOrderEndDate("");
        }

        //wordUtil.generate(response,"/templates", "激活码SAP.xml", codeWordRespDTO, redeemCode.getTitle()+"-激活码SAP确认单");
        String oss = wordUtil.generateOss("/templates", "激活码SAP.xml", codeWordRespDTO, redeemCode.getTitle()+"-激活码SAP确认单.docx");
        try {
            SysUserLoginDTO user = UserUtils.getUser(true);
            msgService.exportNotify(redeemCode.getTitle()+"-激活码SAP确认单",oss,user.getId(),user.getRealName());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void exportSapCodeExcel() {
        SysUserLoginDTO user = UserUtils.getUser(true);
        List<SapCodeExportDTO> list = baseMapper.findSapCodeInfos();
        String ossUrl = exportExcel.exportOssUrl(SapCodeExportDTO.class, "激活码SAP列表导出", list);
        try {
            msgService.exportNotify("激活码SAP列表导出",ossUrl,user.getId(),user.getRealName());
        } catch (Exception e) {
            // 改进异常处理，记录详细的异常信息
            log.error("导出激活码SAP列表时发生错误", e);
        }
    }


    @Override
    public RedeemCodeDTO findBySapId(String sapId) {
        LambdaQueryWrapper<RedeemCode> redeemCodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        redeemCodeLambdaQueryWrapper.select(RedeemCode::getSapOrderId, RedeemCode::getSapOrderStartDate, RedeemCode::getSapOrderEndDate,
                RedeemCode::getSapType,RedeemCode::getSapApplyUser,RedeemCode::getSapOrderTitle);
        redeemCodeLambdaQueryWrapper.eq(RedeemCode::getSapOrderId, sapId).eq(RedeemCode::getPid, "0");
        redeemCodeLambdaQueryWrapper.last("limit 1");
        RedeemCode code = this.getOne(redeemCodeLambdaQueryWrapper);
        RedeemCodeDTO redeemCodeDTO = new RedeemCodeDTO();
        BeanMapper.copy(code, redeemCodeDTO);
        return redeemCodeDTO;
    }


}
package com.yf.exam.modules.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 考题目目实体类
 * </p>
 * <AUTHOR>
 * @since 2020-09-05 11:14
 */
@Data
@TableName("el_exam_qu")
public class ExamQu extends Model<ExamQu> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 考试ID
     */
    @TableField("exam_id")
    private String examId;

    /**
     * 题目ID
     */
    @TableField("qu_id")
    private String quId;

    /**
     * 题目类型
     */
    @TableField("qu_type")
    private String quType;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 排序
     */
    private Integer sort;

}

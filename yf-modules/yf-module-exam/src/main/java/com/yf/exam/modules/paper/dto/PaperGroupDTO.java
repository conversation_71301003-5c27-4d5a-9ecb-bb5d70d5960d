package com.yf.exam.modules.paper.dto;

import com.yf.base.api.annon.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 考试大题数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-04-26 14:20
 */
@Data
@ApiModel(value = "考试大题", description = "考试大题")
public class PaperGroupDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @ApiModelProperty(value = "试卷ID")
    private String paperId;

    @ApiModelProperty(value = "标题")
    private String title;

    @Dict(dicCode = "qu_type")
    @ApiModelProperty(value = "试题类型")
    private String quType;
    //private String quType_dictText;

    @ApiModelProperty(value = "试题数量")
    private Integer quCount;

    @ApiModelProperty(value = "试题总分")
    private BigDecimal totalScore;

    @ApiModelProperty(value = "单题分数")
    private BigDecimal perScore;

    @ApiModelProperty(value = "试题乱序")
    private Boolean quRand;

    @ApiModelProperty(value = "选项乱序")
    private Boolean itemRand;

    @ApiModelProperty(value = "选错也给分")
    private Boolean pathScore;

    @ApiModelProperty(value = "题体，Python,C++预留代码，图形化预留.sb3文件")
    private String titleBody;

    @ApiModelProperty(value = "流程图展示使用")
    private String flowDiagram;

}

package com.yf.exam.modules.tmpl.dto;

import com.yf.base.api.annon.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 试卷数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-04-25 13:59
 */
@Data
@ApiModel(value = "试卷", description = "试卷")
public class TmplDTO implements Serializable {


    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @ApiModelProperty(value = "试卷标题")
    private String title;

    @Dict(dicCode = "tmpl_catalog")
    @ApiModelProperty(value = "试卷分类")
    private String catId;
    //private String catId_dictText;

    @Dict(dicCode = "join_type")
    @ApiModelProperty(value = "组卷方式")
    private Integer joinType;
    //private String joinType_dictText;

    @ApiModelProperty(value = "题目数量")
    private Integer quCount;

    @ApiModelProperty(value = "总分数")
    private BigDecimal totalScore;

    @ApiModelProperty(value = "限时策略1整卷2单题")
    private Integer timeType;

    @ApiModelProperty(value = "所属部门")
    private String deptCode;

    @Dict(dictTable = "el_sys_user", dicCode = "id", dicText = "real_name")
    @ApiModelProperty(value = "创建人")
    private String createBy;
    private String createBy_dictText;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


}

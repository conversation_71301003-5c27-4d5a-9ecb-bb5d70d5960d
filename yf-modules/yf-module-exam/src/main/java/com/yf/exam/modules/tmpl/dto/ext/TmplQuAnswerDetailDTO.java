package com.yf.exam.modules.tmpl.dto.ext;

import com.yf.exam.modules.tmpl.dto.TmplQuAnswerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 试卷题目数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-04-25 13:59
 */
@Data
@ApiModel(value = "大题问题回答请求类", description = "大题问题回答请求类")
public class TmplQuAnswerDetailDTO extends TmplQuAnswerDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "答案内容", required = true)
    private String content;

    @ApiModelProperty(value = "备选图片", required = true)
    private String image;

    @ApiModelProperty(value = "ABC选项", required = true)
    private String tag;

    @ApiModelProperty(value = "是否正确项", required = true)
    private Boolean isRight;

    @ApiModelProperty(value = "分组ID", required = true)
    private String groupId;


}

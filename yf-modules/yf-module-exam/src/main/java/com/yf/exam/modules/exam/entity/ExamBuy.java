package com.yf.exam.modules.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import java.util.Date;

/**
 * <p>
 * 考试购买实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-01 17:21
 */
@Data
@TableName("el_exam_buy")
public class ExamBuy extends Model<ExamBuy> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 考试ID
     */
    @TableField("exam_id")
    private String examId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 支付单号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 是否已支付
     */
    private Boolean paid;

    /**
     * 是否是统一编排进入
     */
    @TableField
    private Integer isArm;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 是否录入信息
     */
    @TableField
    private Integer addInfo;

    /**
     * 申请理由
     */
    @TableField
    private String reason;

    /**
     * 设备是否通过
     */
    @TableField
    private String cameraPass;

    /**
     * 标记没有选到时间
     */
    @TableField
    private Boolean noTime;

    /**
     * 联考的课程Id
     */
    @TableField
    private String courseId;

    /**
     * 旧的考试id
     */
    @TableField
    private String oldExamId;


    /**
     * 考点id
     */
    @TableField
    private String examVenueId;

    /**
     * 考场id
     */
    @TableField
    private String examVenueChildId;

    /**
     * 座位号
     */
    @TableField
    private Integer seatNumber;

    /**
     * 准考证号
     */
    @TableField
    private String examTicketNumber;

    /**
     * 准考证文件地址
     */
    @TableField
    private String examTicketFileUrl;

    /**
     * 模板考试id
     */
    @TableField
    private String tempExamId;

    /**
     * 学员班级
     */
    @TableField
    private String userClass;

}

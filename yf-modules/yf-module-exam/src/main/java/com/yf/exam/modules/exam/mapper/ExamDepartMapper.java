package com.yf.exam.modules.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.exam.modules.exam.entity.ExamDepart;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 考试部门Mapper
 * </p>
 * <AUTHOR>
 * @since 2020-09-03 17:24
 */
public interface ExamDepartMapper extends BaseMapper<ExamDepart> {

    /**
     * 根据考试查找用户ID列表
     * @param examId
     * @param scope
     * @return
     */
    List<String> listUserIdByExam(@Param("examId") String examId, @Param("scope") Integer scope);
}

package com.yf.exam.modules.paper.dto.response;

import com.yf.exam.modules.paper.dto.PaperDTO;
import com.yf.exam.modules.paper.dto.ext.PaperGroupExtDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "考试结果展示响应类", description = "考试结果展示响应类")
public class ExamResultRespDTO extends PaperDTO {


    @ApiModelProperty(value = "结果显示类型", required = true)
    private Integer resultType;

    @ApiModelProperty(value = "感谢文字", required = true)
    private String thanks;

    @ApiModelProperty(value = "题目列表", required = true)
    private List<PaperGroupExtDTO> groupList;

}

package com.yf.exam.modules.tmpl.dto.ext;

import com.yf.exam.modules.tmpl.dto.TmplDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 试卷数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-04-25 13:59
 */
@Data
@ApiModel(value = "试卷详情请求响应类", description = "试卷详情请求响应类")
public class TmplDetailDTO extends TmplDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "大题列表", required = true)
    private List<TmplGroupDetailDTO> groupList;

}

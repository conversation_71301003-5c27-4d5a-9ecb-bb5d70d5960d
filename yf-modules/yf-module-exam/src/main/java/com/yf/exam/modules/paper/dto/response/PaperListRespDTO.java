package com.yf.exam.modules.paper.dto.response;

import com.yf.exam.modules.paper.dto.PaperCaptureDTO;
import com.yf.exam.modules.paper.dto.PaperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 试卷请求类
 * </p>
 * <AUTHOR>
 * @since 2020-05-25 17:31
 */
@Data
@ApiModel(value = "试卷列表响应类", description = "试卷列表响应类")
public class PaperListRespDTO extends PaperDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "人员", required = true)
    private String realName;

    @ApiModelProperty(value = "结果显示", required = true)
    private Integer resultType;

    @ApiModelProperty(value = "主摄视频", required = true)
    private String cameraVideo;

    @ApiModelProperty(value = "录屏视频", required = true)
    private String screenVideo;

    @ApiModelProperty(value = "副摄视频", required = true)
    private String mobileVideo;

    @ApiModelProperty(value = "截图列表", required = true)
    private List<PaperCaptureDTO> captures;

}

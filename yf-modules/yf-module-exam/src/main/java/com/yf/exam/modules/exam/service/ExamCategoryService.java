package com.yf.exam.modules.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.exam.modules.exam.dto.ExamCategoryDTO;
import com.yf.exam.modules.exam.dto.request.ExamCategorySaveReqDTO;
import com.yf.exam.modules.exam.entity.ExamCategory;
import com.yf.exam.modules.paper.dto.response.ExamCategoryTreeDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 考试科目分类业务接口类
 * </p>
 * <AUTHOR>
 */
public interface ExamCategoryService extends IService<ExamCategory> {

    /**
     * 保存考试科目分类
     * @param reqDTO
     * @return
     */
    String saveExamCategory(ExamCategoryDTO reqDTO);

    /**
     * 删除考试科目分类
     * @param examCategoryId
     */
    void removeExamCategory(String examCategoryId);


    /**
     * 考试科目分类详情
     * @param examCategoryId
     * @return
     */
    ExamCategoryDTO findExamCategory(String examCategoryId);

    /**
     * 考试科目分类列表
     * @param reqDTO
     * @return list
     */
    IPage<ExamCategoryDTO> examCategoryPage(PagingReqDTO<ExamCategoryDTO> reqDTO);

    /**
     * 考试科目分类列表(包含考试)
     * @param reqDTO
     * @return list
     */
    Map<String, List<ExamCategory>> examCategoryMap(ExamCategoryDTO reqDTO);

    /**
     * 考试科目分类列表-不分页-未登录
     * @param request
     * @return list
     */
    List<ExamCategoryDTO> examCategoryList(HttpServletRequest request);

    /**
     * 考试科目分类列表-要登录
     * @param request
     * @return list
     */
    List<ExamCategoryTreeDTO> examCategories(HttpServletRequest request);

    /**
     * 考试详情（不需要登录）
     * @param examCategoryId
     * @param request
     * @return
     */
    ExamCategoryTreeDTO examCategoryDetail(String examCategoryId, HttpServletRequest request);


    /**
     * 指定人员为免费
     * @param reqDTO
     */
    void designateFree(ExamCategorySaveReqDTO reqDTO);


    /**
     * 科目定员权限详情
     * @param examCategoryId
     * @return
     */
    ExamCategorySaveReqDTO userList(String examCategoryId);


}

package com.yf.exam.modules.exam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 考试积分规则数据传输类
 * </p>
 * <AUTHOR>
 * @since 2023-03-27 10:44
 */
@Data
@ApiModel(value = "考试积分规则", description = "考试积分规则")
public class ExamPointsDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @ApiModelProperty(value = "考试ID", required = true)
    private String examId;

    @ApiModelProperty(value = "分数段开始", required = true)
    private BigDecimal scoreStart;

    @ApiModelProperty(value = "分数段结束", required = true)
    private BigDecimal scoreEnd;

    @ApiModelProperty(value = "积分数量", required = true)
    private Integer points;

}

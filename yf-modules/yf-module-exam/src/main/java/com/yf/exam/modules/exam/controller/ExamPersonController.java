package com.yf.exam.modules.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.ability.log.annon.LogInject;
import com.yf.ability.log.enums.LogType;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import com.yf.base.api.api.dto.BaseIdsReqDTO;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.exam.modules.exam.dto.request.ExamPersonReqDTO;
import com.yf.exam.modules.exam.service.ExamPersonService;
import com.yf.system.modules.user.dto.SysUserDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 考试控制器
 * </p>
 * <AUTHOR>
 * @since 2020-07-25 16:18
 */
@Api(tags = {"考试人员管理"})
@RestController
@RequestMapping("/api/exam/person")
public class ExamPersonController extends BaseController {

    @Autowired
    private ExamPersonService baseService;


    /**
     * 添加课程人员
     * @param reqDTO
     * @return
     */
    @LogInject(title = "添加考试人员", logType = LogType.EXAM)
    @RequiresPermissions(value = {"exam:update", "exam:add"}, logical = Logical.OR)
    @ApiOperation(value = "添加考试人员")
    @PostMapping("/join")
    public ApiRest<List<String>> join(@RequestBody BaseIdsReqDTO reqDTO) {
        List<String> list = baseService.join(reqDTO);
        return super.success(list);
    }

    /**
     * 移除课程人员
     * @param reqDTO
     * @return
     */
    @LogInject(title = "添加考试人员", logType = LogType.EXAM)
    @RequiresPermissions(value = {"exam:update", "exam:add"}, logical = Logical.OR)
    @ApiOperation(value = "添加考试人员")
    @PostMapping("/remove")
    public ApiRest remove(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        baseService.remove(reqDTO);
        return super.success();
    }

    /**
     * 课程人员分页
     * @param reqDTO
     * @return
     */
    @RequiresPermissions(value = {"exam:update", "exam:add"}, logical = Logical.OR)
    @ApiOperation(value = "添加考试分页")
    @PostMapping("/paging")
    public ApiRest<IPage<SysUserDTO>> paging(@RequestBody PagingReqDTO<ExamPersonReqDTO> reqDTO) {

        //分页查询并转换
        IPage<SysUserDTO> page = baseService.paging(reqDTO);
        return super.success(page);
    }


}

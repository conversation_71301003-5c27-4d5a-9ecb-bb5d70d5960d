package com.yf.exam.modules.paper.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.exam.modules.paper.dto.ext.PaperGroupExtDTO;
import com.yf.exam.modules.paper.dto.ext.PaperQuDetailDTO;
import com.yf.exam.modules.paper.entity.PaperGroup;

import java.util.List;

/**
 * <p>
 * 考试大题业务类
 * </p>
 * <AUTHOR>
 * @since 2021-04-26 14:20
 */
public interface PaperGroupService extends IService<PaperGroup> {

    /**
     * 查找试卷的大题
     * @param paperId
     * @return
     */
    List<PaperGroupExtDTO> listGroup(String paperId);

    /**
     * 查找试卷的题目
     * @param paperId
     * @param full
     * @return
     */
    List<PaperGroupExtDTO> listCardGroup(String paperId, boolean full);


    /**
     * 查找题目详情，用于学员逐题答卷
     * @param paperId
     * @param quId
     * @return
     */
    PaperQuDetailDTO selectQuForAnswer(String paperId, String quId);

    /**
     * 根据考试记录删除
     * @param paperId
     */
    void deleteByPaperId(String paperId);
}

package com.yf.exam.modules.paper.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * 创建人：李享泉
 * 时间：2019/11/11 11:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
@ApiModel(value = "Scratch造型音频", description = "Scratch造型音频类")
@JsonInclude(JsonInclude.Include.NON_NULL)

public class ScratchFileCostumes {
    private String assetId;
    private String name;
    private Integer bitmapResolution;
    private String md5ext;
    private String dataFormat;
    private String format;
    private Integer rate;
    private Integer sampleCount;
    private Integer rotationCenterX;
    private Integer rotationCenterY;

}

package com.yf.exam.modules.exam.dto.response;

import com.yf.exam.modules.exam.dto.ExamDTO;
import com.yf.exam.modules.exam.entity.ExamCategory;
import com.yf.exam.modules.tmpl.entity.TmplGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 考试列表响应类
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "考试列表响应类", description = "考试列表响应类")
public class ExamDetailDTO extends ExamDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "题目数量")
    private List<TmplGroup> tmplGroups;


    @ApiModelProperty(value = "考试状态，1-录入考生信息，录入后根据时间判断是否入场，10分钟内，2-进入考试，10分钟外，3，请于考试10分钟内入场")
    private Integer examStatus;


    @ApiModelProperty(value = "科目")
    private ExamCategory examCategory;


}

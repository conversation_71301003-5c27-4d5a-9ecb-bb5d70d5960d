package com.yf.exam.modules.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.exam.modules.exam.dto.ExamRecordDTO;
import com.yf.exam.modules.exam.dto.request.ExamRecordReqDTO;
import com.yf.exam.modules.exam.dto.response.ExamRecordRespDTO;
import com.yf.exam.modules.exam.entity.ExamRecord;
import com.yf.exam.modules.paper.entity.Paper;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 考试记录业务类
 * </p>
 * <AUTHOR>
 * @since 2020-09-21 15:13
 */
public interface ExamRecordService extends IService<ExamRecord> {


    /**
     * 保存成绩
     * @param reqDTO
     */
    void save(ExamRecordDTO reqDTO);

    /**
     * 删除考试记录
     * @param ids
     */
    void delete(List<String> ids);

    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<ExamRecordRespDTO> paging(PagingReqDTO<ExamRecordReqDTO> reqDTO);


    /**
     * 考试完成后加入成绩
     * @param review
     * @param userId
     * @param examId
     * @param score
     * @param passed
     */
    void joinResult(boolean review, String userId, String examId, BigDecimal score, boolean passed);

    /**
     * 统计用户考试的次数
     * @param userId
     * @param examId
     * @return
     */
    int countExam(String userId, String examId);


    /**
     * 更新考试次数
     * @param userId
     * @param examId
     */
    void increaseTryCount(String userId, String examId);


    /**
     * 保存全部
     * @param list
     */
    void saveAll(List<ExamRecord> list);

    /**
     * 重新更新成绩
     * @param userId
     * @param examId
     * @param list
     */
    void calcResult(String userId, String examId, List<Paper> list);


    /**
     * 重新更新成绩
     * @param userId
     * @param examId
     * @param excludeId
     */
    void calcResult(String userId, String examId, String excludeId);


    /**
     * 重算是否通过
     * @param examId
     * @param qualifyScore
     */
    void calcPass(String examId, BigDecimal qualifyScore);

}

package com.yf.exam.modules.paper.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.exam.modules.paper.dto.PaperQuAnswerDTO;
import com.yf.exam.modules.paper.dto.ext.PaperQuAnswerExtDTO;
import com.yf.exam.modules.paper.entity.PaperQuAnswer;

import java.util.List;

/**
 * <p>
 * 试卷考题备选答案业务类
 * </p>
 * <AUTHOR>
 * @since 2020-05-25 16:33
 */
public interface PaperQuAnswerService extends IService<PaperQuAnswer> {

    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<PaperQuAnswerDTO> paging(PagingReqDTO<PaperQuAnswerDTO> reqDTO);

    /**
     * 查找试卷题目答案列表
     * @param paperId
     * @param quId
     * @return
     */
    List<PaperQuAnswerExtDTO> listForExam(String paperId, String quId);

    /**
     * 根据考试记录删除全部试题
     * @param paperId
     */
    void deleteByPaperId(String paperId);

}

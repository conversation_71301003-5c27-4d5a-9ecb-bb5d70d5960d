package com.yf.exam.modules.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 考试发证规则实体类
 * </p>
 * <AUTHOR>
 * @since 2023-03-27 10:44
 */
@Data
@TableName("el_exam_cert")
public class ExamCert extends Model<ExamCert> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 考试ID
     */
    @TableField("exam_id")
    private String examId;

    /**
     * 分数段开始
     */
    @TableField("score_start")
    private BigDecimal scoreStart;

    /**
     * 分数段结束
     */
    @TableField("score_end")
    private BigDecimal scoreEnd;

    /**
     * 证书ID
     */
    @TableField("cert_id")
    private String certId;

}

package com.yf.exam.modules.tmpl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.utils.BeanMapper;
import com.yf.exam.enums.TmplJoinType;
import com.yf.exam.modules.tmpl.dto.ext.TmplGroupDetailDTO;
import com.yf.exam.modules.tmpl.dto.ext.TmplQuDetailDTO;
import com.yf.exam.modules.tmpl.entity.Tmpl;
import com.yf.exam.modules.tmpl.entity.TmplGroup;
import com.yf.exam.modules.tmpl.mapper.TmplGroupMapper;
import com.yf.exam.modules.tmpl.service.TmplGroupRuleService;
import com.yf.exam.modules.tmpl.service.TmplGroupService;
import com.yf.exam.modules.tmpl.service.TmplQuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 试卷大题业务实现类
 * </p>
 * <AUTHOR>
 * @since 2021-04-25 13:59
 */
@Service
public class TmplGroupServiceImpl extends ServiceImpl<TmplGroupMapper, TmplGroup> implements TmplGroupService {

    @Autowired
    private TmplQuService tmplQuService;

    @Autowired
    private TmplGroupRuleService tmplGroupRuleService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAll(Tmpl tmpl, List<TmplGroupDetailDTO> groupList) {


        List<TmplGroup> list = new ArrayList<>();

        // 当前存在的ID
        List<String> ids = this.findIds(tmpl.getId());

        // 所有题目ID，判断重复的
        List<String> allIds = new ArrayList<>();

        for (TmplGroupDetailDTO item : groupList) {
            TmplGroup group = new TmplGroup();
            BeanMapper.copy(item, group);
            group.setTmplId(tmpl.getId());
            list.add(group);
            this.saveOrUpdate(group);


            // 题目查重
            if (!CollectionUtils.isEmpty(item.getQuList())) {
                for (TmplQuDetailDTO q : item.getQuList()) {
                    if (allIds.contains(q.getQuId())) {
                        throw new ServiceException(q.getContent() + "在整个试卷中出现重复，请检查！");
                    }
                    allIds.add(q.getQuId());
                }
            }


            // 选题组卷随机组卷
            if (TmplJoinType.TYPE1.equals(tmpl.getJoinType()) || TmplJoinType.TYPE2.equals(tmpl.getJoinType())) {
                // 保存试题
                tmplQuService.saveAll(tmpl.getId(), item.getPathScore(), group.getId(), item.getQuList());
            }

            // 随机组卷
            if (TmplJoinType.TYPE3.equals(tmpl.getJoinType())) {
                // 保存规则
                tmplGroupRuleService.saveAll(group.getId(), item.getRuleList());
            }


            // 移除存在的
            ids.remove(item.getId());
        }

        if (!CollectionUtils.isEmpty(ids)) {
            this.delete(ids);
        }
    }

    @Override
    public List<TmplGroup> listByTmpl(String tmplId) {
        QueryWrapper<TmplGroup> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(TmplGroup::getTmplId, tmplId);
        return this.list(wrapper);
    }

    /**
     * 查找已存在列表
     * @param tmplId
     * @return
     */
    private List<String> findIds(String tmplId) {
        QueryWrapper<TmplGroup> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .select(TmplGroup::getId)
                .eq(TmplGroup::getTmplId, tmplId);
        List<TmplGroup> list = this.list(wrapper);
        List<String> ids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (TmplGroup item : list) {
                ids.add(item.getId());
            }
        }
        return ids;
    }

    /**
     * 删除大题
     * @param ids
     */
    private void delete(List<String> ids) {
        QueryWrapper<TmplGroup> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(TmplGroup::getId, ids);
        this.remove(wrapper);

        // 移除题目
        tmplQuService.removeByGroups(ids);
    }

}

package com.yf.xxkj.modules.award.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 课堂互动记录
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "课堂互动记录", description = "课堂互动记录")
public class ClazzInteractionRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "老师", required = true)
    private String userId;

    @ApiModelProperty(value = "课堂id", required = true)
    private String clazzId;

    @ApiModelProperty(value = "类型 1-课件，2-点名 ,3-学科工具，4-测试 5-实验表单,6-抢答", required = true)
    private Integer type;

    @ApiModelProperty(value = "年级", required = true)
    private String grade;

    @ApiModelProperty(value = "成绩", required = true)
    private Integer score;

    @ApiModelProperty(value = "课件，实验工具，测试，实验表单id", required = true)
    private String coursewareId;
}

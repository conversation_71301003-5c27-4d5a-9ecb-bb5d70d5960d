package com.yf.xxkj.modules.dataCentre.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <p>
 * 资源数据总览 - 统计
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "资源数据总览", description = "资源数据总览")
public class ResourceDataOverviewDTO {


    @ApiModelProperty(value = "日期")
    private String date;


    @ApiModelProperty(value = "教学资源")
    private Integer teachNum;

    @ApiModelProperty(value = "学习资源")
    private Integer learnNum;

    @ApiModelProperty(value = "学科工具")
    private Integer toolNum;

    @ApiModelProperty(value = "免费资源")
    private Integer freeNum;

    @ApiModelProperty(value = "教师资源")
    private Integer teacherNum;
}

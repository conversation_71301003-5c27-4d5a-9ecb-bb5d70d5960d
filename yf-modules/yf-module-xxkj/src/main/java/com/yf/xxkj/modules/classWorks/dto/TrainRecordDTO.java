package com.yf.xxkj.modules.classWorks.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import com.yf.train.modules.train.dto.ext.TrainQuDetailDTO;
import com.yf.xxkj.modules.classManagement.dto.DepartDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 练习记录
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "练习记录", description = "练习记录")
public class TrainRecordDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "课堂id")
    private String clazzId;

    @ApiModelProperty(value = "随堂练类型 0-学科组件 1-页面，2-练习")
    private Integer worksType;

    @ApiModelProperty(value = "教师id")
    private String userId;

    @ApiModelProperty(value = "课程名称")
    private String clazzName;

    @ApiModelProperty(value = "课堂号")
    private String classNumber;

    @ApiModelProperty(value = "练习名称")
    private String practiceName;

    @ApiModelProperty(value = "学科工具id")
    private String worksId;

    @ApiModelProperty(value = "学科工具（作业）")
    private String subjectModule;

    @ApiModelProperty(value = "页ID（作业）")
    private String pageId;

    @ApiModelProperty(value = "页信息（作业）")
    private String pageInfo;

    @ApiModelProperty(value = "课程ID")
    private String courseId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


    @ApiModelProperty(value = "筛选用，年级")
    private String grade;


    @ApiModelProperty(value = "0-未提交 1-已提交 2-已批改")
    private Integer state;

    @ApiModelProperty(value = "返回参数，分组信息")
    private List<DepartDTO> departList;

    @ApiModelProperty(value = "学生提交的作业内容或地址")
    private String content;

    @ApiModelProperty(value = "学生封面")
    private String cover;


    @ApiModelProperty(value = "课节id")
    private String dirId;

    @ApiModelProperty(value = "练习ID")
    private String trainId;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "成绩")
    private String score;

    @ApiModelProperty(value = "是否已奖励红花")
    private Boolean award;

    @ApiModelProperty(value = "课节id")
    private String catalogId;


    @ApiModelProperty(value = "节名称")
    private String name;


    @ApiModelProperty(value = "学生id")
    private String studentId;

    /**
     * 锚点anchorId
     */
    @ApiModelProperty
    private String anchorId;

    /**
     * iframe的id
     */
    @ApiModelProperty
    private String iframeId;

    @ApiModelProperty(value = "iframeKey")
    private String iframeKey;

    @ApiModelProperty
    private List<TrainQuDetailDTO> trainProcess;

    @ApiModelProperty(value = "智能批改标识")
    private Integer intelligentCheck;
}

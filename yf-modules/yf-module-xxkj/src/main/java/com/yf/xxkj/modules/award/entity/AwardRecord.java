package com.yf.xxkj.modules.award.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 奖励记录
 * </p>
 * <AUTHOR>
 */
@Data
@TableName(value = "xk_award_record")
public class AwardRecord extends Model<AwardRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;


    /**
     * 发放人
     */
    @TableField
    private String userId;


    /**
     * 课堂id
     */
    @TableField
    private String clazzId;

    /**
     * 学生id
     */
    @TableField
    private String studentId;

    /**
     * 数量
     */
    @TableField
    private Integer amount;


    /**
     * 类型 1-奖励，2-扣除
     */
    @TableField
    private Integer type;

    /**
     * 发放方式 1-随堂练 ，2-同步练习， 3-红花榜 4-点名 5-抢答
     */
    @TableField
    private Integer source;


    /**
     * 年级
     */
    @TableField
    private String grade;

    /**
     * 年级值
     */
    @TableField
    private String gradeValue;

    /**
     * 学期Id
     */
    @TableField
    private String gradeValueId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date updateTime;

}

package com.yf.xxkj.modules.prepareLessons.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 资源下载记录
 * </p>
 * <AUTHOR>
 */
@Data
@TableName("xk_resource_operation_record")
public class ResourceOperationRecord extends Model<ResourceOperationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 类型 1：下载 2：预览
     */
    @TableField
    private Integer type;


    /**
     * 课件id
     */
    @TableField
    private String courseFileId;


    /**
     * 文件类型
     */
    @TableField
    private String fileType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date createTime;


    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;


}

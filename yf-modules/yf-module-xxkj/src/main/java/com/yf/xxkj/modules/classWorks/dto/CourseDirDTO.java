package com.yf.xxkj.modules.classWorks.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.util.List;

/**
 * <p>
 * 课节
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "课节", description = "课节")
public class CourseDirDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程id")
    private String courseRefDirId;

    @ApiModelProperty(value = "课程id")
    private String courseId;

    @ApiModelProperty(value = "课程封面")
    private String cover;

    @ApiModelProperty(value = "教材名称")
    private String courseName;

    @ApiModelProperty(value = "课节名称")
    private String courseDirName;

    @ApiModelProperty(value = "随堂练列表")
    private List<TrainRecordDTO> dataList;



    @ApiModelProperty(value = "年级")
    private String gradeId;

    @ApiModelProperty(value = "学生ID")
    private String userId;


    @ApiModelProperty(value = "catalogId")
    private String id;

    @ApiModelProperty(value = "书Id")
    private String bookId;

    @ApiModelProperty(value = "编程课节名称")
    private String name;


    @ApiModelProperty(value = "教材名")
    private String bookName;

    @ApiModelProperty(value = "册别")
    private String volumeId;
}

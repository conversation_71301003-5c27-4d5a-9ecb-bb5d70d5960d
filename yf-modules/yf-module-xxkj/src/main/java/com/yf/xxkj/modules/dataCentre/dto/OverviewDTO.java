package com.yf.xxkj.modules.dataCentre.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <p>
 * 统计总览
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "统计总览", description = "统计总览")
public class OverviewDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总授课时长")
    private Integer totalTime;

    @ApiModelProperty(value = "总授课人数", required = true)
    private Integer totalUser;

    @ApiModelProperty(value = "总授课资源数")
    private Integer totalCourseFile;

    @ApiModelProperty(value = "表单")
    private Integer totalFormCount;

    @ApiModelProperty(value = "年级")
    private String grade;

}

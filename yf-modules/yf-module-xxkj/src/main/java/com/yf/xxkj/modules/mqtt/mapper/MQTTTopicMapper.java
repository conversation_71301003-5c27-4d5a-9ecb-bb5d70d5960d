package com.yf.xxkj.modules.mqtt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.xxkj.modules.mqtt.entity.MQTTTopic;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * MQTT主题Mapper
 * </p>
 * <AUTHOR>
 */
@Repository
public interface MQTTTopicMapper extends BaseMapper<MQTTTopic> {

    //累加使用数
    void updateNumAddOne(@Param("id") String id);

}

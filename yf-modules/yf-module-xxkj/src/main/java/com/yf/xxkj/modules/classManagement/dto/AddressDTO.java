package com.yf.xxkj.modules.classManagement.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 地址
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "地址", description = "地址")
public class AddressDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "省")
    private String province;
    @ApiModelProperty(value = "市")
    private String city;
    @ApiModelProperty(value = "县")
    private String county;


}

package com.yf.xxkj.modules.mqtt.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * MQTT 设备-主题，中间表
 * </p>
 * <AUTHOR>
 */
@Data
@TableName("xk_mqtt_device_topic")
public class MQTTDeviceTopic extends Model<MQTTDeviceTopic> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * clientID
     */
    @TableField("client_id")
    private String clientId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 主题ID
     */
    @TableField("topic_id")
    private String topicId;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField
    @DefaultValue("0")
    private Boolean isDeleted;
}

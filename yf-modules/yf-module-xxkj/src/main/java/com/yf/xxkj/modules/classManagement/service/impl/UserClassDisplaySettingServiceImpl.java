package com.yf.xxkj.modules.classManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.xxkj.modules.classManagement.entity.UserClassDisplaySetting;
import com.yf.xxkj.modules.classManagement.mapper.UserClassDisplaySettingMapper;
import com.yf.xxkj.modules.classManagement.service.UserClassDisplaySettingService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 用户班级展示设置 服务实现类
 * </p>
 * <AUTHOR>
 */
@Service
public class UserClassDisplaySettingServiceImpl extends ServiceImpl<UserClassDisplaySettingMapper, UserClassDisplaySetting> implements UserClassDisplaySettingService {

    @Override
    public void saveUserDisplaySetting(String userId, Integer showType, Integer sortType) {
        LambdaQueryWrapper<UserClassDisplaySetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserClassDisplaySetting::getUserId, userId);
        UserClassDisplaySetting existingSetting = this.getOne(queryWrapper);
        
        if (existingSetting != null) {
            // 更新现有设置
            LambdaUpdateWrapper<UserClassDisplaySetting> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(UserClassDisplaySetting::getUserId, userId);
            updateWrapper.set(UserClassDisplaySetting::getShowType, showType);
            updateWrapper.set(UserClassDisplaySetting::getSortType, sortType);
            updateWrapper.set(UserClassDisplaySetting::getUpdateTime, new Date());
            this.update(updateWrapper);
        } else {
            // 创建新设置
            UserClassDisplaySetting newSetting = new UserClassDisplaySetting();
            newSetting.setUserId(userId);
            newSetting.setShowType(showType);
            newSetting.setSortType(sortType);
            newSetting.setCreateTime(new Date());
            newSetting.setUpdateTime(new Date());
            this.save(newSetting);
        }
    }

    @Override
    public UserClassDisplaySetting getUserDisplaySetting(String userId) {
        LambdaQueryWrapper<UserClassDisplaySetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserClassDisplaySetting::getUserId, userId);
        return this.getOne(queryWrapper);
    }
} 
package com.yf.xxkj.modules.dataCentre.service;


import com.yf.xxkj.modules.dataCentre.dto.FreeResourceDataDTO;
import com.yf.xxkj.modules.dataCentre.dto.SubjectUseStatisticDTO;
import com.yf.xxkj.modules.dataCentre.entity.ReportFreeResourceMonthDownload;

import java.util.List;

/***
 * @title 资源使用统计
 * @Description
 * <AUTHOR>
 **/

public interface ResourceUseService {

    /**
     * 免费资源-下载次数or使用次数
     * @param type
     * @return
     */
    Integer freeResourceStatistics(Integer type);


    /**
     * 免费资源-每月下载报表
     * @param date
     * @return
     */
    ReportFreeResourceMonthDownload saveFreeResourceReport(String date);


    /**
     * 免费资源-下载趋势折线图
     * @return
     */
    List<Object> freeResourceTrend();


    /**
     * 学科工具使用情况 前10个
     * @return
     */
    List<SubjectUseStatisticDTO> subjectUseStatisticRank();

}

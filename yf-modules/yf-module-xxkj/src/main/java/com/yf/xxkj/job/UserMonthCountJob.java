package com.yf.xxkj.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yf.system.modules.task.entity.SchedulerTask;
import com.yf.system.modules.task.mapper.SchedulerTaskMapper;
import com.yf.system.utils.DateUtils;
import com.yf.xxkj.modules.dataCentre.service.ReportUserTotalService;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.time.LocalDate;
import java.util.Date;

/**
 * 用户月度统计调度
 * <AUTHOR>
 */
@Log4j2
@Component
public class UserMonthCountJob implements Job {

    @Resource
    private ReportUserTotalService reportUserTotalService;


    @Resource
    private SchedulerTaskMapper schedulerTaskMapper;

    @SneakyThrows
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        // 每个月1日0点，截止到当前的所有用户数
        //让程序睡5-10秒，避免多个定时任务出现幻读，多次执行
        Thread.sleep((int) (Math.random() * (9999 - 5000 + 1) + 5000));
        //获取当前年月日时间戳
        Long time = DateUtils.parseString(DateUtils.formatDate(new Date())).getTime();
        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        LambdaQueryWrapper<SchedulerTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SchedulerTask::getName, name);
        SchedulerTask schedulerTask = schedulerTaskMapper.selectOne(queryWrapper);
        Long timestamp = schedulerTask.getTimestamp();
        if (!time.equals(timestamp)) {
            schedulerTask.setTimestamp(time);
            schedulerTask.setIp(InetAddress.getLocalHost().getHostAddress());
            schedulerTaskMapper.insertOrUpdate(schedulerTask);
            try {
                reportUserTotalService.initMonthTotal();
            } catch (Exception e) {
                e.printStackTrace();
                schedulerTask.setTimestamp(timestamp);
                schedulerTaskMapper.insertOrUpdate(schedulerTask);
            }
        }else {
            log.info("保存月度用户总量报表任务已经执行过！");
        }

    }

}

package com.yf.xxkj.modules.prepareLessons.dto;


import com.yf.ability.upload.providers.local.dto.UploadReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 上传文件请求类
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "上传文件请求类", description = "上传文件请求类")
public class UploadResourceDTO extends UploadReqDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "章节目录ID", required = true)
    private String dirId;


    @ApiModelProperty(value = "文件类型", required = true)
    private String fileType;


    @ApiModelProperty(value = "文件名", required = true)
    private String title;


}

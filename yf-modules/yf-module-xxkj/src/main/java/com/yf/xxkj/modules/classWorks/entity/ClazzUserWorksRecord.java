package com.yf.xxkj.modules.classWorks.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 课堂作业操作记录，用于编程统计页面操作时长
 * </p>
 * <AUTHOR>
 */
@Data
@TableName("xk_clazz_user_works_record")
public class ClazzUserWorksRecord extends Model<ClazzUserWorksRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;


    /**
     * 时间戳
     */
    @TableField
    private Long timestamp;


    /**
     * 课堂id
     */
    @TableField
    private String clazzId;

    /**
     * 作业id
     */
    @TableField
    private String clazzUserWorksId;


    /**
     * 类型 1-进入 ，2-离开
     */
    @TableField
    private Integer type;

    /**
     * 书ID
     */
    @TableField
    private String bookId;


    /**
     * 页id
     */
    @TableField
    private String pageId;


    /**
     * 创建人
     */
    @TableField
    private String userId;


    /**
     * 是否提交 0-未提交 1-已提交
     */
    @TableField
    @DefaultValue("0")
    private Integer submitted;

}

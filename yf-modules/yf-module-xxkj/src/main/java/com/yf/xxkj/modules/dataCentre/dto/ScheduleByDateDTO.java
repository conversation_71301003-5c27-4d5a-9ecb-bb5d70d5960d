package com.yf.xxkj.modules.dataCentre.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <p>
 * 课表
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "课表", description = "课表")
public class ScheduleByDateDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "日期")
    private String date;


    @ApiModelProperty(value = "该日期的各班级对应数据")
    private List<AwardRankDTO> awardRankList;

}

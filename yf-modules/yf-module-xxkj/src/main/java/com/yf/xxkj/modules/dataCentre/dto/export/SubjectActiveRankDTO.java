package com.yf.xxkj.modules.dataCentre.dto.export;




import com.yf.ability.excel.annotation.ExcelField;
import io.swagger.annotations.ApiModel;
import lombok.Data;


/**
 * <p>
 * 信科学科工具使用排名导出传输类
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "信科学科工具使用排名导出传输类", description = "信科学科工具使用排名导出传输类")
public class SubjectActiveRankDTO {

    private static final long serialVersionUID = 1L;

    @ExcelField(title = "工具名称", sort = 1)
    private String toolName;

    @ExcelField(title = "年级（通用）", sort = 2)
    private String grade;

    @ExcelField(title = "调用次数", sort = 3)
    private Integer useCount;

    @ExcelField(title = "调用人数", sort = 4)
    private Integer userCount;


}

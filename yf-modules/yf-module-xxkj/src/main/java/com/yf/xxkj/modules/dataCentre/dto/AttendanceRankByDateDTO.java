package com.yf.xxkj.modules.dataCentre.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <p>
 * 红花榜统计
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "红花榜统计", description = "红花榜统计")
public class AttendanceRankByDateDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "日期")
    private String date;


    @ApiModelProperty(value = "该日期的各班级对应数据")
    private List<AttendanceRankDTO> dataList;

}

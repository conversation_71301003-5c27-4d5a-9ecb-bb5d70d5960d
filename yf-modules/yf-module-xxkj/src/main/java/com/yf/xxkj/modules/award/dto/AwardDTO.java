package com.yf.xxkj.modules.award.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 奖励记录
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "奖励", description = "奖励")
public class AwardDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "数量", required = true)
    private Integer amount;

    @ApiModelProperty(value = "随堂练id")
    private String userWorksId;

    @ApiModelProperty(value = "同步练习id")
    private String userTrainId;

    @ApiModelProperty(value = "学生id数组")
    private List<String> studentIds;

    @ApiModelProperty(value = "clazzId")
    private String clazzId;

    @ApiModelProperty(value = "发放方式")
    private Integer source;
}

package com.yf.xxkj.modules.dataCentre.service;

import com.yf.xxkj.modules.dataCentre.dto.TeachDataDTO;
import com.yf.xxkj.modules.dataCentre.entity.ReportTeachDayData;

import java.util.List;
import java.util.Objects;

/***
 * @title 课堂教学情况
 * @Description
 * <AUTHOR>
 * @create 2024/9/10 13:58
 **/

public interface TeachStatisticService {

    /**
     * 用户数据-按天生成数据报表
     * @param date
     * @return
     */
    ReportTeachDayData saveTeachDataReport(String date);


    /**
     * 用户数据-日期列表
     * @return
     */
    List<Object> dateList();

    /**
     * 用户数据-课堂教学统计
     * @param date
     * @return
     */
    TeachDataDTO teachDataByDate(String date);
}

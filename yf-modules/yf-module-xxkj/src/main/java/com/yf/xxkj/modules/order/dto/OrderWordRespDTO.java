package com.yf.xxkj.modules.order.dto;




import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 信息科技订单传输类
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "信息科技订单传输类", description = "信息科技订单传输类")
public class OrderWordRespDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "订单名称")
    private String title;

    @ApiModelProperty(value = "学生数")
    private String studentAddedCount;

    @ApiModelProperty(value = "教师数")
    private String teacherAddedCount;

    @ApiModelProperty(value = "订单管理员数")
    private String agentCount;

    @ApiModelProperty(value = "教师服务时间")
    private String teaDate;

    @ApiModelProperty(value = "学生服务时间")
    private String stuDate;

    @ApiModelProperty(value = "订单开通时间")
    private String createTime;


    @ApiModelProperty(value = "打印时间")
    private String printTime;

    @ApiModelProperty(value = "有效用户数")
    private String userCount;

    @ApiModelProperty(value = "订单类型")
    private String sapType;

    @ApiModelProperty(value = "SAP订单号")
    private String sapOrderId;


    @ApiModelProperty(value = "订单起始日期")
    private String sapOrderStartDate;

    @ApiModelProperty(value = "订单结束日期")
    private String sapOrderEndDate;

    @ApiModelProperty(value = "申请人")
    private String createBy;

    @ApiModelProperty(value = "有效用户数")
    private String accountCount;

    @ApiModelProperty(value = "账号总数")
    private String accountAllCount;
}

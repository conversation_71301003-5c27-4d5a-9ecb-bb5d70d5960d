package com.yf.xxkj.modules.weather.dto;



import com.fasterxml.jackson.annotation.JsonInclude;
import com.yf.xxkj.modules.weather.entity.AirQualityRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 天气记录参数
 * </p>
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
@ApiModel(value = "天气记录参数", description = "天气记录参数")
public class WeatherRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "星期")
    private String week;

    @ApiModelProperty(value = "空气质量")
    private String airQuality;

    @ApiModelProperty(value = "日期")
    private String fxDate;

    @ApiModelProperty(value = "日出时间，在高纬度地区可能为空")
    private String sunrise;

    @ApiModelProperty(value = "日落时间，在高纬度地区可能为空")
    private String sunset;

    @ApiModelProperty(value = "当天月升时间，可能为空")
    private String moonrise;

    @ApiModelProperty(value = "当天月落时间，可能为空")
    private String moonset;

    @ApiModelProperty(value = "月相名称")
    private String moonPhase;

    @ApiModelProperty(value = "月相图标代码，另请参考天气图标项目")
    private String moonPhaseIcon;

    @ApiModelProperty(value = "预报当天最高温度")
    private String tempMax;

    @ApiModelProperty(value = "预报当天最低温度")
    private String tempMin;

    @ApiModelProperty(value = "预报白天天气状况的图标代码，另请参考天气图标项目")
    private String iconDay;

    @ApiModelProperty(value = "预报白天天气状况文字描述，包括阴晴雨雪等天气状态的描述")
    private String textDay;

    @ApiModelProperty(value = "预报夜间天气状况的图标代码，另请参考天气图标项目")
    private String iconNight;

    @ApiModelProperty(value = "预报晚间天气状况文字描述，包括阴晴雨雪等天气状态的描述")
    private String textNight;

    @ApiModelProperty(value = "预报白天风向360角度")
    private String wind360Day;

    @ApiModelProperty(value = "预报白天风向")
    private String windDirDay;

    @ApiModelProperty(value = "预报白天风力等级")
    private String windScaleDay;

    @ApiModelProperty(value = "预报白天风速，公里/小时")
    private String windSpeedDay;

    @ApiModelProperty(value = "预报夜间风向360角度")
    private String wind360Night;

    @ApiModelProperty(value = "预报夜间当天风向")
    private String windDirNight;

    @ApiModelProperty(value = "预报夜间风力等级")
    private String windScaleNight;

    @ApiModelProperty(value = "预报夜间风速，公里/小时")
    private String windSpeedNight;

    @ApiModelProperty(value = "相对湿度，百分比数值")
    private String humidity;

    @ApiModelProperty(value = "预报当天总降水量，默认单位：毫米")
    private String precip;

    @ApiModelProperty(value = "大气压强，默认单位：百帕")
    private String pressure;

    @ApiModelProperty(value = "能见度，默认单位：公里")
    private String vis;

    @ApiModelProperty(value = "云量，百分比数值。可能为空")
    private String cloud;

    @ApiModelProperty(value = "紫外线强度指数")
    private String uvIndex;


    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;


    @ApiModelProperty(value = "空气质量")
    private AirQualityRecord airQualityRecord;

}

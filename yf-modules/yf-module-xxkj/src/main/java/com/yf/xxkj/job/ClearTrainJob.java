package com.yf.xxkj.job;



import com.yf.train.modules.train.service.RepoTrainService;
import com.yf.xxkj.modules.order.service.AgencyOrderService;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 时任务
 * <AUTHOR>
 */
@Log4j2
@Component
public class ClearTrainJob implements Job {

    @Autowired
    private RepoTrainService repoTrainService;


    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {


        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        String group = detail.getKey().getGroup();

        log.info("++++++++++扫描定时任务：清理无用训练任务");
        log.info("++++++++++jobName:{}", name);
        log.info("++++++++++jobGroup:{}", group);

        repoTrainService.clearTrain();

    }


}

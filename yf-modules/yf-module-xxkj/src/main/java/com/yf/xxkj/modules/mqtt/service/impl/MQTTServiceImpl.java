package com.yf.xxkj.modules.mqtt.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yf.ability.Constant;
import com.yf.ability.redis.service.RedisService;
import com.yf.base.api.api.ApiError;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.utils.jackson.JsonHelper;
import com.yf.sign.utils.EncryptionUtils;
import com.yf.sign.utils.HttpRequestUtils;
import com.yf.system.modules.user.UserUtils;
import com.yf.system.utils.CopyUtils;
import com.yf.xxkj.modules.mqtt.dto.*;
import com.yf.xxkj.modules.mqtt.entity.*;
import com.yf.xxkj.modules.mqtt.mapper.*;
import com.yf.xxkj.modules.mqtt.service.MQTTService;
import com.yf.xxkj.modules.mqtt.vo.MQTTDeviceVO;
import com.yf.xxkj.modules.mqtt.vo.MQTTTopicVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/***
 * @title MQTTServiceImpl
 * @Description
 * <AUTHOR>
 * @create 2024/3/16 11:22
 **/
@Log4j2
@Service
public class MQTTServiceImpl implements MQTTService {


    @Value("${emqx.url}")
    private String url;

    @Value("${emqx.ip}")
    private String ip;

    @Value("${emqx.port}")
    private String port;

    @Value("${emqx.ws.port}")
    private String ws_port;

    @Value("${emqx.username}")
    private String username;

    @Value("${emqx.password}")
    private String password;

    @Value("${emqx.key}")
    private String emqxKey;

    @Autowired(required = false)
    private MqttClient emqxClient;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MQTTProjectMapper mQTTProjectMapper;

    @Autowired
    private MQTTDeviceMapper mQTTDeviceMapper;

    @Autowired
    private MQTTDeviceTopicMapper mQTTDeviceTopicMapper;

    @Autowired
    private MQTTMsgRecordMapper mQTTMsgRecordMapper;

    @Autowired
    private MQTTTopicMapper mQTTTopicMapper;

    @Autowired
    private MQTTUserViewMapper mqttUserViewMapper;


    @Override
    public IPage<MQTTProjectDTO> projectPage(PagingReqDTO<MQTTProjectDTO> reqDTO) {
        //查询条件
        QueryWrapper<MQTTProject> wrapper = new QueryWrapper<>();
        // 请求参数
        MQTTProjectDTO params = reqDTO.getParams();
        if (params != null) {
            if (!StringUtils.isEmpty(params.getName())) {
                wrapper.lambda().eq(MQTTProject::getName, params.getName());
            }
        }
        String userId = UserUtils.getUserId();
        if (StringUtils.isNotEmpty(userId)) {
            wrapper.lambda().eq(MQTTProject::getUserId, userId);
        }
        wrapper.lambda().orderByDesc(MQTTProject::getCreateTime);
        //获得数据
        IPage<MQTTProject> page = mQTTProjectMapper.selectPage(reqDTO.toPage(), wrapper);
        //转换结果
        IPage<MQTTProjectDTO> pageData = JsonHelper.parseObject(page, new TypeReference<Page<MQTTProjectDTO>>() {
        });
        return pageData;
    }

    @Override
    public void saveProject(MQTTProjectDTO reqDTO) {
        String id = reqDTO.getId();
        if (StringUtils.isNotEmpty(id)) {
            MQTTProject selectById = mQTTProjectMapper.selectById(id);
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, selectById);
            mQTTProjectMapper.updateById(selectById);
        } else {
            MQTTProject mQTTProject = new MQTTProject();
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, mQTTProject);
            String userId = UserUtils.getUserId();
            mQTTProject.setUserId(userId);
            mQTTProjectMapper.insert(mQTTProject);
        }
    }

    @Override
    public void delProject(String id) {
        String userId = UserUtils.getUserId();
        MQTTProject selectById = mQTTProjectMapper.selectById(id);
        if (null != selectById && !selectById.getCreateBy().equals(userId)) {
            throw new ServiceException("您不能删除他人数据！");
        }
        mQTTProjectMapper.deleteById(id);
        //删除项目中的主题和设备以及通讯记录
        LambdaQueryWrapper<MQTTDevice> delDeviceWrapper = new LambdaQueryWrapper<>();
        delDeviceWrapper.eq(MQTTDevice::getProjectId, id);
        mQTTDeviceMapper.delete(delDeviceWrapper);
        LambdaQueryWrapper<MQTTTopic> delTopicWrapper = new LambdaQueryWrapper<>();
        delTopicWrapper.eq(MQTTTopic::getProjectId, id);
        mQTTTopicMapper.delete(delTopicWrapper);
        LambdaQueryWrapper<MQTTMsgRecord> delRecordWrapper = new LambdaQueryWrapper<>();
        delRecordWrapper.eq(MQTTMsgRecord::getProjectId, id);
        mQTTMsgRecordMapper.delete(delRecordWrapper);
    }

    @Override
    public IPage<MQTTTopicDTO> topicPage(PagingReqDTO<MQTTTopicDTO> reqDTO) {
        //查询登录用户自己的列表数据
        String userId = UserUtils.getUserId();

        // 请求参数
        MQTTTopicDTO params = reqDTO.getParams();
        if (Objects.isNull(params) || StringUtils.isBlank(params.getProjectId())) {
            throw new ServiceException("请选择项目！");
        }
        //获得数据
        IPage<MQTTTopic> page = mQTTTopicMapper.selectPage(reqDTO.toPage(),
                Wrappers.<MQTTTopic>lambdaQuery()
                        .eq(MQTTTopic::getProjectId, params.getProjectId())
                        .eq(StringUtils.isNotBlank(userId), MQTTTopic::getCreateBy, userId)
                        .eq(StringUtils.isNotBlank(params.getName()), MQTTTopic::getName, params.getName())
                        .orderByDesc(MQTTTopic::getCreateTime)
        );

        //转换结果
        IPage<MQTTTopicDTO> pageData = JsonHelper.parseObject(page, new TypeReference<Page<MQTTTopicDTO>>() {
        });
        return pageData;
    }


    @Transactional
    @Override
    public void saveTopic(MQTTTopicDTO reqDTO) {
        String id = reqDTO.getId();
        //判断数据权限
        String projectId = reqDTO.getProjectId();
        MQTTProject mqttProject = mQTTProjectMapper.selectById(projectId);
        if (!mqttProject.getCreateBy().equals(UserUtils.getUserId())){
            throw new ServiceException(ApiError.ERROR_10010014);
        }
        if (StringUtils.isNotEmpty(id)) {
            MQTTTopic selectById = mQTTTopicMapper.selectById(id);
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, selectById);
            mQTTTopicMapper.updateById(selectById);
        } else {
            MQTTTopic mQTTTopic = new MQTTTopic();
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, mQTTTopic);
            String topic = getStringRandom(10);
            boolean isDuplicate = true;
            //判断数据库是否已经存在
            while (isDuplicate) {
                LambdaQueryWrapper<MQTTTopic> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MQTTTopic::getTopic, topic);
                Long count = mQTTTopicMapper.selectCount(queryWrapper);
                if (count == 0) {
                    isDuplicate = false;
                } else {
                    topic = getStringRandom(10);
                }
            }
            mQTTTopic.setTopic(topic);
            mQTTTopicMapper.insert(mQTTTopic);
            //计数
            this.updateMQTTProjectNum(reqDTO.getProjectId());
        }
    }


    @Transactional
    @Override
    public void delTopic(String id) {
        String userId = UserUtils.getUserId();
        MQTTTopic selectById = mQTTTopicMapper.selectById(id);
        if (null != selectById && !selectById.getCreateBy().equals(userId)) {
            throw new ServiceException("您不能删除他人数据！");
        }
        mQTTTopicMapper.deleteById(id);
        //计数
        this.updateMQTTProjectNum(selectById.getProjectId());
        //删除主题中的通讯记录
        LambdaQueryWrapper<MQTTMsgRecord> delRecordWrapper = new LambdaQueryWrapper<>();
        delRecordWrapper.eq(MQTTMsgRecord::getTopicId, id);
        mQTTMsgRecordMapper.delete(delRecordWrapper);
    }

    @Override
    public IPage<MQTTDeviceDTO> devicePage(PagingReqDTO<MQTTDeviceDTO> reqDTO) {
        //查询登录用户自己的列表数据
        String userId = UserUtils.getUserId();

        // 请求参数
        MQTTDeviceDTO params = reqDTO.getParams();
        if (Objects.isNull(params) || StringUtils.isBlank(params.getProjectId())) {
            throw new ServiceException("请选择项目！");
        }

        //获得数据
        IPage<MQTTDevice> page = mQTTDeviceMapper.selectPage(reqDTO.toPage(),
                Wrappers.<MQTTDevice>lambdaQuery()
                        .eq(MQTTDevice::getProjectId, params.getProjectId())
                        .eq(StringUtils.isNotBlank(userId), MQTTDevice::getCreateBy, userId)
                        .eq(StringUtils.isNotBlank(params.getName()), MQTTDevice::getName, params.getName())
                        .orderByDesc(MQTTDevice::getCreateTime)
        );

        //转换结果
        IPage<MQTTDeviceDTO> pageData = JsonHelper.parseObject(page, new TypeReference<Page<MQTTDeviceDTO>>() {
        });
        for (MQTTDeviceDTO mQTTDeviceDTO : pageData.getRecords()) {
            String id = mQTTDeviceDTO.getId();
            String clientId = mQTTDeviceDTO.getClientId();
            //查询订阅
            LambdaQueryWrapper<MQTTDeviceTopic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MQTTDeviceTopic::getDeviceId, id);
            List<MQTTDeviceTopic> mqttDeviceTopics = mQTTDeviceTopicMapper.selectList(queryWrapper);
            List<String> collect = mqttDeviceTopics.stream().map(MQTTDeviceTopic::getTopicId).collect(Collectors.toList());
            if (collect.size() != 0) {
                LambdaQueryWrapper<MQTTTopic> topicQueryWrapper = new LambdaQueryWrapper<>();
                topicQueryWrapper.in(MQTTTopic::getId, collect);
                List<MQTTTopic> topics = mQTTTopicMapper.selectList(topicQueryWrapper);
                mQTTDeviceDTO.setTopicList(topics);
            } else {
                mQTTDeviceDTO.setTopicList(new ArrayList<>());
            }
            //判断是否在线
            try {
                // 构建请求头
                Map<String, String> header = new HashMap<>();
                header.put("Authorization", "Bearer " + getEMQXToken());
                // 构建完整请求URL
                log.info("【emqx】获取设备是否在线请求参数：" + clientId);
                String httpResult = HttpRequestUtils.sendGetHeader(url + "/api/v5/clients/" + clientId, header);
                Map<String, Object> result = JSON.parseObject(httpResult, Map.class);
                log.info("【emqx】获取设备是否在线返回结果：" + result);
                String code = (String) result.getOrDefault("code", "");
                String message = (String) result.getOrDefault("message", "");
                if (StringUtils.isEmpty(code)) {
                    mQTTDeviceDTO.setIsOnline(1);
                } else {
                    mQTTDeviceDTO.setIsOnline(0);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return pageData;
    }


    @Transactional
    @Override
    public void saveDevice(MQTTDeviceDTO reqDTO) throws NoSuchAlgorithmException {
        String id = reqDTO.getId();
        //判断数据权限
        String projectId = reqDTO.getProjectId();
        MQTTProject mqttProject = mQTTProjectMapper.selectById(projectId);
        if (!mqttProject.getCreateBy().equals(UserUtils.getUserId())){
            throw new ServiceException(ApiError.ERROR_10010014);
        }
        if (StringUtils.isNotEmpty(id)) {
            MQTTDevice selectById = mQTTDeviceMapper.selectById(id);
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, selectById);
            mQTTDeviceMapper.updateById(selectById);
        } else {
            MQTTDevice mQTTDevice = new MQTTDevice();
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, mQTTDevice);
            String clientId = getStringRandom(8);
            String userName = getStringRandom(8);
            String password = getStringRandom(10);
            boolean isDuplicate = true;
            //判断数据库是否已经存在
            while (isDuplicate) {
                LambdaQueryWrapper<MQTTDevice> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MQTTDevice::getClientId, clientId).or().eq(MQTTDevice::getUserName, userName);
                Long count = mQTTDeviceMapper.selectCount(queryWrapper);
                if (count == 0) {
                    isDuplicate = false;
                } else {
                    clientId = getStringRandom(8);
                    userName = getStringRandom(8);
                }
            }
            mQTTDevice.setClientId(clientId);
            mQTTDevice.setUserName(userName);
            mQTTDevice.setPassword(password);
            Map<String, String> saltedSHA256Code = EncryptionUtils.getSaltedSHA256Code(password);
            if (saltedSHA256Code.containsKey("password")) {
                String passwordHash = saltedSHA256Code.get("password");
                String salt = saltedSHA256Code.get("salt");
                mQTTDevice.setPasswordHash(passwordHash);
                mQTTDevice.setSalt(salt);
            }
            mQTTDeviceMapper.insert(mQTTDevice);
            id = mQTTDevice.getId();
            //计数
            this.updateMQTTProjectNum(reqDTO.getProjectId());
        }
        //更新订阅消息
        List<String> topicIds = reqDTO.getTopicIds();
        if (topicIds != null) {
            //先删除
            LambdaQueryWrapper<MQTTDeviceTopic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MQTTDeviceTopic::getDeviceId, id);
            mQTTDeviceTopicMapper.delete(queryWrapper);
            //再添加
            for (String topicId : topicIds) {
                MQTTDeviceTopic mQTTDeviceTopic = new MQTTDeviceTopic();
                mQTTDeviceTopic.setDeviceId(id);
                mQTTDeviceTopic.setTopicId(topicId);
                mQTTDeviceTopicMapper.insert(mQTTDeviceTopic);
            }
        }
    }


    @Transactional
    @Override
    public void delDevice(String id) {
        String userId = UserUtils.getUserId();
        MQTTDevice mqttDevice = mQTTDeviceMapper.selectById(id);
        if (null != mqttDevice && !mqttDevice.getCreateBy().equals(userId)) {
            throw new ServiceException("您不能删除他人数据！");
        }
        mQTTDeviceMapper.deleteById(id);
        //计数
        this.updateMQTTProjectNum(mqttDevice.getProjectId());
        //删除设备中的通讯记录
        LambdaQueryWrapper<MQTTMsgRecord> delRecordWrapper = new LambdaQueryWrapper<>();
        delRecordWrapper.eq(MQTTMsgRecord::getClientId, mqttDevice.getClientId());
        mQTTMsgRecordMapper.delete(delRecordWrapper);
    }

    @Override
    public IPage<MQTTMsgRecord> msgRecordPage(PagingReqDTO<Map<String, String>> reqDTO) {
        //查询条件
        QueryWrapper<MQTTMsgRecord> wrapper = new QueryWrapper<>();
        // 请求参数
        Map<String, String> params = reqDTO.getParams();
        if (params != null) {
            if (!StringUtils.isEmpty(params.get("deviceId"))) {
                String deviceId = params.get("deviceId");
                MQTTDevice mqttDevice = mQTTDeviceMapper.selectById(deviceId);
                //判断数据权限
                if (!mqttDevice.getCreateBy().equals(UserUtils.getUserId())){
                    throw new ServiceException(ApiError.ERROR_10010014);
                }
                wrapper.lambda().eq(MQTTMsgRecord::getClientId, mqttDevice.getClientId());
            }
            if (!StringUtils.isEmpty(params.get("topicId"))) {
                String topicId = params.get("topicId");
                MQTTTopic mqttTopic = mQTTTopicMapper.selectById(topicId);
                //判断数据权限
                if (!mqttTopic.getCreateBy().equals(UserUtils.getUserId())){
                    throw new ServiceException(ApiError.ERROR_10010014);
                }
                wrapper.lambda().eq(MQTTMsgRecord::getTopicId, params.get("topicId"));
            }
            if (!StringUtils.isEmpty(params.get("name"))) {
                wrapper.lambda().eq(MQTTMsgRecord::getData, params.get("name"));
            }
            if (!StringUtils.isEmpty(params.get("type"))) {
                wrapper.lambda().eq(MQTTMsgRecord::getType, params.get("type"));
            }
            //筛选某个日期
            if (!StringUtils.isEmpty(params.get("date"))) {
                String dateString = params.get("date");
                LocalDate date = LocalDate.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE);
                wrapper.apply("DATE_FORMAT(create_time, '%Y-%m-%d') = {0}", date.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
        }
        wrapper.lambda().orderByDesc(MQTTMsgRecord::getCreateTime);
        //获得数据
        IPage<MQTTMsgRecord> page = mQTTMsgRecordMapper.selectPage(reqDTO.toPage(), wrapper);
        for (MQTTMsgRecord mqttMsgRecord : page.getRecords()) {
            String clientId = mqttMsgRecord.getClientId();
            //查询设备
            LambdaQueryWrapper<MQTTDevice> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(MQTTDevice::getClientId, clientId);
            lambdaQueryWrapper.last("limit 1");
            MQTTDevice mQTTDevice = mQTTDeviceMapper.selectOne(lambdaQueryWrapper);
            if (mQTTDevice != null)
                mqttMsgRecord.setClientName(mQTTDevice.getName());
            else
                mqttMsgRecord.setClientName("");
            //查询主题
            String topicId = mqttMsgRecord.getTopicId();
            MQTTTopic mqttTopic = mQTTTopicMapper.selectById(topicId);
            if (mqttTopic != null)
                mqttMsgRecord.setTopicName(mqttTopic.getName());
            else
                mqttMsgRecord.setTopicName("");
        }
        return page;
    }

    @Override
    public void delAllMsgRecord(String topicId) {
        MQTTTopic mqttTopic = mQTTTopicMapper.selectById(topicId);
        //判断数据权限
        if (!mqttTopic.getCreateBy().equals(UserUtils.getUserId())){
            throw new ServiceException(ApiError.ERROR_10010014);
        }
        LambdaQueryWrapper<MQTTMsgRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MQTTMsgRecord::getTopicId, topicId);
        mQTTMsgRecordMapper.delete(queryWrapper);
    }

    @Override
    public void sendMsgRecord(Map<String, String> param) {
        String topicId = param.getOrDefault("topicId", "");
        String data = param.getOrDefault("data", "");
        if (StringUtils.isEmpty(data))
            throw new ServiceException("发送内容不能为空！");
        MQTTTopic mqttTopic = mQTTTopicMapper.selectById(topicId);
        //判断数据权限
        if (!mqttTopic.getCreateBy().equals(UserUtils.getUserId())){
            throw new ServiceException(ApiError.ERROR_10010014);
        }
        int qos = 0;
        try {
            // 创建消息并设置 QoS
            MqttMessage message = new MqttMessage(data.getBytes());
            message.setQos(qos);
            System.out.println("emqxClient="+emqxClient.toString());
            // 发布消息
            emqxClient.publish(mqttTopic.getTopic(), message);
            System.out.println("Message published");
            System.out.println("topic: " + mqttTopic.getTopic());
            System.out.println("message content: " + data);
        } catch (MqttException e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional
    @Override
    public void webhookCollback(MqttMessageDTO param, HttpServletRequest request) {
        //保存记录
        MQTTTopic mqttTopic = this.saveMqttRecord(param,0, request);
        if (mqttTopic != null) {
            //使用数+1
            mQTTTopicMapper.updateNumAddOne(mqttTopic.getId());
        }
    }

    //保存记录
    @Nullable
    private MQTTTopic saveMqttRecord(MqttMessageDTO param,Integer type, HttpServletRequest request) {
        //鉴权
        String key = request.getHeader(Constant.KEY);
        if (!key.equals(emqxKey))
            throw new ServiceException("鉴权失败");
        System.out.println(param.toString());
        log.info("mqtt消息回调" + param.toString());
        String username =  param.getUsername();
        String topic = param.getTopic();
        String payload =  param.getPayload();
        String clientid =  param.getClientid();
        // 生成rediskey
        String redisKey = "mqtt:" + clientid + ":" + payload + ":" + type;
        //尝试获取限制锁，如果已存在则返回（即在1秒内同一clientid、payload、type禁止多次调用）
        boolean lock = redisService.tryLock(redisKey, 1000L,0,0L);
        if (!lock){
            throw new ServiceException("请求过于频繁");
        }
        String peerhost =  param.getPeerhost();
        //查询主题
        LambdaQueryWrapper<MQTTTopic> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MQTTTopic::getTopic, topic);
        lambdaQueryWrapper.last("limit 1");
        MQTTTopic mqttTopic = mQTTTopicMapper.selectOne(lambdaQueryWrapper);
        MQTTMsgRecord mQTTMsgRecord = new MQTTMsgRecord();
        if (mqttTopic != null) {
            mQTTMsgRecord.setTopicId(mqttTopic.getId());
            mQTTMsgRecord.setProjectId(mqttTopic.getProjectId());
        }
        mQTTMsgRecord.setTopic(topic);
        mQTTMsgRecord.setUsername(username);
        mQTTMsgRecord.setIp(peerhost);
        // 判断是否以 b' 开头，如果是则转换Unicode编码
        if (payload.startsWith("b'") && payload.endsWith("'")) {
            // 从字符串中剔除前面的"b'和最后的'"字符
            payload = payload.substring(2, payload.length() - 1);
            payload = convert(payload);
        }
        mQTTMsgRecord.setData(payload);
        mQTTMsgRecord.setClientId(clientid);
        mQTTMsgRecord.setType(type);
        mQTTMsgRecord.setCreateDate(new Date());
        mQTTMsgRecordMapper.insert(mQTTMsgRecord);
        return mqttTopic;
    }

    public static String convert(String byteString) {
        // 定义正则表达式来匹配转义的字节表示
        Pattern pattern = Pattern.compile("\\\\x([0-9A-Fa-f]{2})");
        Matcher matcher = pattern.matcher(byteString);

        // 准备一个输出流来存储字节
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        while (matcher.find()) {
            // 提取每个匹配到的字节部分
            String hexStr = matcher.group(1);
            int byteValue = Integer.parseInt(hexStr, 16);
            byteArrayOutputStream.write(byteValue);
        }

        // 将字节数组解析成UTF-8字符串
        return new String(byteArrayOutputStream.toByteArray(), StandardCharsets.UTF_8);
    }

    @Transactional
    @Override
    public void webhookAnswerBack(MqttMessageDTO param, HttpServletRequest request) {
        //保存记录
        this.saveMqttRecord(param,1, request);
    }

    @Override
    public MQTTStatisticsDTO findStatisticsTotal() {
        String userId = UserUtils.getUserId();
        MQTTStatisticsDTO data = new MQTTStatisticsDTO();
        //设备数
        LambdaQueryWrapper<MQTTDevice> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.eq(MQTTDevice::getCreateBy, userId);
        deviceQueryWrapper.isNotNull(MQTTDevice::getProjectId);
        Long deviceCount = mQTTDeviceMapper.selectCount(deviceQueryWrapper);
        data.setDeviceNum(deviceCount.intValue());
        //主题数
        LambdaQueryWrapper<MQTTTopic> topicQueryWrapper = new LambdaQueryWrapper<>();
        topicQueryWrapper.eq(MQTTTopic::getCreateBy, userId);
        topicQueryWrapper.isNotNull(MQTTTopic::getProjectId);
        List<MQTTTopic> mqttTopics = mQTTTopicMapper.selectList(topicQueryWrapper);
        List<String> collect = mqttTopics.stream().map(MQTTTopic::getId).collect(Collectors.toList());
        data.setTopicNum(mqttTopics.size());
        //项目数
        LambdaQueryWrapper<MQTTProject> projectQueryWrapper = new LambdaQueryWrapper<>();
        projectQueryWrapper.eq(MQTTProject::getCreateBy, userId);
        Long projectCount = mQTTProjectMapper.selectCount(projectQueryWrapper);
        data.setProjectNum(projectCount.intValue());
        //通信次数
        if (collect.size() != 0) {
            //发送类型
            LambdaQueryWrapper<MQTTMsgRecord> sendCommunicationQueryWrapper = new LambdaQueryWrapper<>();
            sendCommunicationQueryWrapper.in(MQTTMsgRecord::getTopicId, collect);
            sendCommunicationQueryWrapper.eq(MQTTMsgRecord::getType, 0);
            sendCommunicationQueryWrapper.isNotNull(MQTTMsgRecord::getProjectId);
            Long sendCommunicationCount = mQTTMsgRecordMapper.selectCount(sendCommunicationQueryWrapper);
            //应答类型
            LambdaQueryWrapper<MQTTMsgRecord> backCommunicationQueryWrapper = new LambdaQueryWrapper<>();
            backCommunicationQueryWrapper.in(MQTTMsgRecord::getTopicId, collect);
            backCommunicationQueryWrapper.eq(MQTTMsgRecord::getType, 1);
            backCommunicationQueryWrapper.isNotNull(MQTTMsgRecord::getProjectId);
            Long backCommunicationCount = mQTTMsgRecordMapper.selectCount(backCommunicationQueryWrapper);
            data.setSendCommunicationNum(sendCommunicationCount.intValue());
            data.setBackCommunicationNum(backCommunicationCount.intValue());
            //累加总数
            data.setCommunicationNum(sendCommunicationCount.intValue()+backCommunicationCount.intValue());
        } else {
            data.setCommunicationNum(0);
        }
        return data;
    }

    @Override
    public List<MQTTStatisticsDTO> findRecordStatisticsNum(MQTTRecordStatisticsDTO reqDTO) {
        String userId = UserUtils.getUserId();
        String topicId = reqDTO.getTopicId();
        List<String> topicIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(topicId)) {
            topicIds.add(topicId);
        } else {
            //主题数
            LambdaQueryWrapper<MQTTTopic> topicQueryWrapper = new LambdaQueryWrapper<>();
            topicQueryWrapper.eq(MQTTTopic::getCreateBy, userId);
            topicQueryWrapper.isNotNull(MQTTTopic::getProjectId);
            List<MQTTTopic> mqttTopics = mQTTTopicMapper.selectList(topicQueryWrapper);
            topicIds = mqttTopics.stream().map(MQTTTopic::getId).collect(Collectors.toList());
        }
        //发送和应答统计数组
        reqDTO.setTopicIds(topicIds);
        List<MQTTStatisticsDTO> data = mQTTMsgRecordMapper.findRecordStatisticsNum(reqDTO);
        return data;
    }

    @Override
    public List<MQTTTopic> findTopicStatistics(List<String> ids) {
        String userId = UserUtils.getUserId();
        if (ids.size() == 0) {
            ids.add("1");
        }
        //主题数
        LambdaQueryWrapper<MQTTTopic> topicQueryWrapper = new LambdaQueryWrapper<>();
        topicQueryWrapper.eq(MQTTTopic::getCreateBy, userId);
        topicQueryWrapper.in(MQTTTopic::getId, ids);
        List<MQTTTopic> mqttTopics = mQTTTopicMapper.selectList(topicQueryWrapper);
        for (MQTTTopic mQTTTopic:mqttTopics) {
            //发送类型
            LambdaQueryWrapper<MQTTMsgRecord> sendCommunicationQueryWrapper = new LambdaQueryWrapper<>();
            sendCommunicationQueryWrapper.eq(MQTTMsgRecord::getTopicId, mQTTTopic.getId());
            sendCommunicationQueryWrapper.eq(MQTTMsgRecord::getType, 0);
            Long sendCommunicationCount = mQTTMsgRecordMapper.selectCount(sendCommunicationQueryWrapper);
            //应答类型
            LambdaQueryWrapper<MQTTMsgRecord> backCommunicationQueryWrapper = new LambdaQueryWrapper<>();
            backCommunicationQueryWrapper.eq(MQTTMsgRecord::getTopicId, mQTTTopic.getId());
            backCommunicationQueryWrapper.eq(MQTTMsgRecord::getType, 1);
            Long backCommunicationCount = mQTTMsgRecordMapper.selectCount(backCommunicationQueryWrapper);
            mQTTTopic.setSendCommunicationNum(sendCommunicationCount.intValue());
            mQTTTopic.setBackCommunicationNum(backCommunicationCount.intValue());
        }
        return mqttTopics;
    }

    @Override
    public MQTTStatisticsDTO findTopicStatisticsById(String topicId) {
        MQTTStatisticsDTO data = new MQTTStatisticsDTO();
        MQTTTopic mqttTopic = mQTTTopicMapper.selectById(topicId);
        //判断数据权限
        if (!mqttTopic.getCreateBy().equals(UserUtils.getUserId())){
            throw new ServiceException(ApiError.ERROR_10010014);
        }
        //发送类型
        LambdaQueryWrapper<MQTTMsgRecord> sendCommunicationQueryWrapper = new LambdaQueryWrapper<>();
        sendCommunicationQueryWrapper.eq(MQTTMsgRecord::getTopicId, topicId);
        sendCommunicationQueryWrapper.eq(MQTTMsgRecord::getType, 0);
        Long sendCommunicationCount = mQTTMsgRecordMapper.selectCount(sendCommunicationQueryWrapper);
        //应答类型
        LambdaQueryWrapper<MQTTMsgRecord> backCommunicationQueryWrapper = new LambdaQueryWrapper<>();
        backCommunicationQueryWrapper.eq(MQTTMsgRecord::getTopicId, topicId);
        backCommunicationQueryWrapper.eq(MQTTMsgRecord::getType, 1);
        Long backCommunicationCount = mQTTMsgRecordMapper.selectCount(backCommunicationQueryWrapper);

        data.setSendCommunicationNum(sendCommunicationCount.intValue());
        data.setBackCommunicationNum(backCommunicationCount.intValue());
        return data;
    }

    @Override
    public List<MQTTDevice> findDeviceStatisticsByTopic(String topicId) {
        MQTTTopic mqttTopic = mQTTTopicMapper.selectById(topicId);
        //判断数据权限
        if (!mqttTopic.getCreateBy().equals(UserUtils.getUserId())){
            throw new ServiceException(ApiError.ERROR_10010014);
        }
        List<MQTTDevice> deviceList = mQTTMsgRecordMapper.findDeviceStatisticsByTopic(topicId);
        return deviceList;
    }

    @Override
    public List<MQTTUserView> viewList(String deviceId) {
        String userId = UserUtils.getUserId();
        //视图数据
        LambdaQueryWrapper<MQTTUserView> topicQueryWrapper = new LambdaQueryWrapper<>();
        topicQueryWrapper.eq(MQTTUserView::getDeviceId, deviceId);
        topicQueryWrapper.eq(MQTTUserView::getCreateBy, userId);
        List<MQTTUserView> data = mqttUserViewMapper.selectList(topicQueryWrapper);
        return data;
    }

    @Override
    public void saveUserView(MQTTUserView reqDTO) {
        String id = reqDTO.getId();
        /*String deviceId = reqDTO.getDeviceId();
        MQTTDevice mqttDevice = mQTTDeviceMapper.selectById(deviceId);
        //判断数据权限
        if (!mqttDevice.getCreateBy().equals(UserUtils.getUserId())){
            throw new ServiceException(ApiError.ERROR_10010014);
        }*/
        if (StringUtils.isNotEmpty(id)) {
            MQTTUserView selectById = mqttUserViewMapper.selectById(id);
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, selectById);
            mqttUserViewMapper.updateById(selectById);
        } else {
            mqttUserViewMapper.insert(reqDTO);
        }
    }

    @Override
    public void delUserView(String id) {
        MQTTUserView mqttUserView = mqttUserViewMapper.selectById(id);
        //判断数据权限
        if (!mqttUserView.getCreateBy().equals(UserUtils.getUserId())){
            throw new ServiceException(ApiError.ERROR_10010014);
        }
        mqttUserViewMapper.deleteById(id);
    }

    @Override
    public List<MQTTTopic> findTopicListOrderByLately() {
        String userId = UserUtils.getUserId();
        //主题列表
        LambdaQueryWrapper<MQTTTopic> topicQueryWrapper = new LambdaQueryWrapper<>();
        topicQueryWrapper.eq(MQTTTopic::getCreateBy, userId);
        topicQueryWrapper.isNotNull(MQTTTopic::getProjectId);
        List<MQTTTopic> mqttTopics = mQTTTopicMapper.selectList(topicQueryWrapper);
        for (MQTTTopic mqttTopic:mqttTopics) {
            String id = mqttTopic.getId();
            //查询设备
            LambdaQueryWrapper<MQTTMsgRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(MQTTMsgRecord::getId,MQTTMsgRecord::getTopicId,MQTTMsgRecord::getCreateTime);
            lambdaQueryWrapper.eq(MQTTMsgRecord::getTopicId, id);
            lambdaQueryWrapper.orderByDesc(MQTTMsgRecord::getCreateTime);
            lambdaQueryWrapper.last("limit 1");
            MQTTMsgRecord mqttMsgRecord = mQTTMsgRecordMapper.selectOne(lambdaQueryWrapper);
            if (mqttMsgRecord != null) {
                mqttTopic.setLastDate(mqttMsgRecord.getCreateTime());
            } else {
                Date date = new Date(1900 - 1900, 0, 1);
                mqttTopic.setLastDate(date);
            }
        }
        mqttTopics = mqttTopics.stream()
                .sorted(Comparator.comparing(MQTTTopic::getLastDate).reversed())
                .collect(Collectors.toList());
        return mqttTopics;
    }

    @Override
    public Map<String, Object> findProjectIPAndPort() {
        Map<String, Object> data = new HashMap<>();
        data.put("ip", ip);
        data.put("port", port);
        data.put("ws_port", ws_port);
        return data;
    }

    @Override
    public Object topicQuery(String user, String psd, String topic, Integer limit) {
        //查询设备
        LambdaQueryWrapper<MQTTDevice> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MQTTDevice::getUserName, user);
        lambdaQueryWrapper.eq(MQTTDevice::getPassword, psd);
        lambdaQueryWrapper.last("limit 1");
        MQTTDevice mqttDevice = mQTTDeviceMapper.selectOne(lambdaQueryWrapper);
        if (mqttDevice == null) {
            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("reason", "用户名或密码错误！");
            return objectObjectHashMap;
        }
        //查询条件
        LambdaQueryWrapper<MQTTMsgRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MQTTMsgRecord::getTopic, topic);
        queryWrapper.orderByDesc(MQTTMsgRecord::getCreateTime).last(" LIMIT " + limit);
        //获得数据
        List<MQTTMsgRecord> mqttMsgRecords = mQTTMsgRecordMapper.selectList(queryWrapper);
        return mqttMsgRecords;
    }

    //更新mqtt项目计数
    public void updateMQTTProjectNum(String projectId) {
        mQTTProjectMapper.updateProjectNumById(projectId);
    }

    //获取emqx登录token
    public String getEMQXToken() {
        //获取emqx登录token,先判断缓存是否存在
        String token = redisService.getString("emqx-token");
        if (StringUtils.isEmpty(token)) {
            try {
                // 构建完整请求URL
                Map<String, Object> map = new HashMap<>();
                map.put("username", username);
                map.put("password", password);
                String httpResult = HttpRequestUtils.sendPostJson(url + "/api/v5/login", new HashMap<>(), map);
                Map<String, Object> result = JSON.parseObject(httpResult, Map.class);
                token = (String) result.get("token");
                redisService.set("emqx-token", token, 3000L);
                log.info("【emqx】成功获取到token：" + token);
                return token;
            } catch (Exception e) {
                log.error("【emqx】获取token时发生错误" + e);
                e.printStackTrace();
                return null;
            }
        } else {
            return token;
        }
    }

    public static String getStringRandom(int length) {
        String val = "";
        Random random = new Random();
        // 参数length，表示生成几位随机数
        for (int i = 0; i < length; i++) {
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                // 输出是大写字母还是小写字母
                int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (random.nextInt(26) + temp);
            } else if ("num".equalsIgnoreCase(charOrNum)) {
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }



    @Transactional
    @Override
    public MQTTDeviceVO saveAndSearchDevice(MQTTDeviceDTO reqDTO) throws NoSuchAlgorithmException {
        MQTTDeviceVO mQTTDeviceVO = new MQTTDeviceVO();
        String userId = UserUtils.getUserId();
        String name = reqDTO.getName();
        //查询设备名
        LambdaQueryWrapper<MQTTDevice> queryDeviceWrapper = new LambdaQueryWrapper<>();
        queryDeviceWrapper.eq(MQTTDevice::getCreateBy, userId);
        queryDeviceWrapper.eq(MQTTDevice::getName, name);
        queryDeviceWrapper.last(" LIMIT 1");
        MQTTDevice mqttDevice = mQTTDeviceMapper.selectOne(queryDeviceWrapper);
        if (mqttDevice != null) {
            CopyUtils.copyPropertiesIgnoreNull(mqttDevice,mQTTDeviceVO);
            return mQTTDeviceVO;
        } else {
            MQTTDevice mQTTDevice = new MQTTDevice();
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, mQTTDevice);
            String clientId = getStringRandom(8);
            String userName = getStringRandom(8);
            String password = getStringRandom(10);
            boolean isDuplicate = true;
            //判断数据库是否已经存在
            while (isDuplicate) {
                LambdaQueryWrapper<MQTTDevice> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MQTTDevice::getClientId, clientId).or().eq(MQTTDevice::getUserName, userName);
                Long count = mQTTDeviceMapper.selectCount(queryWrapper);
                if (count == 0) {
                    isDuplicate = false;
                } else {
                    clientId = getStringRandom(8);
                    userName = getStringRandom(8);
                }
            }
            mQTTDevice.setClientId(clientId);
            mQTTDevice.setUserName(userName);
            mQTTDevice.setPassword(password);
            Map<String, String> saltedSHA256Code = EncryptionUtils.getSaltedSHA256Code(password);
            if (saltedSHA256Code.containsKey("password")) {
                String passwordHash = saltedSHA256Code.get("password");
                String salt = saltedSHA256Code.get("salt");
                mQTTDevice.setPasswordHash(passwordHash);
                mQTTDevice.setSalt(salt);
            }
            mQTTDeviceMapper.insert(mQTTDevice);
            CopyUtils.copyPropertiesIgnoreNull(mQTTDevice,mQTTDeviceVO);
            return mQTTDeviceVO;
        }
    }


    @Transactional
    @Override
    public MQTTTopicVO saveAndSearchTopic(MQTTTopicDTO reqDTO) {
        MQTTTopicVO mQTTTopicVO = new MQTTTopicVO();
        String userId = UserUtils.getUserId();
        String name = reqDTO.getName();
        //查询设备名
        LambdaQueryWrapper<MQTTTopic> queryTopicWrapper = new LambdaQueryWrapper<>();
        queryTopicWrapper.eq(MQTTTopic::getCreateBy, userId);
        queryTopicWrapper.eq(MQTTTopic::getName, name);
        queryTopicWrapper.last(" LIMIT 1");
        MQTTTopic mQTTTopicOne = mQTTTopicMapper.selectOne(queryTopicWrapper);
        if (mQTTTopicOne != null) {
            CopyUtils.copyPropertiesIgnoreNull(mQTTTopicOne,mQTTTopicVO);
            return mQTTTopicVO;
        } else {
            MQTTTopic mQTTTopic = new MQTTTopic();
            CopyUtils.copyPropertiesIgnoreNull(reqDTO, mQTTTopic);
            String topic = getStringRandom(10);
            boolean isDuplicate = true;
            //判断数据库是否已经存在
            while (isDuplicate) {
                LambdaQueryWrapper<MQTTTopic> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MQTTTopic::getTopic, topic);
                Long count = mQTTTopicMapper.selectCount(queryWrapper);
                if (count == 0) {
                    isDuplicate = false;
                } else {
                    topic = getStringRandom(10);
                }
            }
            mQTTTopic.setTopic(topic);
            mQTTTopicMapper.insert(mQTTTopic);
            CopyUtils.copyPropertiesIgnoreNull(mQTTTopic,mQTTTopicVO);
            return mQTTTopicVO;
        }
    }
}

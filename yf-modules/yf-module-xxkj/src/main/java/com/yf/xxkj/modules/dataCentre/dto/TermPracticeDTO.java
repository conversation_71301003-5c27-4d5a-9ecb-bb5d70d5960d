package com.yf.xxkj.modules.dataCentre.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <p>
 * 随堂练评价
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "随堂练评价", description = "随堂练评价")
public class TermPracticeDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "优秀")
    private Integer excellent;

    @ApiModelProperty(value = "良好")
    private Integer good;

    @ApiModelProperty(value = "差")
    private Integer medium;

    @ApiModelProperty(value = "差")
    private Integer poor;

    @ApiModelProperty(value = "无成绩")
    private Integer noScore;


    @ApiModelProperty(value = "成绩")
    private String score;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "率")
    private BigDecimal rate;
}

package com.yf.xxkj.modules.dataCentre.dto;


import com.yf.system.modules.user.entity.SysUser;
import com.yf.xxkj.modules.classManagement.dto.StudentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 出勤率统计
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "出勤率统计", description = "出勤率统计")
public class AttendanceRankDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "班级id")
    private String deptId;

    @ApiModelProperty(value = "班级名称")
    private String deptName;

    @ApiModelProperty(value = "实到")
    private Integer factCount;

    @ApiModelProperty(value = "应到")
    private Integer allCount;

    @ApiModelProperty(value = "数量")
    private BigDecimal rate;


    @ApiModelProperty(value = "未到数")
    private Integer absentCount;

    @ApiModelProperty(value = "实到")
    private List<StudentDTO> attendUsers;

    @ApiModelProperty(value = "未到")
    private List<StudentDTO> absentUsers;
}

package com.yf.xxkj.modules.award.controller;


import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import com.yf.xxkj.modules.award.dto.InteractionRecordDTO;
import com.yf.xxkj.modules.award.dto.ReplyDTO;
import com.yf.xxkj.modules.award.service.InteractionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/***
 * @title 互动操作接口
 * @Description
 * <AUTHOR>
 **/
@Api(tags = {"互动操作接口"})
@RestController
@RequestMapping("/api/xxkj/interaction")
public class InteractionController extends BaseController {

    @Autowired
    private InteractionService interactionService;


    /**
     * 记录互动
     * @param reqDTO
     */
    @ApiOperation(value = "记录互动")
    @PostMapping("/saveInteractionRecord")
    public ApiRest saveInteractionRecord(@RequestBody InteractionRecordDTO reqDTO) {
        interactionService.saveInteractionRecord(reqDTO);
        return super.success();
    }


    /**
     * 抢答成绩
     * @param reqDTO
     */
    @ApiOperation(value = "抢答成绩")
    @PostMapping("/saveReplyScore")
    public ApiRest saveReplyScore(@RequestBody ReplyDTO reqDTO) {
        interactionService.saveReplyScore(reqDTO);
        return super.success();
    }

}

package com.yf.xxkj.job;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yf.course.modules.course.service.CourseFileService;
import com.yf.course.modules.course.service.OfficeWeb365Service;
import com.yf.system.modules.task.entity.SchedulerTask;
import com.yf.system.modules.task.mapper.SchedulerTaskMapper;
import com.yf.system.utils.DateUtils;
import com.yf.xxkj.modules.clazz.service.ClazzService;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.util.Date;

/**
 * office web 365 解析ppt
 * <AUTHOR>
 */
@Log4j2
@Component
public class ParsePptx365Job implements Job {

    @Autowired
    private CourseFileService courseFileService;

    @Resource
    private SchedulerTaskMapper schedulerTaskMapper;

    @SneakyThrows
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {


        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        String group = detail.getKey().getGroup();

        log.info("++++++++++office web 365 解析ppt");
        log.info("++++++++++jobName:{}", name);
        log.info("++++++++++jobGroup:{}", group);

        //让程序睡5-20秒，避免多个定时任务出现幻读，多次执行
        Long aLong = Long.valueOf((int)(Math.random()*(19999-5000+1)+5000));
        Thread.sleep(aLong);
        //获取当前年月日时间戳
        Long time = System.currentTimeMillis();
        LambdaQueryWrapper<SchedulerTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SchedulerTask::getName, name);
        SchedulerTask schedulerTask = schedulerTaskMapper.selectOne(queryWrapper);
        Long timestamp = schedulerTask.getTimestamp();
        //如果时间戳不一样
        if (60000<=(time - timestamp)){
            schedulerTask.setTimestamp(time);
            schedulerTask.setIp(InetAddress.getLocalHost().getHostAddress());
            schedulerTaskMapper.insertOrUpdate(schedulerTask);
            try {
                courseFileService.resetParsePptx();
            } catch (Exception e) {
                e.printStackTrace();
                schedulerTask.setTimestamp(timestamp);
                schedulerTaskMapper.insertOrUpdate(schedulerTask);
            }
        }else {
            log.info("office web 365 解析ppt任务已经执行过！");
        }
    }


}

package com.yf.xxkj.modules.award.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 互动记录
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "互动记录", description = "互动记录")
public class InteractionRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "老师", required = true)
    private String userId;

    @ApiModelProperty(value = "学生id", required = true)
    private String studentId;

    @ApiModelProperty(value = "课堂id", required = true)
    private String clazzId;

    @ApiModelProperty(value = "类型 1-提交随堂练，2-举手 ,3-抢答", required = true)
    private Integer type;

    @ApiModelProperty(value = "年级", required = true)
    private String grade;

    @ApiModelProperty(value = "成绩", required = true)
    private Integer score;
}

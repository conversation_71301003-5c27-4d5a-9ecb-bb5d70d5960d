package com.yf.xxkj.modules.dataCentre.controller;

import com.yf.ability.Constant;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import com.yf.base.api.exception.ServiceException;
import com.yf.xxkj.modules.dataCentre.entity.ReportUserDayActive;
import com.yf.xxkj.modules.dataCentre.service.ReportUserDayActiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 用户日活报表
 * </p>
 * <AUTHOR>
 * @since 2024-09-10
 */
@Api(tags = {"用户日活报表"})
@RestController
@RequestMapping("/api/xxkj/report/user/day/active")
public class ReportUserDayActiveController extends BaseController {

    @Resource
    private ReportUserDayActiveService reportUserDayActiveService;
    
    @Value("${emqx.key}")
    private String datavKey;

    /**
     * 执行统计日活数据
     * @param date 指定日期。
     * @return 操作结果
     */
    @PostMapping("/{date}")
    public ApiRest<?> exeCount(@PathVariable String date, HttpServletRequest request) {
        //鉴权
        String key = request.getHeader(Constant.KEY);
        if (StringUtils.isEmpty(key) || !key.equals(datavKey))
            throw new ServiceException("鉴权失败");
        reportUserDayActiveService.exeCountDayActiveInfo(LocalDate.parse(date));
        return super.success();
    }

    /**
     * 查询近{days}日的日活数据
     * @param role 用户角色
     * @param days 近几日
     * @return 报表列表数据
     */
    @ApiOperation(value = "查询近｛days｝日的用户活跃数据")
    @GetMapping("/{role}/{days}")
    public List<ReportUserDayActive> listNearDays(@PathVariable String role, @PathVariable Integer days, HttpServletRequest request) {
        //鉴权
        String key = request.getHeader(Constant.KEY);
        if (StringUtils.isEmpty(key) || !key.equals(datavKey))
            throw new ServiceException("鉴权失败");
        return reportUserDayActiveService.listNearDayActive(role, days);
    }

}

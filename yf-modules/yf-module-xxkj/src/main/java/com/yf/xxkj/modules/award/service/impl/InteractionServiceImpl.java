package com.yf.xxkj.modules.award.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.base.utils.BeanMapper;
import com.yf.system.modules.depart.entity.SysDepart;
import com.yf.system.modules.depart.service.SysDepartService;
import com.yf.system.modules.dict.service.SysDicValueService;
import com.yf.system.modules.user.UserUtils;
import com.yf.system.modules.user.entity.SysUser;
import com.yf.system.modules.user.service.SysUserService;
import com.yf.xxkj.modules.award.dto.InteractionRecordDTO;
import com.yf.xxkj.modules.award.dto.ReplyDTO;
import com.yf.xxkj.modules.award.entity.InteractionRecord;
import com.yf.xxkj.modules.award.mapper.InteractionRecordMapper;
import com.yf.xxkj.modules.award.service.InteractionService;
import com.yf.xxkj.modules.clazz.entity.Clazz;
import com.yf.xxkj.modules.clazz.mapper.ClazzMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/***
 * @title InteractionServiceImpl
 * @Description
 * <AUTHOR>
 **/
@Service
public class InteractionServiceImpl extends ServiceImpl<InteractionRecordMapper, InteractionRecord> implements InteractionService {

    @Autowired
    private SysDepartService sysDepartService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysDicValueService sysDicValueService;

    @Autowired
    private ClazzMapper clazzMapper;

    @Override
    public void saveInteractionRecord(InteractionRecordDTO reqDTO) {
        InteractionRecord interactionRecord = new InteractionRecord();
        BeanMapper.copy(reqDTO, interactionRecord);
        String clazzId = reqDTO.getClazzId();
        if (StringUtils.isNotBlank(clazzId)) {
            Clazz clazz = clazzMapper.selectById(clazzId);
            if (clazz != null && StringUtils.isNotBlank(clazz.getGradeValue())) {
                interactionRecord.setGradeValue(clazz.getGradeValue());
                interactionRecord.setGradeValueId(clazz.getGradeValueId());
            }
            interactionRecord.setGrade(clazz.getGrade());
        } else {
            //此时学生的年级
            SysUser sysUser = sysUserService.getById(interactionRecord.getStudentId());
            String deptCode = sysUser.getDeptCode();
            SysDepart deptByCode = sysDepartService.findDeptByCode(deptCode, 3);
            if (deptByCode != null && StringUtils.isNotBlank(deptByCode.getGradeValue())) {
                String gradeValue = deptByCode.getGradeValue();
                String grade = deptByCode.getGrade();
                if (StringUtils.isBlank(grade)){
                    grade = sysDicValueService.findDictText("grade", gradeValue);
                }
                interactionRecord.setGrade(grade);
            }
        }
        this.save(interactionRecord);
    }

    @Override
    public void saveReplyScore(ReplyDTO reqDTO) {
        String clazzId = reqDTO.getClazzId();
        Clazz clazz = clazzMapper.selectById(clazzId);
        Integer type = reqDTO.getType();
        List<InteractionRecordDTO> students = reqDTO.getStudents();
        for (InteractionRecordDTO student : students) {
            String studentId = student.getStudentId();
            Integer score = student.getScore();
            InteractionRecord interactionRecord = new InteractionRecord();
            interactionRecord.setClazzId(clazzId);
            interactionRecord.setUserId(UserUtils.getUserId());
            interactionRecord.setStudentId(studentId);
            interactionRecord.setGrade(clazz.getGrade());
            interactionRecord.setType(type);
            interactionRecord.setScore(score);
            if (StringUtils.isNotBlank(clazz.getGradeValue())){
                interactionRecord.setGradeValue(clazz.getGradeValue());
                interactionRecord.setGradeValueId(clazz.getGradeValueId());
            }
            this.save(interactionRecord);
        }

    }
}

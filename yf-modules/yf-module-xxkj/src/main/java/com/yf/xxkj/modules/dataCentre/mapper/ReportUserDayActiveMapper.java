package com.yf.xxkj.modules.dataCentre.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yf.xxkj.modules.dataCentre.dto.ReportUserDayActiveDTO;
import com.yf.xxkj.modules.dataCentre.entity.ReportUserDayActive;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 用户日活报表mapper
 * </p>
 * <AUTHOR>
 * @since 2024-09-10
 */
@Repository
public interface ReportUserDayActiveMapper extends BaseMapper<ReportUserDayActive> {

    /**
     * 统计指定日期的数据
     * @param param 指定条件
     * @return 返回日活数据统计结果
     */
    List<ReportUserDayActive> countDayActiveInfo(@Param("param") ReportUserDayActiveDTO param);

}

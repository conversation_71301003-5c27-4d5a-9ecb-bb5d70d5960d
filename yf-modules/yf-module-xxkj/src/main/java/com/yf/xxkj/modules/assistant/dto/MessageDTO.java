package com.yf.xxkj.modules.assistant.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * AI客服消息
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AI客服消息", description = "AI客服消息")
public class MessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "会话id")
    private String conversation_id;

    @ApiModelProperty(value = "问题", required = true)
    private String query;

    @ApiModelProperty(value = "知识库检索内容", required = true)
    private String content;

    @ApiModelProperty(value = "标题", required = true)
    private String title;

    @ApiModelProperty(value = "章节目录id", required = true)
    private String dirId;

    @ApiModelProperty(value = "图片上传id", required = true)
    private String imageId;

    @ApiModelProperty(value = "0 是自由问答，1 是相关知识点讲解，2 是教学活动设计，3 是思维导图|0 代表解读，1 代表总结，2 代表解析，3 代表自由 ")
    private Integer type;

    @ApiModelProperty(value = "思考速度 ：0 quick，1 slow")
    private Integer thinkType;

    @ApiModelProperty(value = "trainId")
    private String trainId;

    @ApiModelProperty(value = "编创平台：数字教材ID")
    private String bookId;

    @ApiModelProperty(value = "编创平台：学科维值ID")
    private String courseId;
}

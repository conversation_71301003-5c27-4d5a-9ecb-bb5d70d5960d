package com.yf.xxkj.modules.dataCentre.service;

import com.yf.system.modules.dict.enums.RegionLevelEnum;
import com.yf.xxkj.modules.dataCentre.vo.ReportUserRegionVO;

import java.util.List;

public interface ReportRegionUserService {

    /**
     * 查询全国指定级别部门的用户统计数据
     * @param regionLevelEnum 行政区域层级
     * @param province 根据省的选择，展现这个省下面城市的用户数
     * @return 用户统计数据
     */
    List<ReportUserRegionVO> listRegionUserCount(RegionLevelEnum regionLevelEnum,String province);



    /**
     * 查询全国指定级别部门的用户角色统计数据
     * @param regionLevelEnum 行政区域层级
     * @param province 根据省的选择，展现这个省下面城市的用户数
     * @param role 角色
     * @return 用户统计数据
     */
    List<ReportUserRegionVO> listRegionUserRoleCount(RegionLevelEnum regionLevelEnum,String province,String role);


    /**
     * 查询全国指定级别部门的用户角色统计数据
     * @param province
     * @return 用户统计数据
     */
    List<ReportUserRegionVO> userRoleCount(String province);
}

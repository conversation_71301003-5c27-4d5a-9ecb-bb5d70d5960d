package com.yf.xxkj.modules.order.dto;



import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.yf.base.api.annon.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 信息科技订单传输类
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "信息科技订单传输类", description = "信息科技订单传输类")
public class AgencyOrderDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id", required = true)
    private String id;

    @ApiModelProperty(value = "名称", required = true)
    private String title;

    @ApiModelProperty(value = "省", required = true)
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "县")
    private String county;

    @ApiModelProperty(value = "6-教材 ,7-实验室", required = true)
    private Integer type;

    @ApiModelProperty(value = "教师数", required = true)
    private Integer teacherCount;

    @ApiModelProperty(value = "学生数", required = true)
    private Integer studentCount;

    @ApiModelProperty(value = "订单失效时间", required = true)
    private Date expirationDate;;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @Dict(dictTable = "el_sys_user", dicText = "real_name", dicCode = "id")
    @ApiModelProperty(value = "创建人")
    private String createBy;


    @ApiModelProperty(value = "是否所有册别")
    private Integer teacherAllVolumes;

    @ApiModelProperty(value = "是否所有册别")
    private Integer studentAllVolumes;

    @ApiModelProperty(value = "教师可用册别")
    private List<String> teacherValueIds;

    @ApiModelProperty(value = "学生可用册别")
    private List<String> studentValueIds;


    @ApiModelProperty(value = "教师账号有效期类型  1-永久有效，2-指定日期 3-指定年限")
    private Integer teacherAccountType;

    @ApiModelProperty(value = "教师账号teacherAccountType=3 指定年限")
    private Integer teacherYearCount;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "教师账号有效期", required = true)
    private Date teacherAccountExpirationDate;


    @ApiModelProperty(value = "学生账号有效期类型  1-永久有效，2-指定日期 3-指定年限")
    private Integer studentAccountType;


    @ApiModelProperty(value = "学生账号teacherAccountType=3 指定年限")
    private Integer studentYearCount;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "学生账号有效期", required = true)
    private Date studentAccountExpirationDate;


    @ApiModelProperty(value = "教师账号订单状态 0-正常，1-过期")
    private Integer teacherState;


    @ApiModelProperty(value = "学生账号订单状态 0-正常，1-过期")
    private Integer studentState;

    @ApiModelProperty(value = "存在用户信息")
    private Boolean haveUser;

    @ApiModelProperty(value = "教师端导入功能权限 0-无，1-有")
    private Integer importAccess;

    @ApiModelProperty(value = "AI助手使用权限 0-无，1-有")
    private Integer assistantAccess;

    @ApiModelProperty(value = "已添加学生数")
    private Integer studentAddedCount;

    @ApiModelProperty(value = "已添加教师数")
    private Integer teacherAddedCount;



    @ApiModelProperty(value = "SAP订单号")
    private String sapOrderId;

    @ApiModelProperty(value = "订单起始日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date sapOrderStartDate;

    @ApiModelProperty(value = "订单结束日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date sapOrderEndDate;

    @ApiModelProperty(value = "订单类型")
    private String sapType;

    @ApiModelProperty(value = "申请人")
    private String sapApplyUser;

    @ApiModelProperty(value = "SAP订单名称")
    private String sapOrderTitle;

}

package com.yf.xxkj.modules.mqtt.vo;

import com.yf.xxkj.modules.mqtt.entity.MQTTTopic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * MQTT设备数据返回类
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "MQTT设备数据返回类", description = "MQTT设备数据返回类")
public class MQTTDeviceVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true)
    private String projectId;


    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", required = true)
    private String name;

    /**
     * clientID
     */
    @ApiModelProperty(value = "clientID")
    private String clientId;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;


}

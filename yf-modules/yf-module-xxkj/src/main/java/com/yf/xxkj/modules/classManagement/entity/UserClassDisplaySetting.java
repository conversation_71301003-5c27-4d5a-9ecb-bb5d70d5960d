package com.yf.xxkj.modules.classManagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 用户班级展示设置
 * </p>
 * <AUTHOR>
 */
@Data
@TableName(value = "xk_user_class_display_setting")
public class UserClassDisplaySetting extends Model<UserClassDisplaySetting> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    @TableField
    private String userId;

    /**
     * 展示方式，0-默认按组，1-全班列表
     */
    @TableField
    private Integer showType;

    /**
     * 排序方式 0-默认按账号排序,1-学生姓名，2-小红花数量 ，3-添加顺序（id）
     */
    @TableField
    private Integer sortType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date updateTime;
} 
# yf-module-xxkj 模块交接文档

## 1. 模块概述

### 1.1 模块简介
`yf-module-xxkj` 是信息科技教育管理系统的核心模块，主要服务于中小学信息科技教育场景。该模块提供了完整的教学管理、学习跟踪、数据分析等功能。

### 1.2 主要功能模块
- **年级班级管理** - 学校、年级、班级的层级管理
- **用户管理** - 教师和学生用户的管理
- **课堂授课** - 在线课堂的开设和管理
- **课堂作业** - 作业的发布、提交和批改
- **AI助手** - 智能出题、内容分析等AI功能
- **奖励系统** - 学生激励机制（小红花）
- **数据中心** - 教学数据统计和分析
- **序列号管理** - 硬件设备序列号管理
- **订单管理** - 信息科技相关订单处理
- **MQTT管理** - 物联网设备通信管理
- **天气预报** - 天气信息服务

### 1.3 技术架构
- **框架**: Spring Boot + MyBatis Plus
- **数据库**: MySQL
- **消息队列**: MQTT (EMQX)
- **缓存**: Redis
- **文件存储**: 本地存储/OSS
- **定时任务**: Quartz

## 2. 项目结构

### 2.1 目录结构
```
yf-module-xxkj/
├── src/main/java/com/yf/xxkj/
│   ├── job/                    # 定时任务
│   └── modules/                # 业务模块
│       ├── assistant/          # AI助手
│       ├── award/              # 奖励系统
│       ├── classManagement/    # 班级管理
│       ├── classWorks/         # 课堂作业
│       ├── clazz/              # 课堂管理
│       ├── dataCentre/         # 数据中心
│       ├── mqtt/               # MQTT管理
│       ├── order/              # 订单管理
│       ├── prepareLessons/     # 备课管理
│       ├── serialNumber/       # 序列号管理
│       ├── user/               # 用户管理
│       ├── weather/            # 天气服务
│       └── ws/                 # WebSocket
├── src/main/resources/
│   └── mapper/                 # MyBatis映射文件
├── API文档.md                  # API接口文档
├── 订单激活码接口文档.md        # 订单激活码文档
└── pom.xml                     # Maven配置
```

### 2.2 核心依赖
- yf-module-system (系统模块)
- yf-module-repo (资源模块)
- yf-module-train (训练模块)
- yf-module-pay (支付模块)
- yf-module-exam (考试模块)
- yf-module-book (图书模块)
- yf-module-cert (证书模块)
- yf-module-mall (商城模块)
- yf-module-base (基础模块)
- yf-module-jiandanai (简单AI模块)

## 3. 核心业务模块

### 3.1 用户管理 (user)
**主要功能:**
- 用户信息管理
- 激活码验证
- 用户导入导出
- 账号注销

**核心类:**
- `UserController` - 用户控制器
- `XkUserService` - 用户业务服务

### 3.2 班级管理 (classManagement)
**主要功能:**
- 学校、年级、班级层级管理
- 学生班级分配
- 教师班级授权

### 3.3 课堂管理 (clazz)
**主要功能:**
- 在线课堂创建
- 课堂状态管理
- 学生课堂参与

### 3.4 课堂作业 (classWorks)
**主要功能:**
- 作业发布
- 作业提交
- 作业批改
- 成绩统计

### 3.5 数据中心 (dataCentre)
**主要功能:**
- 用户活跃度统计
- 学习数据分析
- 教学效果评估
- 数据报表生成

**核心实体:**
- `ReportUserDayActive` - 用户日活报表
- `ReportUserMonthOverview` - 用户月度总览
- `ReportResourceMonthOverview` - 资源月度总览

### 3.6 MQTT管理 (mqtt)
**主要功能:**
- 物联网设备管理
- MQTT项目管理
- 设备通信监控

**核心实体:**
- `MQTTProject` - MQTT项目
- `MQTTDevice` - MQTT设备
- `MQTTTopic` - MQTT主题

### 3.7 订单管理 (order)
**主要功能:**
- 订单创建管理
- 激活码生成
- 代理商管理

**核心类:**
- `AgencyOrderController` - 代理订单控制器

### 3.8 奖励系统 (award)
**主要功能:**
- 学生互动记录
- 奖励机制
- 积分管理

## 4. 定时任务

### 4.1 任务启动器
**类名:** `XkJobStarter`
**功能:** 系统启动时初始化所有定时任务

### 4.2 主要定时任务
1. **PPT解析任务** (`parse_pptx`)
   - 执行频率: 每3分钟
   - 功能: 解析PPT文档

2. **下课任务** (`end_clazz`)
   - 执行频率: 每30秒
   - 功能: 扫描并结束超时课堂

3. **AI使用次数刷新** (`refreshPoint`)
   - 执行频率: 每月1日0点
   - 功能: 重置用户AI使用次数

4. **账号有效期更新** (`accountExpirationDate`)
   - 执行频率: 每日1点
   - 功能: 更新用户账号有效期

5. **用户日活统计** (`userDayActive`)
   - 执行频率: 每日0点1分
   - 功能: 统计前一天用户活跃数据

6. **天气预报获取** (`daytimeWeather`/`nighttimeWeather`)
   - 执行频率: 白天8点，夜间20点
   - 功能: 获取天气预报数据

7. **数据报表任务**
   - `saveUserDataReport`: 每月1日保存用户数据报表
   - `userMonthCountReport`: 每月1日保存用户总量报表
   - `dataOverview`: 每日0点固化管理员数据
   - `reportUserSchoolData`: 每日2点生成学校用户数据

8. **学期数据固化**
   - `lastTerm`: 每年2月1日3点固化上学期数据
   - `nextTerm`: 每年8月15日3点固化下学期数据

## 5. 配置参数

### 5.1 数据库配置
```yaml
spring:
  datasource:
    url: **********************************
    username: ebook
    password: Zqzz123$%^
```

### 5.2 MQTT配置
```yaml
emqx:
  url: http://mqtt.mypep.cn:18083
  broker: tcp://mqtt.mypep.cn:1883
  username: admin
  password: Zqzz123!@#
```

### 5.3 文件上传配置
```yaml
local-upload:
  enabled: true
  prefix-url: https://examExample/xxkj/
  file-dir: /data/exambackend/upload/
```

### 5.4 天气服务配置
```yaml
weather:
  key: 43a7fd23505b44f895a6cff4ec9b010c
```

### 5.5 短信服务配置
```yaml
xkSms:
  signName: 人民教育出版社
  sname: rjxxkj
  spwd: Rjxxkj20240625
  open: true
```

## 6. API接口

### 6.1 基础信息
- **基础路径**: `/api/xxkj/` 和 `/api/mqtt/`
- **认证方式**: JWT Token
- **响应格式**: JSON

### 6.2 统一响应结构
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": *************
}
```

## 7. 数据库表结构

### 7.1 主要数据表
- `xk_mqtt_project` - MQTT项目表
- `xk_mqtt_device` - MQTT设备表
- `xk_mqtt_topic` - MQTT主题表
- `xk_agency_order` - 代理订单表
- `xk_account_date_record` - 账户日期记录表
- `xk_clazz_user` - 课堂用户关联表
- `xk_interaction_record` - 互动记录表
- `xk_serial_number` - 序列号表
- `report_user_day_active` - 用户日活报表
- `report_user_month_overview` - 用户月度总览报表
- `report_resource_month_overview` - 资源月度总览报表

## 8. 部署说明

### 8.1 环境要求
- JDK 8+
- MySQL 5.7+
- Redis 3.0+
- EMQX MQTT Broker

### 8.2 启动顺序
1. 启动MySQL数据库
2. 启动Redis缓存
3. 启动EMQX MQTT服务
4. 启动应用服务

## 9. 注意事项

### 9.1 重要提醒
1. **定时任务**: 系统启动时会自动初始化所有定时任务，注意避免重复执行
2. **数据备份**: 重要数据表需要定期备份，特别是用户数据和订单数据
3. **MQTT连接**: 确保MQTT服务稳定，设备通信依赖此服务
4. **文件存储**: 注意文件存储空间，定期清理无用文件
5. **日志监控**: 关注定时任务执行日志，及时发现异常

### 9.2 常见问题
1. **定时任务不执行**: 检查Quartz配置和数据库连接
2. **MQTT连接失败**: 检查网络和认证信息
3. **文件上传失败**: 检查存储路径权限和空间
4. **数据统计异常**: 检查定时任务执行状态
5. **激活码，订单相关**: 用户管理中状态栏，通过激活码，订单激活的用户，存在xk_account_date_record表，AccountDateRecord 是一个用于记录用户账户过期时间设置的实体类，主要用于管理用户账户的有效期和激活状态。
   核心字段和功能
   1. 基础信息字段
      id: 主键ID，使用雪花算法生成
      createTime/updateTime: 创建和更新时间
      createBy/updateBy: 创建人和修改人
      isDeleted: 逻辑删除标记
   2. 用户关联字段
      userId: 关联的用户ID
      roleId: 用户角色类型
   3. 激活方式字段
      type: 激活类型（1-激活码，2-订单）
      orderId: 订单ID（当type=2时使用）
      code: 使用的激活码
      codeId: 激活码模板ID
   4. 账户有效期管理
      accountType: 有效期类型
      expireDate: 账户过期日期
      state: 账户状态（1-有效，2-过期）
   5. 用户管理功能
      userRegType: 订单用户加入方式
      0: 新建用户
      1: 导入已有用户
      2: 导入新用户
      3: 批量生成用户
      4: 添加订单管理员
      5: 教师端导入
      6: 教师端添加
      importSwitch: 导入开关（0-关，1-开），教师的这个字段如果没开，默认导入按激活码的方式导入
   6. 会有定时任务每日更新过期用户
6. **埋点相关**:
   1. /api/xxkj/operationRecord保存操作行为，保存到redis缓存中，每日夜里拉去数据插入数据库中。
   2. OperationEnum常用的用户行为枚举类，SysDicValue记录了全量的操作类型，remark字段区分是信科的，还是编创的，分别存到不同的数据库，如果contentType字段不为空且remark是编程，
      则在信科数据库也存一份，用于信科后端统计
   3. 学科工具的埋点用xk_subject_tool_record存储，记录了时长
   4. 随堂练的页面时长埋点用xk_clazz_user_works_record存储，

   
   


---


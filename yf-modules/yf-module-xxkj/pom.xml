<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.yf</groupId>
        <artifactId>yf-modules</artifactId>
        <version>6.4.0.23042803</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yf</groupId>
    <artifactId>yf-module-xxkj</artifactId>
    <packaging>jar</packaging>
    <name>yf-module-xxkj</name>


    <dependencies>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-repo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-train</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-pay</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-exam</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-book</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-cert</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-mall</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yf</groupId>
            <artifactId>yf-module-jiandanai</artifactId>
        </dependency>

    </dependencies>
</project>
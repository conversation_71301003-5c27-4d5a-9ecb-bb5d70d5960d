package com.yf.exam.modules.cert.dto;

import com.yf.base.api.annon.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 授权列表数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-02-04 09:38
 */
@Data
@ApiModel(value = "授权列表", description = "授权列表")
public class CertGrantDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @Dict(dictTable = "el_sys_user", dicText = "real_name", dicCode = "id")
    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;


    @Dict(dictTable = "el_cert", dicText = "title", dicCode = "id")
    @ApiModelProperty(value = "证书ID", required = true)
    private String certId;


    @Dict(dicCode = "cert_grant_type")
    @ApiModelProperty(value = "1考试通过获得,2人工颁发", required = true)
    private Integer grantType;

    @ApiModelProperty(value = "考试ID或课程ID")
    private String refId;

    @ApiModelProperty(value = "证书原始文件")
    private String certFileOrig;

    @ApiModelProperty(value = "已签章的证书")
    private String certFileSign;

    @ApiModelProperty(value = "证书最终PDF")
    private String certFilePdf;

    @ApiModelProperty(value = "0待制证,1已制证")
    private Integer state;

    @ApiModelProperty(value = "归属部门")
    private String deptCode;

    @ApiModelProperty(value = "颁发人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "到期时间")
    private Date expireTime;

}

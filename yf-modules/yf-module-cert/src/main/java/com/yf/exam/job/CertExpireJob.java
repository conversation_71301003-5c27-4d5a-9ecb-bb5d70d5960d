package com.yf.exam.job;

import com.yf.base.utils.DateUtils;
import com.yf.exam.modules.cert.entity.Cert;
import com.yf.exam.modules.cert.entity.CertGrant;
import com.yf.exam.modules.cert.service.CertGrantService;
import com.yf.exam.modules.cert.service.CertService;
import com.yf.job.service.JobService;
import com.yf.notify.enums.MsgId;
import com.yf.system.modules.user.dto.request.UserMsgReqDTO;
import com.yf.system.modules.user.service.UserMsgService;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 颁发证书给用户
 * <AUTHOR>
 */
@Log4j2
@Component
public class CertExpireJob implements Job {

    @Autowired
    private CertService certService;

    @Autowired
    private UserMsgService userMsgService;

    @Autowired
    private CertGrantService certGrantService;


    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {


        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        String group = detail.getKey().getGroup();
        String data = String.valueOf(detail.getJobDataMap().get(JobService.TASK_DATA));

        log.info("++++++++++颁发证书：证书到期提醒");
        log.info("++++++++++jobName:{}", name);
        log.info("++++++++++jobGroup:{}", group);
        log.info("++++++++++taskData:{}", data);

        // 3日内即将到期
        List<CertGrant> list = certGrantService.listExpire(3);
        for (CertGrant item : list) {
            // 发送消息通知
            try {
                this.sendNotify(item);
            } catch (Exception e) {
                log.error(e);
                e.printStackTrace();
            }
        }
    }

    /**
     * 发送消息通知
     * @param grant
     */
    private void sendNotify(CertGrant grant) {

        UserMsgReqDTO dto = new UserMsgReqDTO();

        Cert cert = certService.getById(grant.getCertId());
        dto.setTmplId(MsgId.CERT_EXPIRE);
        dto.setUserIds(Arrays.asList(new String[]{grant.getUserId()}));

        // 到期时间
        String expire = DateUtils.formatDate(grant.getExpireTime(), "yyyy-MM-dd");

        // 必须按顺序
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("title", cert.getTitle());
        params.put("date", expire);
        dto.setParams(params);
        dto.setUseIm(true);
        dto.setUseSms(true);
        dto.setUseEmail(true);
        userMsgService.sendMsg(dto);
    }

}
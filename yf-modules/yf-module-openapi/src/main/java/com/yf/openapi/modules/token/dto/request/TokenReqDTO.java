package com.yf.openapi.modules.token.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 授权获取请求类
 * <AUTHOR>
 */
@Data
@ApiModel(value = "授权获取请求类", description = "授权获取请求类")
public class TokenReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "授权秘钥，与配置文件application-xx.yml中的ycloud.open-secret一致即可，测试默认秘钥为：JoQEJeD7grF8JdyVaOCvPDNL8arsyvfL", required = true)
    private String secret;

    @ApiModelProperty(value = "有效时长/秒，根据您系统安全要求自行设定，单位秒", required = true)
    private Long expires;

}

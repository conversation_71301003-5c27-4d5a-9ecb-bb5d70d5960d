<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.repo.modules.qu.mapper.QuAnswerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.repo.modules.qu.dto.QuAnswerDTO">
        <id column="id" property="id" />
        <result column="qu_id" property="quId" />
        <result column="is_right" property="isRight" />
        <result column="content" property="content" />
        <result column="image" property="image" />
        <result column="analysis" property="analysis" />
        <result column="tag" property="tag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`qu_id`,`is_right`,`content`,`image`,`analysis`,`tag`
    </sql>


    <update id="updateImageUrls" parameterType="java.lang.String">
        UPDATE el_qu_answer
        SET image = CONCAT(
                #{newUrlPrefix},
                SUBSTRING(image,
                          LOCATE('/', image, LOCATE('/', image, LOCATE('/', image) + 1) + 1)
                    )
            )
        WHERE image LIKE 'https://%/%/%/%';
    </update>
</mapper>

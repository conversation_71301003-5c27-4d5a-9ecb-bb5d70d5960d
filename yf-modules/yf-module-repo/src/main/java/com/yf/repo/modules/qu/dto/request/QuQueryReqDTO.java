package com.yf.repo.modules.qu.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 问题题目请求类
 * </p>
 * <AUTHOR>
 * @since 2020-05-25 13:23
 */
@Data
@ApiModel(value = "题目查询请求类", description = "题目查询请求类")
public class QuQueryReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "题目类型")
    private String quType;

    @ApiModelProperty(value = "归属题库")
    private String repoId;

    @ApiModelProperty(value = "章节ID")
    private String chapterId;

    @ApiModelProperty(value = "题目难度")
    private String level;

    @ApiModelProperty(value = "题目内容")
    private String content;

    @ApiModelProperty(value = "排除ID列表")
    private List<String> excludes;

    @ApiModelProperty(value = "是否只查用于考试的")
    private Boolean forExam;

    @ApiModelProperty(value = "数据类型：0训练 1考试")
    private Integer dataType;

    @ApiModelProperty(value = "知识点查询集合")
    private List<String> knowledgePointList;

    @ApiModelProperty(value = "数字素养查询集合")
    private List<String> digitalLiteracyList;


    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "编创")
    private Integer editor;

    @ApiModelProperty(value = "管理端")
    private Boolean admin;

}

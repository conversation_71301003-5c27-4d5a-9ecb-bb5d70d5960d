package com.yf.repo.modules.repo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 题库购买数据传输类
 * </p>
 * <AUTHOR>
 * @since 2023-03-06 10:03
 */
@Data
@ApiModel(value = "题库购买", description = "题库购买")
public class RepoBuyDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @ApiModelProperty(value = "商品ID", required = true)
    private String repoId;

    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;

    @ApiModelProperty(value = "订单编号", required = true)
    private String orderId;

    @ApiModelProperty(value = "是否已支付", required = true)
    private Boolean paid;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

}

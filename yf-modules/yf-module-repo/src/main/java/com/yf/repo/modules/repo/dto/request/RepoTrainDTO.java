package com.yf.repo.modules.repo.dto.request;

import com.yf.base.api.annon.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 题库训练数据传输类
 * </p>
 * <AUTHOR>
 * @since 2022-03-01 18:42
 */
@Data
@ApiModel(value = "题库训练", description = "题库训练")
public class RepoTrainDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @Dict(dicCode = "train_mode")
    @ApiModelProperty(value = "训练模式", required = true)
    private Integer mode;
    //private String mode_dictText;

    @Dict(dicCode = "id", dicText = "title", dictTable = "el_repo")
    @ApiModelProperty(value = "题库ID", required = true)
    private String repoId;
    //private String repoId_dictText;

    @ApiModelProperty(value = "已答题数", required = true)
    private Integer answerCount;

    @ApiModelProperty(value = "正确题数", required = true)
    private Integer rightCount;

    @ApiModelProperty(value = "总题数", required = true)
    private Integer totalCount;

    @ApiModelProperty(value = "0进行中1已完成", required = true)
    private Integer state;

    @ApiModelProperty(value = "用户部门", required = true)
    private String deptCode;

    @ApiModelProperty(value = "创建人", required = true)
    private String createBy;

    @ApiModelProperty(value = "提交时间")
    private Date finishTime;

    @ApiModelProperty(value = "开始时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "用户作业ID")
    private String clazzUserWorksId;


    /**
     * 正确百分比
     * @return
     */
    public Integer getPercent() {

        if (this.rightCount == null || this.totalCount == null) {
            return 0;
        }

        if (this.rightCount > 0) {
            return this.rightCount * 100 / this.totalCount;
        }

        return 0;
    }

}

package com.yf.repo.modules.repo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.repo.modules.repo.entity.RepoBuy;

import java.math.BigDecimal;

/**
 * <p>
 * 题库购买业务接口类
 * </p>
 * <AUTHOR>
 * @since 2023-03-06 10:03
 */
public interface RepoBuyService extends IService<RepoBuy> {

    /**
     * 判断是否购买
     * @param repoId
     * @param userId
     * @param price
     * @return
     */
    boolean isBuy(String repoId, String userId, BigDecimal price);

    /**
     * 标记为已支付
     * @param buyId
     * @param userId
     * @return
     */
    void finishBuy(String buyId, String userId);

    /**
     * 初始化支付
     * @param repoId
     * @param userId
     * @return 订单中心ID
     */
    String initBuy(String repoId, String userId);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.train.modules.train.mapper.RepoTrainQuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.train.modules.train.dto.RepoTrainQuDTO">
        <id column="id" property="id" />
        <result column="train_id" property="trainId" />
        <result column="qu_id" property="quId" />
        <result column="sort" property="sort" />
        <result column="is_right" property="isRight" />
        <result column="child" property="child" />
        <result column="ref_id" property="refId" />
        <result column="answered" property="answered" />
        <result column="title_body" property="titleBody" />
        <result column="flow_diagram" property="flowDiagram" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`train_id`,`qu_id`,`sort`,`is_right`,`child`,`ref_id`,`answered`
    </sql>

    <resultMap id="DetailResultMap"
               type="com.yf.train.modules.train.dto.ext.TrainQuDetailDTO"
               extends="BaseResultMap">
        <collection property="answerList" column="{trainId=train_id, quId=qu_id}" select="selectAnswers"/>
        <collection property="subList" column="id" select="selectSubList"/>
    </resultMap>

    <resultMap id="SubResultMap"
               type="com.yf.train.modules.train.dto.ext.TrainQuDetailDTO"
               extends="BaseResultMap">
        <collection property="answerList" column="{trainId=train_id, quId=qu_id}" select="selectAnswers"/>
    </resultMap>

    <resultMap id="AnswerResultMap"
               type="com.yf.train.modules.train.dto.ext.TrainQaDetailDTO"
               extends="com.yf.train.modules.train.mapper.RepoTrainQaMapper.BaseResultMap">
        <result column="content" property="content" />
        <result column="image" property="image" />
    </resultMap>


    <select id="detail" resultMap="DetailResultMap">
        SELECT tq.*,qu.content,qu.qu_type,qu.analysis,tq.title_body,qu.scratch_url as scratchAnswer,qu.code_answer,qu.have_answer,qu.answer_style,tq.judge_short_answer,tq.short_answer
        FROM el_repo_train_qu tq
        LEFT JOIN el_qu qu ON tq.qu_id=qu.id
        WHERE train_id=#{trainId} AND qu_id=#{quId} LIMIT 1;
    </select>


    <select id="selectAnswers" resultMap="AnswerResultMap">
        SELECT qa.content,qa.image,ta.id,
               IFNULL(ta.train_id, '${trainId}') AS train_id,
               IFNULL(ta.qu_id, '${quId}') AS qu_id,
               IFNULL(ta.answer_id, qa.id) AS answer_id,
               qa.is_right,
               IFNULL(ta.checked, 0) AS checked,
               ta.answer,qa.tag
        FROM el_qu_answer qa
        LEFT JOIN el_repo_train_qa ta ON ta.answer_id=qa.id AND ta.train_id=#{trainId}
        WHERE qa.qu_id=#{quId}
    </select>

    <select id="selectSubList" resultMap="SubResultMap">
        SELECT tq.*,qu.content,qu.qu_type as quType,qu.analysis,qu.have_answer,qu.answer_style,tq.judge_short_answer,tq.short_answer
        FROM el_repo_train_qu tq
         LEFT JOIN el_qu qu ON tq.qu_id=qu.id
        WHERE tq.ref_id=#{id} ORDER BY  tq.sort
    </select>


    <select id="detailList" resultMap="DetailResultMap">
        SELECT tq.*,qu.content,qu.qu_type,qu.analysis,tq.title_body,qu.scratch_url as scratchAnswer,qu.code_answer,qu.have_answer,tq.judge_short_answer,tq.short_answer
        FROM el_repo_train_qu tq
        LEFT JOIN el_qu qu ON tq.qu_id=qu.id
        WHERE train_id=#{trainId}

        <if test="quIds!=null and quIds.size()>0">
            AND qu_id IN
            <foreach collection="quIds" item="quId" open="(" separator="," close=")">
                #{quId}
            </foreach>
        </if>

    </select>

</mapper>

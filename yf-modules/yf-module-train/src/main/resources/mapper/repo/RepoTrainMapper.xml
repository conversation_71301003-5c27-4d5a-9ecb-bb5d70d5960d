<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.train.modules.train.mapper.RepoTrainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.train.modules.train.dto.RepoTrainDTO">
        <id column="id" property="id" />
        <result column="mode" property="mode" />
        <result column="repo_id" property="repoId" />
        <result column="answer_count" property="answerCount" />
        <result column="right_count" property="rightCount" />
        <result column="total_count" property="totalCount" />
        <result column="state" property="state" />
        <result column="dept_code" property="deptCode" />
        <result column="create_by" property="createBy" />
        <result column="finish_time" property="finishTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`mode`,`repo_id`,`answer_count`,`right_count`,`total_count`,`state`,`dept_code`,`create_by`,`finish_time`,`create_time`,`update_time`
    </sql>


    <select id="paging" resultMap="BaseResultMap">
        SELECT rt.* FROM el_repo_train rt
        LEFT JOIN el_repo repo ON rt.repo_id=repo.id
        WHERE rt.create_by='{{userId}}' AND (repo.id IS NOT NULL OR rt.mode=0)

        <if test="query!=null">
            <if test="query.createBy!=null and query.createBy!=''">
                AND rt.create_by = #{query.create_by}
            </if>
            <if test="query.repoId!=null and query.repoId!=''">
                AND rt.repo_id = #{query.repoId}
            </if>
            <if test="query.title!=null and query.title!=''">
                AND repo.title LIKE CONCAT('%',#{query.title},'%')
            </if>
        </if>

        <if test="query==null or query.repoId==null or query.repoId=='' ">
            AND rt.mode!=0
        </if>

        ORDER BY rt.state ASC, rt.update_time DESC
    </select>


    <select id="checkProcess" resultMap="BaseResultMap">

        SELECT rt.* FROM el_repo_train rt
        LEFT JOIN el_repo repo ON rt.repo_id=repo.id
        WHERE repo.is_train=1 AND rt.create_by=#{userId} AND rt.state=0 AND repo.id IS NOT NULL
        <if test="repoId!=null and repoId!=''">
            AND rt.repo_id=#{repoId}
        </if>
        ORDER BY rt.update_time DESC LIMIT 1

    </select>

</mapper>

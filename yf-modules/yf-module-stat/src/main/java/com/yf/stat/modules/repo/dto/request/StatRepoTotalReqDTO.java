package com.yf.stat.modules.repo.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 题库统计请求类
 * </p>
 * <AUTHOR>
 * @since 2020-09-11 11:10
 */
@Data
@ApiModel(value = "题库统计请求类", description = "题库统计请求类")
public class StatRepoTotalReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String title;

    @ApiModelProperty(value = "学习时间段", required = true)
    private List<String> dateRange;

}

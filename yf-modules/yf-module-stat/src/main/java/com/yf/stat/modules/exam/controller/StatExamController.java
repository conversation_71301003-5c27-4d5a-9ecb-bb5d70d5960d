package com.yf.stat.modules.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.ability.excel.ExportExcel;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import com.yf.base.api.api.dto.BaseIdReqDTO;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.exam.modules.exam.dto.ExamDTO;
import com.yf.exam.modules.exam.service.ExamService;
import com.yf.stat.modules.exam.dto.*;
import com.yf.stat.modules.exam.dto.request.StatExamReqDTO;
import com.yf.stat.modules.exam.dto.request.StatQuReqDTO;
import com.yf.stat.modules.exam.dto.request.StatTotalReqDTO;
import com.yf.stat.modules.exam.mapper.StatExamMapper;
import com.yf.stat.modules.exam.service.StatExamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 * 考试总体统计控制器
 * </p>
 * <AUTHOR>
 * @since 2020-09-11 11:10
 */
@Api(tags = {"考试总体统计"})
@RestController
@RequestMapping("/api/stat/exam")
public class StatExamController extends BaseController {

    @Autowired
    private StatExamService baseService;

    @Autowired
    private StatExamMapper statExamMapper;

    @Autowired
    private ExportExcel exportExcel;

    @Autowired
    private ExamService examService;

    /**
     * 查找考试总览
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "查找考试总览")
    @PostMapping("/overview")
    public ApiRest<StatExamTotalDTO> overview(@RequestBody BaseIdReqDTO reqDTO) {
        StatExamTotalDTO dto = baseService.findOverview(reqDTO.getId());
        return super.success(dto);
    }

    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "用户考试情况列表")
    @RequiresPermissions("exam:detail:record:list")
    @PostMapping("/user-paging")
    public ApiRest<IPage<StatExamUserDTO>> userPaging(@RequestBody PagingReqDTO<StatExamReqDTO> reqDTO) {

        //分页查询并转换
        IPage<StatExamUserDTO> page = baseService.userPaging(reqDTO);

        return super.success(page);
    }

    /**
     * 导出excel文件
     */
    @RequiresPermissions("exam:detail:record:export")
    @PostMapping("/user-export")
    public ApiRest exportFile(HttpServletResponse response, @RequestBody PagingReqDTO<StatExamReqDTO> reqDTO) {


        reqDTO.setCurrent(1);
        reqDTO.setSize(Integer.MAX_VALUE);

        // 导出文件名
        IPage<StatExamUserDTO> page = baseService.userPaging(reqDTO);

        // 导出数据
        exportExcel.export(response, StatExamUserDTO.class, "统计总览", page.getRecords());

        return success();
    }


    /**
     * 导出excel文件
     */
    @RequiresPermissions("exam:detail:record:export")
    @PostMapping("/userExamExport")
    public ApiRest userExamExport(HttpServletResponse response, @RequestBody PagingReqDTO<StatExamReqDTO> reqDTO) {


        reqDTO.setCurrent(1);
        reqDTO.setSize(Integer.MAX_VALUE);

        // 导出文件名
        IPage<ExportExamUserDTO> page = baseService.exportFile(reqDTO);

        // 导出数据
        exportExcel.export(response, StatExamUserDTO.class, "统计总览", page.getRecords());

        return success();
    }


    /**
     * 考试错题统计
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "考试错题统计")
    @PostMapping("/error-qu-paging")
    public ApiRest<IPage<StatErrorQuDTO>> errorQuPaging(@RequestBody PagingReqDTO<StatQuReqDTO> reqDTO) {

        //分页查询并转换
        IPage<StatErrorQuDTO> page = baseService.errorQuPaging(reqDTO);

        return super.success(page);
    }


    /**
     * 导出考试错题统计
     * @param response
     * @param reqDTO
     * @return
     */
    @PostMapping("/error-qu-export")
    public ApiRest errorQuExport(HttpServletResponse response, @RequestBody PagingReqDTO<StatQuReqDTO> reqDTO) {


        //分页查询并转换
        reqDTO.setCurrent(1);
        reqDTO.setSize(Integer.MAX_VALUE);
        IPage<StatErrorQuDTO> page = baseService.errorQuPaging(reqDTO);

        // 导出数据
        exportExcel.export(response, StatErrorQuDTO.class, "考试错题统计", page.getRecords());
        return success();
    }


    /**
     * 控制台-考试统计
     * @return
     */
    @ApiOperation(value = "考试列表")
    @PostMapping("/dash-exam-list")
    public ApiRest<List<StatExamDashDTO>> dashExamList() {
        List<StatExamDashDTO> list = statExamMapper.dashExamList();
        return super.success(list);
    }


    /**
     * 返回总分排行统计
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "总分统计")
    @PostMapping("/total-score")
    public ApiRest<List<LinkedHashMap<String, Object>>> totalScoreStat(@RequestBody StatTotalReqDTO reqDTO) {
        //分页查询并转换
        List<LinkedHashMap<String, Object>> list = baseService.totalScoreStat(reqDTO);
        return super.success(list);
    }

    /**
     * 返回总分排行统计
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "考试综合统计-导出")
    @PostMapping("/total-score-export")
    public void totalScoreStatExport(HttpServletResponse response, @RequestBody StatTotalReqDTO reqDTO) {
        //分页查询并转换
        List<LinkedHashMap<String, Object>> list = baseService.totalScoreStat(reqDTO);

        LinkedHashMap<String, String> headers = new LinkedHashMap<>(16);


        List<ExamDTO> examList = examService.listTitles(reqDTO.getExamIds());
        headers.put("sort", "排名");
        headers.put("realName", "姓名");
        headers.put("deptName", "部门名称");
        headers.put("totalScore", "总分数");

        // 动态列
        for (ExamDTO item : examList) {
            headers.put("exam_" + item.getId(), item.getTitle());
        }
        headers.put("passed", "是否通过");
        // 导出数据
        exportExcel.exportMap(response, "考试综合统计", headers, list);
    }

    /**
     * 返回总分排行统计
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "部门情况统计")
    @PostMapping("/dept-score")
    public ApiRest<List<StatExamDeptRespDTO>> deptScoreStat(@RequestBody StatTotalReqDTO reqDTO) {
        //分页查询并转换
        List<StatExamDeptRespDTO> list = baseService.deptScoreStat(reqDTO);
        return super.success(list);
    }

    /**
     * 返回总分排行统计
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "部门情况统计-导出")
    @PostMapping("/dept-score-export")
    public void deptScoreStatExport(HttpServletResponse response, @RequestBody StatTotalReqDTO reqDTO) {
        //分页查询并转换
        List<StatExamDeptRespDTO> list = baseService.deptScoreStat(reqDTO);
        // 导出数据
        exportExcel.export(response, StatExamDeptRespDTO.class, "部门考试统计", list);
    }


    /**
     * 返回总分排行统计
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "群组情况统计")
    @PostMapping("/group-score")
    public ApiRest<List<StatExamGroupRespDTO>> groupScoreStat(@RequestBody StatTotalReqDTO reqDTO) {
        //分页查询并转换
        List<StatExamGroupRespDTO> list = baseService.groupScoreStat(reqDTO);
        return super.success(list);
    }

    /**
     * 返回总分排行统计
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "群组情况统计-导出")
    @PostMapping("/group-score-export")
    public void groupScoreStatExport(HttpServletResponse response, @RequestBody StatTotalReqDTO reqDTO) {
        //分页查询并转换
        List<StatExamGroupRespDTO> list = baseService.groupScoreStat(reqDTO);
        // 导出数据
        exportExcel.export(response, StatExamGroupRespDTO.class, "群组考试统计", list);
    }


    /**
     * 分数段情况统计
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "分数段情况统计")
    @PostMapping("/score-range")
    public ApiRest<List<StatScoreRangeRespDTO>> rangeScoreStat(@RequestBody StatTotalReqDTO reqDTO) {
        //分页查询并转换
        List<StatScoreRangeRespDTO> list = baseService.scoreRangeStat(reqDTO);
        return super.success(list);
    }

    /**
     * 分数段情况统计-导出
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "分数段情况统计-导出")
    @PostMapping("/score-range-export")
    public void scoreRangeStatExport(HttpServletResponse response, @RequestBody StatTotalReqDTO reqDTO) {
        //分页查询并转换
        List<StatScoreRangeRespDTO> list = baseService.scoreRangeStat(reqDTO);
        // 导出数据
        exportExcel.export(response, StatScoreRangeRespDTO.class, "考试分数段统计", list);
    }
}

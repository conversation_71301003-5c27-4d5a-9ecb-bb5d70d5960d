package com.yf.stat.modules.exam.mapper;

import com.yf.stat.modules.exam.dto.request.StatTotalReqDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 考试总体统计Mapper
 * </p>
 * <AUTHOR>
 * @since 2020-09-11 11:10
 */
@Mapper
public interface StatScoreRangeMapper {


    /**
     * 查找分数段
     * @param reqDTO
     * @return
     */
    List<BigDecimal> rangeByDefaults(@Param("query") StatTotalReqDTO reqDTO);

    /**
     * 查找分数段
     * @param reqDTO
     * @return
     */
    List<BigDecimal> rangeBySelf(@Param("query") StatTotalReqDTO reqDTO);


}

package com.yf.stat.modules.course.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.stat.modules.course.dto.StatCourseTotalDTO;
import com.yf.stat.modules.course.dto.request.StatCourseFileReqDTO;
import com.yf.stat.modules.course.dto.request.StatCourseLearnReqDTO;
import com.yf.stat.modules.course.dto.response.StatCourseFileDTO;
import com.yf.stat.modules.course.dto.response.StatCourseLearnDTO;

/**
 * <p>
 * 考试总体统计业务类
 * </p>
 * <AUTHOR>
 * @since 2020-09-11 11:10
 */
public interface StatCourseService {

    /**
     * 查找课程总体统计
     * @param courseId
     * @return
     */
    StatCourseTotalDTO findOverview(String courseId);

    /**
     * 查找课件统计分页
     * @param reqDTO
     * @return
     */
    IPage<StatCourseFileDTO> fileStatPaging(PagingReqDTO<StatCourseFileReqDTO> reqDTO);


    /**
     * 查找用户学习统计
     * @param reqDTO
     * @return
     */
    IPage<StatCourseLearnDTO> userStatPaging(PagingReqDTO<StatCourseLearnReqDTO> reqDTO);

}

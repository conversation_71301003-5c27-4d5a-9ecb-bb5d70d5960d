package com.yf.stat.modules.exam.dto;

import com.yf.ability.excel.annotation.ExcelField;
import com.yf.base.api.annon.Dict;
import com.yf.system.utils.HtmlUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <p>
 * 考试总体统计数据传输类
 * </p>
 * <AUTHOR>
 * @since 2020-09-11 11:10
 */
@Data
@ApiModel(value = "错题统计响应类", description = "错题统计响应类")
public class StatErrorQuDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "题目", required = true)
    private String content;

    @ExcelField(title = "题型", dictCode = "qu_type", sort = 1)
    @Dict(dicCode = "qu_type")
    @ApiModelProperty(value = "题型")
    private String quType;
    //private String quType_dictText;

    @ExcelField(title = "作答次数")
    @ApiModelProperty(value = "作答次数", required = true)
    private String allCount;

    @ExcelField(title = "错误用户数", sort = 2)
    @ApiModelProperty(value = "错误用户数", required = true)
    private String userCount;

    @ExcelField(title = "错误次数", sort = 3)
    @ApiModelProperty(value = "错误次数")
    private String errorCount;

    @ExcelField(title = "题目")
    private String contentText;


    @ExcelField(title = "错误率", sort = 3)
    @ApiModelProperty(value = "错误率")
    private String errorRate;


    /**
     * 获取文本
     * @return
     */
    public String getContentText() {
        if (StringUtils.isBlank(this.content)) {
            return "";
        }
        return HtmlUtils.splitAndFilterString(this.content, 2000);
    }


}

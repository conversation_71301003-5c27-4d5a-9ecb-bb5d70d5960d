package com.yf.stat.modules.total.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户统计数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-06-04 08:28
 */
@Data
@ApiModel(value = "控制台数据统计", description = "控制台数据统计")
public class StatTotalNumDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "考试数量")
    private Integer examCount;

    @ApiModelProperty(value = "试卷数量")
    private Integer tmplCount;

    @ApiModelProperty(value = "竞赛数量")
    private Integer battleCount;

    @ApiModelProperty(value = "课程数量")
    private Integer courseCount;

    @ApiModelProperty(value = "课件数量")
    private Integer fileCount;

    @ApiModelProperty(value = "题库数量")
    private Integer repoCount;

    @ApiModelProperty(value = "试题数量")
    private Integer quCount;

    @ApiModelProperty(value = "用户数量")
    private Integer userCount;

    @ApiModelProperty(value = "发证数量")
    private Integer grantCount;

    @ApiModelProperty(value = "学校总数")
    private Integer schoolCount;

    @ApiModelProperty(value = "班级总数")
    private Integer clazzCount;

    @ApiModelProperty(value = "教师总数")
    private Integer teacherCount;

    @ApiModelProperty(value = "学生总数")
    private Integer studentCount;

    @ApiModelProperty(value = "部门资源总数")
    private Integer resourceCount;
}

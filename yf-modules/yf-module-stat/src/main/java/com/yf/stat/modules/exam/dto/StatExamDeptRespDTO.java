package com.yf.stat.modules.exam.dto;

import com.yf.ability.excel.annotation.ExcelField;
import com.yf.base.utils.DecimalUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 部门统计响应类
 * </p>
 * <AUTHOR>
 * @since 2020-09-11 11:10
 */
@Data
@ApiModel(value = "部门统计响应类", description = "部门统计响应类")
public class StatExamDeptRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部门编号", required = true)
    private String deptCode;

    @ExcelField(title = "部门名称")
    @ApiModelProperty(value = "部门名称", required = true)
    private String deptName;

    @ExcelField(title = "参考人数", sort = 1)
    @ApiModelProperty(value = "参考人数", required = true)
    private Integer joinUser;

    @ExcelField(title = "通过人数", sort = 2)
    @ApiModelProperty(value = "通过人数", required = true)
    private Integer passUser;

    @ExcelField(title = "平均分", sort = 3)
    @ApiModelProperty(value = "平均分", required = true)
    private BigDecimal avgScore;

    @ExcelField(title = "通过率%", sort = 4)
    @ApiModelProperty(value = "通过率%", required = true)
    private BigDecimal passRate;

    /**
     * 通过率、百分比
     * @return
     */
    public BigDecimal getPassRate() {
        if (passUser == null || passUser.equals(0) || joinUser == null || joinUser.equals(0)) {
            return BigDecimal.ZERO;
        }

        // 通过百分比
        return DecimalUtils.divide(new BigDecimal(passUser * 100), joinUser).setScale(2, RoundingMode.DOWN);
    }

}

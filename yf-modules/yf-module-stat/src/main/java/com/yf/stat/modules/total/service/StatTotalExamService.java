package com.yf.stat.modules.total.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.stat.modules.total.dto.StatTotalExamDTO;
import com.yf.stat.modules.total.dto.request.StatTotalExamReqDTO;

/**
 * <p>
 * 考试统计业务类
 * </p>
 * <AUTHOR>
 * @since 2021-06-04 09:08
 */
public interface StatTotalExamService {


    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<StatTotalExamDTO> paging(PagingReqDTO<StatTotalExamReqDTO> reqDTO);
}

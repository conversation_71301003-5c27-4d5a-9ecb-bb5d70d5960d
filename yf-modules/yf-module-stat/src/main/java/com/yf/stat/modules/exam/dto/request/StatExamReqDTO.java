package com.yf.stat.modules.exam.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 考试统计请求类
 * </p>
 * <AUTHOR>
 * @since 2020-09-11 11:10
 */
@Data
@ApiModel(value = "考试统计请求类", description = "考试统计请求类")
public class StatExamReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "考试ID", required = true)
    private String examId;

    @ApiModelProperty(value = "用户名，用于搜索", required = true)
    private String realName;


}

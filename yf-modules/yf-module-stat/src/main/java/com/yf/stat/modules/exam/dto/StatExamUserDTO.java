package com.yf.stat.modules.exam.dto;


import com.yf.ability.excel.annotation.ExcelField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 考试总体统计数据传输类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11 11:10
 */
@Data
@ApiModel(value="用户统计", description="考试总体统计")
public class StatExamUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required=true)
    private String id;

    @ApiModelProperty(value = "考试ID", required=true)
    private String examId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ExcelField(title = "用户名")
    @ApiModelProperty(value = "用户名")
    private String userName;

    @ExcelField(title = "姓名",sort = 1)
    @ApiModelProperty(value = "姓名")
    private String realName;

    @ExcelField(title = "考试次数", sort = 2)
    @ApiModelProperty(value = "考试次数")
    private Integer examCount;

    @ExcelField(title = "最高分数", sort = 3)
    @ApiModelProperty(value = "最高分数")
    private BigDecimal maxScore;

    @ApiModelProperty(value = "是否通过")
    private Boolean passed;

    @ExcelField(title = "是否通过", sort = 4)
    private String passDesc;

    @ExcelField(title = "部门", sort = 5)
    @ApiModelProperty(value = "部门")
    private String deptName;

    @ExcelField(title = "考试用时", sort = 6)
    @ApiModelProperty(value = "考试用时")
    private String userTime;

    @ExcelField(title = "证件号", sort = 7)
    @ApiModelProperty(value = "证件号")
    private String idCard;

    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "座位号")
    private Integer seatNumber;


    @ApiModelProperty(value = "考试状态")
    private String examStatus;

    @ApiModelProperty(value = "能否删除")
    private Boolean canRemove;

    @ApiModelProperty("报告生成状态,成功 失败")
    private String reportStatus;

    @ApiModelProperty(value = "考点id")
    private String examVenueId;

    @ApiModelProperty(value = "考场id")
    private String examVenueChildId;

    @ApiModelProperty(value = "考点")
    private String examVenueName;

    @ApiModelProperty(value = "考场")
    private String examVenueChildName;

    @ApiModelProperty(value = "信息科技-上次备课所在年级 或 完善信息时填写的年级")
    private String grade;

    @ApiModelProperty(value = "信息科技-学校")
    private String school;

    @ApiModelProperty(value = "班级id")
    private String clazzId;

    @ApiModelProperty(value = "班级名称")
    private String clazzName;

    /**
     * 准考证号
     */
    @ApiModelProperty(value = "准考证号", required = true)
    private String examTicketNumber;

    /**
     * 处理是否通过
     * @return
     */
    /*public String getPassDesc(){
        if(examCount == 0){
            return "缺考";
        }
        return passed?"通过":"不通过";
    }*/


}

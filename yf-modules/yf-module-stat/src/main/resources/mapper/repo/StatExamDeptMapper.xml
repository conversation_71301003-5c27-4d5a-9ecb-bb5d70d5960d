<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.stat.modules.exam.mapper.StatExamDeptMapper">


    <resultMap id="ResultMap" type="com.yf.stat.modules.exam.dto.StatExamDeptRespDTO">
        <result column="dept_code" property="deptCode" />
        <result column="dept_name" property="deptName" />
        <result column="joinUser" property="joinUser" />
        <result column="avgScore" property="avgScore" />
    </resultMap>


    <select id="listByDefaults" resultMap="ResultMap">
        SELECT
        dept.dept_code,
        dept.dept_name,
        COUNT(DISTINCT rc.user_id) AS joinUser,
        ROUND((IFNULL(SUM(rc.max_score)/COUNT(DISTINCT rc.user_id), 0)),2) AS avgScore
        FROM el_sys_depart dept
        LEFT JOIN el_sys_user uc ON uc.dept_code=dept.dept_code
        LEFT JOIN el_exam_record rc ON rc.user_id=uc.id AND rc.exam_id IN <foreach collection="query.examIds" open="(" close=")" separator="," item="examId">#{examId}</foreach>
        WHERE dept.dept_code IN <foreach collection="query.deptCodes" open="(" close=")" separator="," item="deptCode">#{deptCode}</foreach>
        GROUP BY dept.dept_code
        ORDER BY avgScore DESC
    </select>

    <select id="listPassIds" resultType="String">
        SELECT
        uc.id,
        COUNT(CASE rc.passed WHEN 1 THEN 1 ELSE NULL END) AS passCount
        FROM el_exam_record rc
        LEFT JOIN el_sys_user uc ON rc.user_id=uc.id
        WHERE uc.dept_code=#{deptCode} AND rc.exam_id IN <foreach collection="examIds" open="(" close=")" separator="," item="examId">#{examId}</foreach>
        GROUP BY uc.id
        HAVING passCount >= #{size}
    </select>



</mapper>

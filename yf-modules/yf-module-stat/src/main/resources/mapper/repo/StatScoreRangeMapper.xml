<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.stat.modules.exam.mapper.StatScoreRangeMapper">



    <select id="rangeByDefaults" resultType="java.math.BigDecimal">
        SELECT
        IFNULL(SUM(rc.max_score),0) AS score
        FROM el_sys_user uc
        LEFT JOIN el_sys_depart dept ON uc.dept_code=dept.dept_code
        LEFT JOIN el_enter_dist dst ON uc.id=dst.user_id AND dst.ref_type=1 AND dst.ref_id IN <foreach collection="query.examIds" open="(" close=")" separator="," item="examId">#{examId}</foreach>
        LEFT JOIN el_exam_record rc ON rc.exam_id IN <foreach collection="query.examIds" open="(" close=")" separator="," item="examId">#{examId}</foreach> AND rc.user_id=uc.id
        WHERE dst.user_id IS NOT NULL OR rc.user_id IS NOT NULL
        GROUP BY uc.id
    </select>

    <select id="rangeBySelf" resultType="java.math.BigDecimal">
        SELECT
        IFNULL(SUM(rc.max_score),0) AS score
        FROM el_sys_user uc
        LEFT JOIN el_sys_depart dept ON uc.dept_code=dept.dept_code
        LEFT JOIN el_exam_record rc ON rc.user_id=uc.id
        WHERE rc.exam_id IN <foreach collection="query.examIds" open="(" close=")" separator="," item="examId">#{examId}</foreach>
        AND (<include refid="userScopeQuery" />)
        GROUP BY uc.id
    </select>

    <sql id="userScopeQuery">
        <trim prefix="" suffix="" prefixOverrides="OR">
            <if test="query.deptCodes!=null and query.deptCodes.size()>0">
                OR uc.dept_code IN <foreach collection="query.deptCodes" open="(" close=")" separator="," item="deptCode">#{deptCode}</foreach>
            </if>
            <if test="query.userIds!=null and query.userIds.size()>0">
                OR uc.id IN <foreach collection="query.userIds" open="(" close=")" separator="," item="userId">#{userId}</foreach>
            </if>
            <if test="query.groupIds!=null and query.groupIds.size()>0">
                OR uc.id IN (SELECT user_id FROM el_sys_group_user WHERE group_id IN <foreach collection="query.groupIds" open="(" close=")" separator="," item="groupId">#{groupId}</foreach>)
            </if>
        </trim>
    </sql>


</mapper>

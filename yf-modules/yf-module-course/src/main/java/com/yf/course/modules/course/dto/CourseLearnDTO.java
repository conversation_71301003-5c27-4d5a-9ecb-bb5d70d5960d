package com.yf.course.modules.course.dto;

import com.yf.base.api.annon.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 课程学习记录数据传输类
 * </p>
 * <AUTHOR>
 * @since 2020-12-17 11:28
 */
@Data
@ApiModel(value = "课程学习记录", description = "课程学习记录")
public class CourseLearnDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @Dict(dictTable = "el_course", dicText = "title", dicCode = "id")
    @ApiModelProperty(value = "课程ID", required = true)
    private String courseId;
    private String courseId_dictText;

    @Dict(dictTable = "el_sys_user", dicText = "real_name", dicCode = "id")
    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;
    private String userId_dictText;

    @ApiModelProperty(value = "已学课件", required = true)
    private Integer learnFile;

    @ApiModelProperty(value = "总课件数量", required = true)
    private Integer totalFile;

    @ApiModelProperty(value = "0未学完,1已学完", required = true)
    private Integer state;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

    @ApiModelProperty(value = "更新时间", required = true)
    private Date updateTime;

    @ApiModelProperty(value = "学完时间", required = true)
    private Date finishTime;

    @ApiModelProperty(value = "创建人", required = true)
    private String createBy;

    @ApiModelProperty(value = "修改人", required = true)
    private String updateBy;

    @ApiModelProperty(value = "数据标识", required = true)
    private Integer dataFlag;

}

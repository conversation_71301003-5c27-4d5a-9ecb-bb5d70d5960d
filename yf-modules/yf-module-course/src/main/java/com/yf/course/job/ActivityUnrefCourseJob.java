package com.yf.course.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yf.base.utils.jackson.JsonHelper;
import com.yf.course.modules.course.entity.CourseActivity;
import com.yf.course.modules.course.service.CourseActivityService;
import com.yf.job.service.JobService;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 处理关联活动
 * <AUTHOR>
 */
@Log4j2
@Component
public class ActivityUnrefCourseJob implements Job {

    @Autowired
    private CourseActivityService courseActivityService;

    @Override
    public void execute(JobExecutionContext jbExecutionContext) throws JobExecutionException {


        JobDetail detail = jbExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        String group = detail.getKey().getGroup();
        String data = String.valueOf(detail.getJobDataMap().get(JobService.TASK_DATA));

        log.info("++++++++++定时任务：移除课程关联活动");
        log.info("++++++++++jobName:{}", name);
        log.info("++++++++++jobGroup:{}", group);
        log.info("++++++++++taskData:{}", data);

        Map<String, String> map = JsonHelper.parseObject(data, new TypeReference<Map<String, String>>() {
        });
        String userId = map.get("userId");
        String activityId = map.get("activityId");

        // 移除引用
        QueryWrapper<CourseActivity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(CourseActivity::getActivityId, activityId);
        if (!StringUtils.isEmpty(userId)) {
            wrapper.lambda().eq(CourseActivity::getUserId, userId);
        }
        courseActivityService.remove(wrapper);

    }


}

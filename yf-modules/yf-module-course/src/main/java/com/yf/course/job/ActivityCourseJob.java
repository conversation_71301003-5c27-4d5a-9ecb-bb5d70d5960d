package com.yf.course.job;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yf.base.utils.jackson.JsonHelper;
import com.yf.course.modules.course.service.CourseActivityService;
import com.yf.job.service.JobService;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 处理关联活动
 * <AUTHOR>
 */
@Log4j2
@Component
public class ActivityCourseJob implements Job {

    @Autowired
    private CourseActivityService courseActivityService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {


        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        String group = detail.getKey().getGroup();
        String data = String.valueOf(detail.getJobDataMap().get(JobService.TASK_DATA));

        log.info("++++++++++定时任务：课程处理关联活动");
        log.info("++++++++++jobName:{}", name);
        log.info("++++++++++jobGroup:{}", group);
        log.info("++++++++++taskData:{}", data);

        Map<String, String> map = JsonHelper.parseObject(data, new TypeReference<Map<String, String>>() {
        });
        String userId = map.get("userId");
        String refCourse = map.get("refCourse");
        String activityId = map.get("activityId");
        // 考试
        courseActivityService.merge(userId, activityId, refCourse);

    }


}

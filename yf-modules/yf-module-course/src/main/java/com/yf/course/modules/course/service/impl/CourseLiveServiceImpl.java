package com.yf.course.modules.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.utils.BeanMapper;
import com.yf.base.utils.DateUtils;
import com.yf.course.modules.course.dto.response.CourseLiveRespDTO;
import com.yf.course.modules.course.entity.CourseLive;
import com.yf.course.modules.course.mapper.CourseLiveMapper;
import com.yf.course.modules.course.service.CourseLiveService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 课程直播业务实现类
 * </p>
 * <AUTHOR>
 * @since 2021-12-03 19:31
 */
@Service
public class CourseLiveServiceImpl extends ServiceImpl<CourseLiveMapper, CourseLive> implements CourseLiveService {


    @Override
    public void saveAll(String courseId, List<CourseLiveRespDTO> liveList) {


        // 移除旧数据
        QueryWrapper<CourseLive> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(CourseLive::getCourseId, courseId);
        List<CourseLive> oldList = this.list(wrapper);

        // 已存在的ID
        List<String> ids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(oldList)) {
            for (CourseLive item : oldList) {
                ids.add(item.getId());
            }
        }


        List<CourseLive> list = new ArrayList<>();

        for (CourseLiveRespDTO item : liveList) {
            CourseLive live = new CourseLive();
            BeanMapper.copy(item, live);
            live.setCourseId(courseId);

            if (StringUtils.isEmpty(live.getTitle())) {
                throw new ServiceException("直播标题不能为空！");
            }

            if (live.getStartTime() == null) {
                throw new ServiceException("直播开始时间不能为空！");
            }

            if (live.getDuration() == null || live.getDuration().equals(0)) {
                throw new ServiceException("直播时长不能为空，且必须大于0");
            }

            // 移除要移除的
            if (!StringUtils.isEmpty(live.getId())
                    && ids.contains(live.getId())) {
                ids.remove(live.getId());
            }

            list.add(live);
        }

        // 添加或保存
        if (!CollectionUtils.isEmpty(list)) {
            this.saveOrUpdateBatch(list);
        }

        // 删除的ID
        if (!CollectionUtils.isEmpty(ids)) {
            this.removeByIds(ids);
        }
    }

    @Override
    public boolean checkLive(String courseId, String liveId) {
        // 校验是否存在
        QueryWrapper<CourseLive> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(CourseLive::getCourseId, courseId)
                .eq(CourseLive::getId, liveId);

        return this.count(wrapper) > 0;
    }


    /**
     * 校验直播时间是否变化，时间发生变化才需要重新生成直播地址
     * @param old
     * @param add
     * @return
     */
    private boolean isChange(CourseLive old, CourseLive add) {


        String oldStart = DateUtils.formatDate(old.getStartTime(), "yyyy-MM-dd HH:mm:ss");
        String newStart = DateUtils.formatDate(add.getStartTime(), "yyyy-MM-dd HH:mm:ss");

        if (!oldStart.equals(newStart)) {
            return true;
        }

        return !old.getDuration().equals(add.getDuration());
    }

}

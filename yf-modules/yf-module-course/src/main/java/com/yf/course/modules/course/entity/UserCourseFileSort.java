package com.yf.course.modules.course.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * <p>
 * 用户授课中资源列表排序
 * </p>
 */
@Data
@TableName("el_user_course_file_sort")
public class UserCourseFileSort extends Model<UserCourseFileSort> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户id
     */
    @TableField
    private String userId;


    /**
     * 文件夹ID
     */
    @TableField
    private String dirId;



    /**
     * 文件ID
     */
    @TableField
    private String fileRefId;


    /**
     * 课件排序
     */
    @TableField
    private Integer sort;


}

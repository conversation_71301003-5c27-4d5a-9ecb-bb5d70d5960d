package com.yf.course.job;

import com.yf.base.utils.jackson.JsonHelper;
import com.yf.course.modules.course.dto.CourseFileDTO;
import com.yf.course.modules.course.entity.CourseFile;
import com.yf.course.modules.course.service.CourseFileService;
import com.yf.job.service.JobService;
import com.yf.system.utils.VideoUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 获取视频时长的任务
 * <AUTHOR>
 */
@Log4j2
@Component
public class VideoDurationJob implements Job {

    @Autowired
    private VideoUtil videoUtil;

    @Autowired
    private CourseFileService courseFileService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {


        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        String group = detail.getKey().getGroup();
        String data = String.valueOf(detail.getJobDataMap().get(JobService.TASK_DATA));

        log.info("++++++++++定时任务：处理视频时长");
        log.info("++++++++++jobName:{}", name);
        log.info("++++++++++jobGroup:{}", group);
        log.info("++++++++++taskData:{}", data);

        CourseFileDTO dto = JsonHelper.parseObject(data, CourseFileDTO.class);
        if (StringUtils.isNotBlank(dto.getViewUrl())) {
            long duration = videoUtil.getDuration(dto.getViewUrl());
            if (duration > 0) {
                CourseFile file = new CourseFile();
                file.setId(dto.getId());
                file.setDuration((int) duration);
                file.setUpdateTime(new Date());
                courseFileService.updateById(file);
            }
        }

    }


}

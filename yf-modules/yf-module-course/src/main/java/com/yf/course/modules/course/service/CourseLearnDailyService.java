package com.yf.course.modules.course.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.course.modules.course.dto.CourseLearnDailyDTO;
import com.yf.course.modules.course.entity.CourseLearnDaily;

/**
 * <p>
 * 学习日统计业务接口类
 * </p>
 * <AUTHOR>
 * @since 2022-03-29 11:29
 */
public interface CourseLearnDailyService extends IService<CourseLearnDaily> {

    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<CourseLearnDailyDTO> paging(PagingReqDTO<CourseLearnDailyDTO> reqDTO);

    /**
     * 保存每日学习时间
     * @param courseId
     * @param fileId
     * @param userId
     * @param sec
     */
    void incr(String courseId, String fileId, String userId, long sec);

    /**
     * 查找当日学习时间
     * @param courseId
     * @param userId
     * @return
     */
    long daily(String courseId, String userId);
}

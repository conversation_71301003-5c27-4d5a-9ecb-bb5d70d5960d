package com.yf.course.modules.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.course.modules.course.dto.request.CoursePersonReqDTO;
import com.yf.course.modules.course.entity.CoursePerson;
import com.yf.system.modules.user.dto.SysUserDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程定员Mapper
 * </p>
 * <AUTHOR>
 * @since 2021-02-01 10:51
 */
public interface CoursePersonMapper extends BaseMapper<CoursePerson> {

    /**
     * 查找分页数据
     * @param page
     * @param reqDTO
     * @return
     */
    IPage<SysUserDTO> paging(Page page, @Param("query") CoursePersonReqDTO reqDTO);

    /**
     * 根据课程查找用户ID列表
     * @param courseId
     * @param scope
     * @return
     */
    List<String> listUserIdByCourse(@Param("courseId") String courseId, @Param("scope") Integer scope);
}

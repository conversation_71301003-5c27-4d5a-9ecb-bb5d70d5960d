package com.yf.course.modules.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.base.utils.jackson.JsonHelper;
import com.yf.course.modules.course.dto.CourseCommentDTO;
import com.yf.course.modules.course.entity.CourseComment;
import com.yf.course.modules.course.mapper.CourseCommentMapper;
import com.yf.course.modules.course.service.CourseCommentService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 课程评论业务实现类
 * </p>
 * <AUTHOR>
 * @since 2020-12-17 11:28
 */
@Service
public class CourseCommentServiceImpl extends ServiceImpl<CourseCommentMapper, CourseComment> implements CourseCommentService {

    @Override
    public IPage<CourseCommentDTO> paging(PagingReqDTO<CourseCommentDTO> reqDTO) {

        // 创建分页对象
        Page query = reqDTO.toPage();


        //查询条件
        QueryWrapper<CourseComment> wrapper = new QueryWrapper<>();

        // 请求参数
        CourseCommentDTO params = reqDTO.getParams();


        //获得数据
        IPage<CourseComment> page = this.page(query, wrapper);
        //转换结果
        IPage<CourseCommentDTO> pageData = JsonHelper.parseObject(page, new TypeReference<Page<CourseCommentDTO>>() {
        });
        return pageData;
    }
}

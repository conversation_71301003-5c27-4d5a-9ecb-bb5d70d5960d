package com.yf.course.modules.course.service.impl;

import com.yf.course.modules.course.service.OfficeWeb365Service;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.Key;
import java.security.spec.AlgorithmParameterSpec;

/**
 * <p>
 * officeweb365相关实现类
 * </p>
 * <AUTHOR>
 * @since 2024-7-23 11:28
 */
@Service
public class OfficeWeb365ServiceImpl implements OfficeWeb365Service {
    @Value("${officeweb365.key}")
    private String officeweb365Key;
    @Value("${officeweb365.iv}")
    private String officeweb365IV;

    @Override
    public String encode(String data) throws Exception {
        // 得到加密对象Cipher
        Cipher enCipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        // 设置工作模式为加密模式，给出密钥和向量
        // 设置密钥参数
        DESKeySpec keySpec = new DESKeySpec(officeweb365Key.getBytes());
        // 设置向量
        AlgorithmParameterSpec iv = new IvParameterSpec(officeweb365IV.getBytes());
        // 获得密钥工厂
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        Key key = keyFactory.generateSecret(keySpec);// 得到密钥对象
        System.out.println("officeweb365-data:"+data);
        enCipher.init(Cipher.ENCRYPT_MODE, key, iv);
        byte[] pasByte = enCipher.doFinal(data.getBytes("utf-8"));
        String base64 = Base64.encodeBase64String(pasByte).replaceAll("\\+", "_").replaceAll("\\/", "@");
        System.out.println("officeweb365-base64:"+base64);
        return base64;
    }
}

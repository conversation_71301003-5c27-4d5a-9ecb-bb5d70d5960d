package com.yf.course.modules.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.course.modules.course.dto.CourseInfoDTO;
import com.yf.course.modules.course.dto.ext.CourseDetailExtDTO;
import com.yf.course.modules.course.dto.request.UserCourseReqDTO;
import com.yf.course.modules.course.dto.response.UserCourseRespDTO;
import com.yf.course.modules.course.entity.Course;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 课程信息Mapper
 * </p>
 * <AUTHOR>
 * @since 2020-12-17 11:28
 */
@Repository
public interface CourseMapper extends BaseMapper<Course> {


    /**
     * 查找课程详情
     * @param id
     * @return
     */
    CourseDetailExtDTO findDetail(@Param("id") String id);

    /**
     * 查找用户课程列表，包含用户学习状态的
     * @param page
     * @param userId
     * @param deptCode
     * @param reqDTO
     * @return
     */
    IPage<UserCourseRespDTO> userPaging(Page page, @Param("userId") String userId, @Param("deptCode") String deptCode, @Param("query") UserCourseReqDTO reqDTO);


    /**
     * 课程列表，包含用户学习状态的，是否购买
     * @param userId
     * @return
     */
    List<CourseDetailExtDTO> userCourseList(@Param("userId") String userId);


    /**
     * 根据年级查询课程目录
     * @param id
     * @return
     */
    CourseInfoDTO findCourseInfo(@Param("id") String id);



    /**
     * 根据年级查询课程目录(不返回文件)
     * @param id
     * @return
     */
    CourseInfoDTO findCourseDir(@Param("id") String id);
}

package com.yf.course.modules.course.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.course.modules.course.dto.CourseFileDTO;
import com.yf.course.modules.course.dto.ResourceViewerNumberDTO;
import com.yf.course.modules.course.dto.ResourceViewerTeachNumberDTO;
import com.yf.course.modules.course.dto.ext.CourseRefDirExtDTO;
import com.yf.course.modules.course.dto.request.CourseFileFetchReqDTO;
import com.yf.course.modules.course.dto.request.CourseFileReqDTO;
import com.yf.course.modules.course.entity.CourseFile;
import com.yf.system.modules.dict.dto.ext.DicValueTreeDTO;

import java.util.List;

/**
 * <p>
 * 课件信息业务类
 * </p>
 * <AUTHOR>
 * @since 2020-12-17 11:28
 */
public interface CourseFileService extends IService<CourseFile> {

    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<CourseFileDTO> paging(PagingReqDTO<CourseFileReqDTO> reqDTO);

    /**
     * 保存课件文件
     * @param reqDTO
     */
    CourseFileDTO save(CourseFileDTO reqDTO) ;

    /**
     * 删除课件
     * @param ids
     */
    void delete(List<String> ids);

    /**
     * 根据上级类目来加载数据
     * @param reqDTO
     * @return
     */
    List<CourseRefDirExtDTO> fetchByCatId(CourseFileFetchReqDTO reqDTO);

    /**
     * 信息科技-教学资源类型列表
     * @return
     */
    List<DicValueTreeDTO> courseFileTypeList();

    /**
     * 重新解析 office web 365 pptx
     * @return
     */
    void resetParsePptx();
    /**
     * 解析 office web 365 pptx 单个文件
     * @return
     */
    void parseOnePptx(String id);

    /**
     * 数据大屏-资源总览
     * @return
     */
    ResourceViewerNumberDTO resourceOverview();

    /**
     * 资源观看人数-教学资源
     * @return
     */
    ResourceViewerTeachNumberDTO resourceTeachOverview();


    /**
     * 分页查询教师学堂数据
     * @param reqDTO
     * @return
     */
    IPage<CourseFileDTO> filePaging(PagingReqDTO<CourseFileReqDTO> reqDTO);


    /**
     * 根据bookId查询是否为信息科技用书
     * @param bookId
     * @return
     */
    Boolean isXkBook(Long bookId);

}

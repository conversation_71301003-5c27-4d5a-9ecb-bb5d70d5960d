package com.yf.course.modules.course.dto.request;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yf.base.utils.jackson.NumericBooleanDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 课程信息数据传输类
 * </p>
 * <AUTHOR>
 * @since 2020-12-17 11:28
 */
@Data
@ApiModel(value = "用户课程学习请求类", description = "用户课程学习请求类")
public class UserCourseReqDTO implements Serializable {


    @JsonDeserialize(using = NumericBooleanDeserializer.class)
    @ApiModelProperty(value = "是否必修", required = true)
    private Boolean isMust;

    @ApiModelProperty(value = "分类", required = true)
    private String catId;

    @ApiModelProperty(value = "课程名称", required = true)
    private String title;

    @ApiModelProperty(value = "只显示已学习的", required = true)
    private Boolean onlyLearn;

    @ApiModelProperty(value = "0未学习,1学习中,2已学完", required = true)
    private Integer learnState;
}

package com.yf.course.modules.course.dto.response;

import com.yf.course.modules.course.dto.CourseQaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 课程问答数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-04-19 11:04
 */
@Data
@ApiModel(value = "课程问答分页响应类", description = "课程问答分页响应类")
public class CourseQaRespDTO extends CourseQaDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "回复列表", required = true)
    private List<CourseQaRespDTO> replyList;

    @ApiModelProperty(value = "用户头像", required = true)
    private String avatar;


}

package com.yf.course.modules.course.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 课程学习记录实体类
 * </p>
 * <AUTHOR>
 * @since 2020-12-17 11:28
 */
@Data
@TableName("el_course_learn")
public class CourseLearn extends Model<CourseLearn> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 课程ID
     */
    @TableField("course_id")
    private String courseId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 已学课件
     */
    @TableField("learn_file")
    private Integer learnFile;

    /**
     * 总课件数量
     */
    @TableField("total_file")
    private Integer totalFile;

    /**
     * 0未学完,1已学完
     */
    @TableField
    private Integer state;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 学完时间
     */
    @TableField("finish_time")
    private Date finishTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 数据标识
     */
    @TableField("data_flag")
    private Integer dataFlag;

}

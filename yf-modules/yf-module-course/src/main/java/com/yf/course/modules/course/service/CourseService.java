package com.yf.course.modules.course.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.course.modules.course.dto.CourseDTO;
import com.yf.course.modules.course.dto.ext.CourseDetailExtDTO;
import com.yf.course.modules.course.dto.request.CourseNotifyReqDTO;
import com.yf.course.modules.course.dto.request.UserCourseReqDTO;
import com.yf.course.modules.course.dto.response.UserCourseRespDTO;
import com.yf.course.modules.course.entity.Course;

import java.util.List;

/**
 * <p>
 * 课程信息业务类
 * </p>
 * <AUTHOR>
 * @since 2020-12-17 11:28
 */
public interface CourseService extends IService<Course> {

    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<CourseDTO> paging(PagingReqDTO<CourseDTO> reqDTO);

    /**
     * 删除课程
     * @param ids
     */
    void delete(List<String> ids);

    /**
     * 保存课程
     * @param reqDTO
     */
    void save(CourseDetailExtDTO reqDTO);

    /**
     * 详情用于学员端展示
     * @param id
     * @return
     */
    CourseDetailExtDTO detail(String id);

    /**
     * 详情用于管理端编辑
     * @param id
     * @return
     */
    CourseDetailExtDTO detailForUpdate(String id);

    /**
     * 用户课程分页列表
     * @param userId
     * @param deptCode
     * @param reqDTO
     * @return
     */
    IPage<UserCourseRespDTO> userPaging(String userId, String deptCode, PagingReqDTO<UserCourseReqDTO> reqDTO);


    /**
     * 获取可学习状态
     * @param course
     * @param userId
     * @return
     */
    int canLearn(Course course, String userId);

    /**
     * 查找课程简略信息，用于校验
     * @param courseId
     * @return
     */
    Course findSimple(String courseId);

    /**
     * 付费购买
     * @param courseId
     * @param userId
     * @return
     */
    String buy(String courseId, String userId);

    /**
     * 发送通知
     * @param reqDTO
     */
    void prepareNotify(CourseNotifyReqDTO reqDTO);

    /**
     * 查找关联的课程数量
     * @param examId
     * @return
     */
    int joinCount(String examId);


    /**
     * 查找详情
     * @param id
     * @return
     */
    CourseDetailExtDTO findDetail(String id);


    /**
     * 保存课程-新版，支持多级目录
     * @param reqDTO
     */
    void saveCourse(CourseDetailExtDTO reqDTO);


}

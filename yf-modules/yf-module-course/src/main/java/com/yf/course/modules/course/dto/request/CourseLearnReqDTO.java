package com.yf.course.modules.course.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 课程学习记录数据传输类
 * </p>
 * <AUTHOR>
 * @since 2020-12-17 11:28
 */
@Data
@ApiModel(value = "课程学习记录请求类", description = "课程学习记录请求类")
public class CourseLearnReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程ID", required = true)
    private String courseId;

    @ApiModelProperty(value = "用户ID", required = true)
    private String userName;

}

package com.yf.notify.modules.sms.providers.aliyun.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 短信发送
 * <AUTHOR>
 */
@Data
public class AliyunSmsConfig {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "秘钥ID")
    private String accessKeyId;

    @ApiModelProperty(value = "秘钥密码")
    private String accessKeySecret;

    @ApiModelProperty(value = "短信签名")
    private String sign;
}

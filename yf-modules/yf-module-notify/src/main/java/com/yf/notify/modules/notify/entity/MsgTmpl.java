package com.yf.notify.modules.notify.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * <p>
 * 消息模板实体类
 * </p>
 * <AUTHOR>
 * @since 2022-11-08 10:54
 */
@Data
@TableName("el_msg_tmpl")
public class MsgTmpl extends Model<MsgTmpl> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息模板
     */
    private String template;

    /**
     * 站内信可用
     */
    @TableField("im_enable")
    private Boolean imEnable;

    /**
     * 站内信状态
     */
    @TableField("im_on")
    private Boolean imOn;

    /**
     * 短信可用状态
     */
    @TableField("sms_enable")
    private Boolean smsEnable;

    /**
     * 短信开启状态
     */
    @TableField("sms_on")
    private Boolean smsOn;

    /**
     * 服务商短信模板
     */
    @TableField("sms_tmpl")
    private String smsTmpl;

    /**
     * 社媒可用状态
     */
    @TableField("social_enable")
    private Boolean socialEnable;

    /**
     * 社媒开启状态
     */
    @TableField("social_on")
    private Boolean socialOn;

    /**
     * 邮件可用状态
     */
    @TableField("email_enable")
    private Boolean emailEnable;

    /**
     * 邮件开启状态
     */
    @TableField("email_on")
    private Boolean emailOn;

    /**
     * 服务商邮件模板
     */
    @TableField("email_tmpl")
    private String emailTmpl;

}

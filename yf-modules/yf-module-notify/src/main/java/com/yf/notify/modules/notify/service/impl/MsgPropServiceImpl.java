package com.yf.notify.modules.notify.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.base.utils.BeanMapper;
import com.yf.notify.modules.notify.dto.MsgPropDTO;
import com.yf.notify.modules.notify.entity.MsgProp;
import com.yf.notify.modules.notify.mapper.MsgPropMapper;
import com.yf.notify.modules.notify.service.MsgPropService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 消息参数业务实现类
 * </p>
 * <AUTHOR>
 * @since 2022-11-08 10:54
 */
@Service
public class MsgPropServiceImpl extends ServiceImpl<MsgPropMapper, MsgProp> implements MsgPropService {


    @Override
    public List<MsgPropDTO> list(MsgPropDTO reqDTO) {

        //分页查询并转换
        QueryWrapper<MsgProp> wrapper = new QueryWrapper<>();

        //转换并返回
        List<MsgProp> list = this.list(wrapper);

        //转换数据
        List<MsgPropDTO> dtoList = BeanMapper.mapList(list, MsgPropDTO.class);

        return dtoList;
    }

    @Override
    public void saveAll(String msgId, Map<String, String> params) {

        List<MsgProp> list = new ArrayList<>();
        for (String key : params.keySet()) {
            MsgProp prop = new MsgProp();
            prop.setProp(key);
            prop.setMsgId(msgId);
            prop.setValue(params.get(key));
            list.add(prop);
        }

        this.saveBatch(list);
    }

    @Override
    public LinkedHashMap<String, String> findAll(String msgId) {
        //分页查询并转换
        QueryWrapper<MsgProp> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MsgProp::getMsgId, msgId);
        List<MsgProp> list = this.list(wrapper);
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (MsgProp prop : list) {
                map.put(prop.getProp(), prop.getValue());
            }
        }
        return map;
    }

    @Override
    public void removeByMsg(List<String> msgIds) {
        QueryWrapper<MsgProp> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(MsgProp::getMsgId, msgIds);
        this.remove(wrapper);
    }


}

package com.yf.notify.modules.notify.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * <p>
 * 模板参数实体类
 * </p>
 * <AUTHOR>
 * @since 2022-11-08 10:54
 */
@Data
@TableName("el_msg_tmpl_prop")
public class MsgTmplProp extends Model<MsgTmplProp> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 模板ID
     */
    @TableField("tmpl_id")
    private String tmplId;

    /**
     * 参数名称
     */
    private String prop;

    /**
     * 参数解释
     */
    private String remark;

}

package com.yf.notify.modules.notify.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.notify.modules.notify.dto.MsgTmplPropDTO;
import com.yf.notify.modules.notify.entity.MsgTmplProp;

import java.util.List;

/**
 * <p>
 * 模板参数业务接口类
 * </p>
 * <AUTHOR>
 * @since 2022-11-08 10:54
 */
public interface MsgTmplPropService extends IService<MsgTmplProp> {


    /**
     * 查找列表
     * @param tmplId
     * @return
     */
    List<MsgTmplPropDTO> listAll(String tmplId);
}

package com.yf.notify.modules.notify.dto.response;

import com.yf.notify.modules.notify.dto.MsgDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 系统消息数据传输类
 * </p>
 * <AUTHOR>
 * @since 2022-11-08 10:54
 */
@Data
@ApiModel(value = "消息分页响应类", description = "消息分页响应类")
public class MsgRespDTO extends MsgDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "发送数量", required = true)
    private Integer sendCount;

    @ApiModelProperty(value = "已读数量", required = true)
    private Integer readCount;


}

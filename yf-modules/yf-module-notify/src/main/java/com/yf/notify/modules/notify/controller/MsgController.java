package com.yf.notify.modules.notify.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import com.yf.base.api.api.dto.BaseIdReqDTO;
import com.yf.base.api.api.dto.BaseIdsReqDTO;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.notify.modules.notify.dto.MsgDTO;
import com.yf.notify.modules.notify.dto.request.MsgTestDTO;
import com.yf.notify.modules.notify.dto.response.MsgRespDTO;
import com.yf.notify.modules.notify.service.MsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 系统消息控制器
 * </p>
 * <AUTHOR>
 * @since 2022-11-08 10:54
 */
@Api(tags = {"系统消息"})
@RestController
@RequestMapping("/api/notify/msg")
public class MsgController extends BaseController {

    @Autowired
    private MsgService baseService;


    /**
     * 测试消息发送
     * @param reqDTO
     * @return
     */
    @RequiresPermissions(value = {"notify:msg:send", "notfiy:tmpl:list"}, logical = Logical.OR)
    @ApiOperation(value = "测试消息发送")
    @PostMapping("/test-send")
    public ApiRest send(@RequestBody MsgTestDTO reqDTO) {
        baseService.testNotify(reqDTO);
        return super.success();
    }

    /**
     * 提交发送消息
     * @param reqDTO
     * @return
     */
    @RequiresPermissions("notify:msg:send")
    @ApiOperation(value = "提交发送消息", notes = "用于重发或发送失败的重发")
    @PostMapping("/trigger-send")
    public ApiRest trigger(@RequestBody BaseIdsReqDTO reqDTO) {
        baseService.triggerSend(reqDTO.getIds());
        return super.success();
    }


    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @RequiresPermissions("notify:msg:delete")
    @ApiOperation(value = "批量删除")
    @PostMapping("/delete")
    public ApiRest delete(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        baseService.delete(reqDTO.getIds());
        return super.success();
    }

    /**
     * 查找详情
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "查找详情")
    @PostMapping("/detail")
    public ApiRest<MsgDTO> detail(@RequestBody BaseIdReqDTO reqDTO) {
        MsgDTO dto = baseService.detail(reqDTO.getId());
        return super.success(dto);
    }

    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "分页查找")
    @RequiresPermissions("notify:msg:list")
    @PostMapping("/paging")
    public ApiRest<IPage<MsgRespDTO>> paging(@RequestBody PagingReqDTO<MsgDTO> reqDTO) {
        //分页查询并转换
        IPage<MsgRespDTO> page = baseService.paging(reqDTO);
        return super.success(page);
    }


}

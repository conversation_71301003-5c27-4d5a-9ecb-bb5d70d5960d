package com.yf.notify.modules.notify.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.notify.modules.notify.dto.MsgDTO;
import com.yf.notify.modules.notify.dto.request.MsgQueryDTO;
import com.yf.notify.modules.notify.dto.request.MsgReceiverDTO;
import com.yf.notify.modules.notify.dto.request.MsgSendDTO;
import com.yf.notify.modules.notify.dto.request.MsgTestDTO;
import com.yf.notify.modules.notify.dto.response.MsgRespDTO;
import com.yf.notify.modules.notify.entity.Msg;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统消息业务接口类
 * </p>
 * <AUTHOR>
 * @since 2022-11-08 10:54
 */
public interface MsgService extends IService<Msg> {

    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<MsgRespDTO> paging(PagingReqDTO<MsgDTO> reqDTO);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    void delete(List<String> ids);


    /**
     * 学员站内信
     * @param reqDTO
     * @return
     */
    IPage<MsgDTO> userPaging(PagingReqDTO<MsgQueryDTO> reqDTO);


    /**
     * 查找详情并标记已读，用于学员查看消息
     * @param msgId
     * @param userId
     * @return
     */
    MsgDTO detailForRead(String msgId, String userId);


    /**
     * 添加或保存
     * @param reqDTO
     * @return
     */
    void save(MsgSendDTO reqDTO);

    /**
     * 提交发送
     * @param ids
     */
    void triggerSend(List<String> ids);

    /**
     * 查找详情
     * @param id
     * @return
     */
    MsgDTO detail(String id);

    /**
     * 发送消息总入口，根据模板全部发送
     * @param reqDTO
     */
    void sendNotify(MsgSendDTO reqDTO);


    /**
     * 消息测试请求类
     * @param reqDTO
     */
    void testNotify(MsgTestDTO reqDTO);

    /**
     * 发送短信
     * @param tmplId
     * @param tos
     * @param params
     */
    void smsNotify(String tmplId, List<MsgReceiverDTO> tos, Map<String, String> params);

    /**
     * 邮件发送
     * @param tmplId 消息模板
     * @param tos    接收的邮件
     * @param params 参数列表
     */
    void emailNotify(String tmplId,
                     List<MsgReceiverDTO> tos,
                     Map<String, String> params);

    /**
     * 站内信通知
     * @param tmplId
     * @param tos
     * @param params
     */
    void imNotify(String tmplId,
                  List<MsgReceiverDTO> tos,
                  Map<String, String> params);


    /**
     * 人教信息科技发送短信
     * @param phone
     */
    void smsXkNotify(String phone,String code);


    /**
     * 导出站内信通知
     * @param title
     * @param url
     */
    void exportNotify(String title,String url,String userId,String realName);
}

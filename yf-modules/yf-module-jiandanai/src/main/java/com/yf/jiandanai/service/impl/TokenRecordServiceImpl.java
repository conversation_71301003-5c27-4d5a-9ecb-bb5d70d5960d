package com.yf.jiandanai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.ability.redis.service.RedisService;
import com.yf.base.api.exception.ServiceException;
import com.yf.jiandanai.entity.TokenRecord;
import com.yf.jiandanai.mapper.TokenRecordMapper;
import com.yf.jiandanai.service.TokenRecordService;
import com.yf.system.modules.user.UserUtils;
import com.yf.system.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/***
 * @title CustomizeAideServiceImpl
 * @Description
 * <AUTHOR>
 * @create 2023/6/13 15:39
 **/
@Service
@Slf4j
public class TokenRecordServiceImpl extends ServiceImpl<TokenRecordMapper, TokenRecord> implements TokenRecordService {

    @Autowired
    private TokenRecordMapper tokenRecordMapper;
    @Autowired
    private RedisService redisService;


    @Override
    public void saveTokenRecord(Map<String, Object> params) {
        String model = (String) params.get("model");
        String type = (String) params.get("type");
        Integer length = (Integer) params.get("length");
        String currentyyyyMMddWithHyphen = DateUtils.getCurrentyyyyMMddWithHyphen();
        String userId = UserUtils.getUserId();
        //分布式锁,解决重复插入用户
        boolean isLock = redisService.tryLock("lock|saveTokenRecord|" + userId + model, 3000L);
        //加锁
        try {
            if (isLock) {//上锁成功
                LambdaQueryWrapper<TokenRecord> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TokenRecord::getUserId, userId).eq(TokenRecord::getDate, currentyyyyMMddWithHyphen);
                if (StringUtils.isNotBlank(model)) {
                    queryWrapper.eq(TokenRecord::getModel, model);
                } else {
                    queryWrapper.isNull(TokenRecord::getModel);
                }
                TokenRecord record = tokenRecordMapper.selectOne(queryWrapper);
                if (record == null) {
                    TokenRecord tokenRecord = new TokenRecord();
                    tokenRecord.setCompletionTokens(0);
                    tokenRecord.setPromptTokens(0);
                    tokenRecord.setTotalTokens(0);
                    if ("questionLength".equals(type)) {
                        tokenRecord.setQuestionLength(length);
                        tokenRecord.setAnswerLength(0);
                    } else {
                        tokenRecord.setQuestionLength(0);
                        tokenRecord.setAnswerLength(length);
                    }
                    tokenRecord.setTotalLength(length);
                    tokenRecord.setDate(DateUtils.getCurrentyyyyMMddWithHyphen());
                    tokenRecord.setUserId(UserUtils.getUserId());
                    tokenRecord.setModel(model);
                    tokenRecordMapper.insert(tokenRecord);
                } else {
                    if ("questionLength".equals(type)) {
                        record.setQuestionLength(record.getQuestionLength() + length);
                        record.setTotalLength(record.getTotalLength() + length);
                    } else {
                        record.setAnswerLength(record.getAnswerLength() + length);
                        record.setTotalLength(record.getTotalLength() + length);
                    }
                    tokenRecordMapper.updateById(record);
                }
            } else {//上锁失败
                throw new ServiceException("点击频繁，请稍后再试");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (isLock) {
                //解锁
                redisService.del("lock|saveTokenRecord|" + userId + model);
            }
        }
    }
}

package com.yf.jiandanai.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.ability.Constant;
import com.yf.ability.redis.service.RedisService;
import com.yf.ability.upload.providers.local.utils.OssUtils;
import com.yf.ability.upload.service.UploadService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.utils.jackson.JsonHelper;
import com.yf.jiandanai.dto.DrawingWorksDTO;
import com.yf.jiandanai.entity.*;
import com.yf.jiandanai.mapper.*;
import com.yf.jiandanai.service.ChatModelService;
import com.yf.jiandanai.service.DrawingService;
import com.yf.sign.service.impl.OCRService;
import com.yf.system.modules.points.entity.UserPoints;
import com.yf.system.modules.points.service.UserPointsService;
import com.yf.system.modules.user.UserUtils;
import com.yf.system.modules.user.entity.SysUser;
import com.yf.system.modules.user.service.SysUserService;
import com.yf.system.utils.CopyUtils;
import com.yf.system.utils.DateUtils;
import com.yf.system.utils.ImageLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/***
 * @title DrawingDescriptionServiceImpl
 * @Description
 * <AUTHOR>
 * @create 2023/6/28 14:10
 **/

@Slf4j
@Service
public class DrawingServiceImpl extends ServiceImpl<DrawingDescriptionMapper, DrawingDescription> implements DrawingService {

    @Autowired
    private DrawingDescriptionMapper drawingDescriptionMapper;
    @Autowired
    private DrawingDescriptionTypeMapper drawingDescriptionTypeMapper;

    @Autowired
    private UserPointsService userPointsService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DrawingWorksMapper drawingWorksMapper;
    @Autowired
    private DrawingWorksLikeMapper drawingWorksLikeMapper;
    @Autowired
    private MidjourneyDictionaryMapper midjourneyDictionaryMapper;
    @Autowired
    private OCRService ocrService;
    @Autowired
    private UploadService uploadService;

    @Value("${aliyun.oss.publicBucket}")
    private String publicBucket;

    @Autowired
    private ChatModelService chatModelService;

    @Override
    public void saveDrawingDescriptionType(DrawingDescriptionType drawingDescriptionType) {
        if (StringUtils.isNotEmpty(drawingDescriptionType.getId())) {
            DrawingDescriptionType selectById = drawingDescriptionTypeMapper.selectById(drawingDescriptionType.getId());
            CopyUtils.copyPropertiesIgnoreNull(drawingDescriptionType, selectById);
            drawingDescriptionTypeMapper.updateById(selectById);
        } else {
            drawingDescriptionTypeMapper.insert(drawingDescriptionType);
        }
    }

    @Override
    public void saveDrawingDescription(DrawingDescription drawingDescription) {
        if (StringUtils.isNotEmpty(drawingDescription.getId())) {
            DrawingDescription selectById = drawingDescriptionMapper.selectById(drawingDescription.getId());
            CopyUtils.copyPropertiesIgnoreNull(drawingDescription, selectById);
            drawingDescriptionMapper.updateById(selectById);
        } else {
            drawingDescriptionMapper.insert(drawingDescription);
        }
    }

    @Override
    public void deleteDrawingDescriptionType(String drawingDescriptionTypeId) {
        LambdaQueryWrapper<DrawingDescription> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DrawingDescription::getDrawingDescriptionTypeId, drawingDescriptionTypeId);
        drawingDescriptionMapper.delete(queryWrapper);
        drawingDescriptionTypeMapper.deleteById(drawingDescriptionTypeId);
    }

    @Override
    public void deleteDrawingDescription(String drawingDescriptionId) {
        drawingDescriptionMapper.deleteById(drawingDescriptionId);
    }

    @Override
    public List<DrawingDescriptionType> getDrawingDescriptionTypeList() {
        List<DrawingDescriptionType> drawingDescriptionTypeList = drawingDescriptionTypeMapper.selectList(null);
        for (DrawingDescriptionType drawingDescriptionType : drawingDescriptionTypeList) {
            List<DrawingDescription> drawingDescriptionList = this.getDrawingDescriptionList(drawingDescriptionType.getId());
            drawingDescriptionType.setDrawingDescriptionList(drawingDescriptionList);
        }
        return drawingDescriptionTypeList;
    }

    @Override
    public List<DrawingDescription> getDrawingDescriptionList(String drawingDescriptionTypeId) {
        LambdaQueryWrapper<DrawingDescription> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(drawingDescriptionTypeId)) {
            queryWrapper.eq(DrawingDescription::getDrawingDescriptionTypeId, drawingDescriptionTypeId);
        }
        List<DrawingDescription> drawingDescriptions = drawingDescriptionMapper.selectList(queryWrapper);
        return drawingDescriptions;
    }


    @Override
    public Map<String, Object> checkUserPoint(HttpServletRequest request) throws Exception {
        String token = Constant.getHeaderToken(request);
        String userId = null;
        if (StringUtils.isNotEmpty(token)) {
            userId = sysUserService.getUserIdByToken(token);
        } else {
            return null;
        }
        Boolean flag = false;
        Map<String, Object> map = new HashMap<>();
        QueryWrapper<UserPoints> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(UserPoints::getUserId, userId)
                .eq(UserPoints::getRefId, "drawing");
        int count = (int) userPointsService.count(wrapper);
        if (count > 0) {
            map.put("isSent", true);
        } else {
            //如果有积分不赠送
            SysUser sysUser = sysUserService.getById(userId);
            Integer points = sysUser.getPoints();
            if (points > 0) {
                map.put("isSent", true);
            } else {
                flag = true;
                map.put("isSent", false);
            }
        }
        if (flag) {
            //赠送积分
            userPointsService.incr(userId, 10, "获取绘画积分", "drawing");
        }
        SysUser byId = sysUserService.getById(userId);
        Integer points = byId.getPoints();
        map.put("points", points);
        Integer monthlyPoints = byId.getMonthlyPoints();
        map.put("monthlyPoints", monthlyPoints);
        return map;
    }

    @Override
    public Integer updatePoint(String type) throws Exception {
        String userId = UserUtils.getUserId();
        //分布式锁,解决重复插入用户
        boolean isLock = redisService.tryLock("lock|updatePoint|" + userId, 1000L);
        //加锁
        try {
            if (isLock) {//上锁成功
                SysUser byId = sysUserService.getById(userId);
                Integer points = byId.getPoints();
                if (points == 0) {
                    throw new ServiceException("积分不够");
                }
                if (StringUtils.isEmpty(type)) {
                    type = "Midjourney";
                }
                if ("Midjourney".equals(type)) {
                    userPointsService.use(userId, 1, "AI绘画消耗积分", userId);
                } else if ("GPT-4".equals(type)) {
                    userPointsService.use(userId, 2, "GPT-4消耗积分", userId);
                }

                return points - 1;
            } else {
                throw new ServiceException("请勿重复提交");
            }
        } finally {
            if (isLock) {
                //解锁
                redisService.del("lock|updatePoint|" + userId);
            }
        }
    }

    @Override
    public void saveDrawingWorks(DrawingWorks drawingWorks) throws IOException {
        if (StringUtils.isNotEmpty(drawingWorks.getId())) {
            DrawingWorks selectById = drawingWorksMapper.selectById(drawingWorks.getId());
            CopyUtils.copyPropertiesIgnoreNull(drawingWorks, selectById);
            drawingWorksMapper.updateById(selectById);
        } else {
            String url = drawingWorks.getUrl();
            InputStream inputStreamFromUrl = OssUtils.getInputStreamFromUrl(url,true);
            byte[] bytes = OssUtils.toByteArray(inputStreamFromUrl);
            String newUrl = OssUtils.uploadFileByByte(bytes, publicBucket, "/ai/drawingWorks/" + DateUtils.getCurrentyyyyMMdd(), UUID.randomUUID() + ".png");
            drawingWorks.setUrl(newUrl);
            drawingWorks.setUserId(UserUtils.getUserId());
            drawingWorks.setLikeNum(0);
            drawingWorks.setState(0);
            drawingWorksMapper.insert(drawingWorks);
        }
    }

    @Override
    public IPage<DrawingWorks> drawingWorksPaging(PagingReqDTO<DrawingWorksDTO> reqDTO, HttpServletRequest request) {
        String token = Constant.getHeaderToken(request);
        String userId = null;
        if (StringUtils.isNotEmpty(token)) {
            userId = sysUserService.getUserIdByToken(token);
        }

        //创建分页对象
        IPage<DrawingWorks> query = new Page<>(reqDTO.getCurrent(), reqDTO.getSize());

        //查询条件
        QueryWrapper<DrawingWorks> wrapper = new QueryWrapper<>();
        DrawingWorksDTO params = reqDTO.getParams();
        if (Objects.nonNull(params)) {
            Integer type = params.getType();
            Integer state = params.getState();
            if (state != null) {
                wrapper.lambda().eq(DrawingWorks::getState, state);
            }

            if (type == 3) {
                if (StringUtils.isBlank(userId)) {
                    wrapper.lambda().eq(DrawingWorks::getUserId, "0");
                } else {
                    //我喜欢的绘画作品单独查询
                    LambdaQueryWrapper<DrawingWorksLike> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(DrawingWorksLike::getUserId, userId);
                    List<DrawingWorksLike> drawingWorksLikes = drawingWorksLikeMapper.selectList(lambdaQueryWrapper);
                    if (drawingWorksLikes != null && drawingWorksLikes.size() > 0) {
                        List<String> worksIds = drawingWorksLikes.stream().map(DrawingWorksLike::getDrawingWorksId).collect(Collectors.toList());
                        wrapper.lambda().in(DrawingWorks::getId, worksIds);
                    } else {
                        wrapper.lambda().eq(DrawingWorks::getUserId, "0");
                    }
                }
                IPage<DrawingWorks> page = drawingWorksMapper.selectPage(query, wrapper);
                List<DrawingWorks> records = page.getRecords();
                for (DrawingWorks record : records) {
                    record.setIsLike(true);
                    String authorId = record.getUserId();
                    SysUser author = sysUserService.getById(authorId);
                    if (author != null) {
                        record.setAuthorName(author.getNickname());
                        record.setAuthorAvatar(author.getAvatar());
                    }
                    if (StringUtils.isNotBlank(record.getModelId())) {
                        ChatModel chatModel = null;
                        Map<String, Object> json = redisService.getJson("model:" + record.getModelId());
                        if (json != null) {
                            chatModel = JsonHelper.parseObject(json, ChatModel.class);
                        } else {
                            chatModel = chatModelService.getById(record.getModelId());
                        }
                        record.setModel(chatModel.getModel());
                        record.setModelLogo(chatModel.getModelLogo());
                    }
                }
                //转换结果
                IPage<DrawingWorks> pageData = JSON.parseObject(JSON.toJSONStringWithDateFormat(page, "yyyy-MM-dd HH:mm:ss"), new TypeReference<Page<DrawingWorks>>() {
                });
                return pageData;
            } else {
                if (type == 1) {
                    wrapper.lambda().orderByDesc(DrawingWorks::getCreateDate);
                } else if (type == 2) {
                    wrapper.lambda().orderByDesc(DrawingWorks::getLikeNum);
                } else if (type == 4) {
                    if (StringUtils.isBlank(userId)) {
                        wrapper.lambda().eq(DrawingWorks::getUserId, "0");
                    } else {
                        wrapper.lambda().eq(DrawingWorks::getUserId, userId);
                    }
                }
            }
        }
        //获得数据
        IPage<DrawingWorks> page = drawingWorksMapper.selectPage(query, wrapper);
        List<DrawingWorks> records = page.getRecords();
        for (DrawingWorks record : records) {
            String authorId = record.getUserId();
            SysUser author = sysUserService.getById(authorId);
            if (author != null) {
                record.setAuthorName(author.getNickname());
                record.setAuthorAvatar(author.getAvatar());
            }

            if (StringUtils.isNotBlank(record.getModelId())) {
                ChatModel chatModel = null;
                Map<String, Object> json = redisService.getJson("model:" + record.getModelId());
                if (json != null) {
                    chatModel = JsonHelper.parseObject(json, ChatModel.class);
                } else {
                    chatModel = chatModelService.getById(record.getModelId());
                }
                record.setModel(chatModel.getModel());
                record.setModelLogo(chatModel.getModelLogo());
            }
            if (StringUtils.isNotBlank(userId)) {
                //判断是否赞过
                LambdaQueryWrapper<DrawingWorksLike> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DrawingWorksLike::getDrawingWorksId, record.getId());
                queryWrapper.eq(DrawingWorksLike::getUserId, userId);
                int count = drawingWorksLikeMapper.selectCount(queryWrapper).intValue();
                if (count > 0) {
                    record.setIsLike(true);
                } else {
                    record.setIsLike(false);
                }
            } else {
                record.setIsLike(false);
            }

        }
        //转换结果
        IPage<DrawingWorks> pageData = JSON.parseObject(JSON.toJSONStringWithDateFormat(page, "yyyy-MM-dd HH:mm:ss"), new TypeReference<Page<DrawingWorks>>() {
        });
        return pageData;
    }

    @Override
    public void drawingWorksLike(String drawingWorksId) {
        LambdaQueryWrapper<DrawingWorksLike> drawingWorksLikeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        drawingWorksLikeLambdaQueryWrapper.eq(DrawingWorksLike::getDrawingWorksId, drawingWorksId);
        drawingWorksLikeLambdaQueryWrapper.eq(DrawingWorksLike::getUserId, UserUtils.getUserId());
        int count = drawingWorksLikeMapper.selectCount(drawingWorksLikeLambdaQueryWrapper).intValue();
        if (count > 0) {
            throw new ServiceException("已经点赞过了");
        }
        DrawingWorks drawingWorks = drawingWorksMapper.selectById(drawingWorksId);
        drawingWorks.setLikeNum(drawingWorks.getLikeNum() + 1);
        drawingWorksMapper.updateById(drawingWorks);
        DrawingWorksLike drawingWorksLike = new DrawingWorksLike();
        drawingWorksLike.setDrawingWorksId(drawingWorksId);
        drawingWorksLike.setUserId(UserUtils.getUserId());
        drawingWorksLike.setDrawingWorksUserId(drawingWorks.getUserId());
        drawingWorksLikeMapper.insert(drawingWorksLike);
    }

    @Override
    public void drawingWorksCancelLike(String drawingWorksId) {
        DrawingWorks drawingWorks = drawingWorksMapper.selectById(drawingWorksId);
        if (drawingWorks.getLikeNum() > 0) {
            drawingWorks.setLikeNum(drawingWorks.getLikeNum() - 1);
            drawingWorksMapper.updateById(drawingWorks);
        }
        String currentUserId = UserUtils.getUserId();
        LambdaQueryWrapper<DrawingWorksLike> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(DrawingWorksLike::getDrawingWorksId, drawingWorksId);
        queryWrapper.eq(DrawingWorksLike::getUserId, currentUserId);
        DrawingWorksLike drawingWorksLike = drawingWorksLikeMapper.selectOne(queryWrapper);
        if (drawingWorksLike != null) {
            String drawingWorksLikeId = drawingWorksLike.getId();
            drawingWorksLikeMapper.deleteById(drawingWorksLikeId);
        }
    }

    @Override
    public void saveMidjourneyDictionary() throws Exception {
        String url = "/Users/<USER>/Desktop/midjourney/中国元素_2.png";
        String type = "中国元素";
        ImageLoader imageLoader = new ImageLoader(url);

        String saveWordEN1 = this.saveWord(imageLoader, 544, 391, 420, 132);
        String saveWordCN1 = this.saveWord(imageLoader, 544, 569, 420, 132);
        String picture1 = this.savePicture(imageLoader, 141, 294, 390, 390, saveWordEN1);
        MidjourneyDictionary midjourneyDictionary1 = new MidjourneyDictionary();
        midjourneyDictionary1.setDescription(saveWordCN1);
        midjourneyDictionary1.setEnglish(saveWordEN1);
        midjourneyDictionary1.setPictureUrl(picture1);
        midjourneyDictionary1.setType(type);
        midjourneyDictionaryMapper.insert(midjourneyDictionary1);

        String saveWordEN2 = this.saveWord(imageLoader, 1332, 391, 420, 132);
        String saveWordCN2 = this.saveWord(imageLoader, 1332, 569, 420, 132);
        String picture2 = this.savePicture(imageLoader, 924, 294, 390, 390, saveWordEN2);
        MidjourneyDictionary midjourneyDictionary2 = new MidjourneyDictionary();
        midjourneyDictionary2.setDescription(saveWordCN2);
        midjourneyDictionary2.setEnglish(saveWordEN2);
        midjourneyDictionary2.setPictureUrl(picture2);
        midjourneyDictionary2.setType(type);
        midjourneyDictionaryMapper.insert(midjourneyDictionary2);

        String saveWordEN3 = this.saveWord(imageLoader, 544, 913, 420, 132);
        String saveWordCN3 = this.saveWord(imageLoader, 544, 1091, 420, 132);
        String picture3 = this.savePicture(imageLoader, 141, 819, 390, 390, saveWordEN3);
        MidjourneyDictionary midjourneyDictionary3 = new MidjourneyDictionary();
        midjourneyDictionary3.setDescription(saveWordCN3);
        midjourneyDictionary3.setEnglish(saveWordEN3);
        midjourneyDictionary3.setPictureUrl(picture3);
        midjourneyDictionary3.setType(type);
        midjourneyDictionaryMapper.insert(midjourneyDictionary3);

        String saveWordEN4 = this.saveWord(imageLoader, 1332, 913, 420, 132);
        String saveWordCN4 = this.saveWord(imageLoader, 1332, 1091, 420, 132);
        String picture4 = this.savePicture(imageLoader, 924, 819, 390, 390, saveWordEN4);
        MidjourneyDictionary midjourneyDictionary4 = new MidjourneyDictionary();
        midjourneyDictionary4.setDescription(saveWordCN4);
        midjourneyDictionary4.setEnglish(saveWordEN4);
        midjourneyDictionary4.setPictureUrl(picture4);
        midjourneyDictionary4.setType(type);
        midjourneyDictionaryMapper.insert(midjourneyDictionary4);

        String saveWordEN5 = this.saveWord(imageLoader, 544, 1451, 420, 132);
        String saveWordCN5 = this.saveWord(imageLoader, 544, 1617, 420, 132);
        String picture5 = this.savePicture(imageLoader, 141, 1343, 390, 390, saveWordEN5);
        MidjourneyDictionary midjourneyDictionary5 = new MidjourneyDictionary();
        midjourneyDictionary5.setDescription(saveWordCN5);
        midjourneyDictionary5.setEnglish(saveWordEN5);
        midjourneyDictionary5.setPictureUrl(picture5);
        midjourneyDictionary5.setType(type);
        midjourneyDictionaryMapper.insert(midjourneyDictionary5);

        String saveWordEN6 = this.saveWord(imageLoader, 1332, 1451, 420, 132);
        String saveWordCN6 = this.saveWord(imageLoader, 1332, 1617, 420, 132);
        String picture6 = this.savePicture(imageLoader, 924, 1343, 390, 390, saveWordEN6);
        MidjourneyDictionary midjourneyDictionary6 = new MidjourneyDictionary();
        midjourneyDictionary6.setDescription(saveWordCN6);
        midjourneyDictionary6.setEnglish(saveWordEN6);
        midjourneyDictionary6.setPictureUrl(picture6);
        midjourneyDictionary6.setType(type);
        midjourneyDictionaryMapper.insert(midjourneyDictionary6);

        String saveWordEN7 = this.saveWord(imageLoader, 544, 1967, 420, 132);
        String saveWordCN7 = this.saveWord(imageLoader, 544, 2145, 420, 132);
        String picture7 = this.savePicture(imageLoader, 141, 1869, 390, 390, saveWordEN7);
        MidjourneyDictionary midjourneyDictionary7 = new MidjourneyDictionary();
        midjourneyDictionary7.setDescription(saveWordCN7);
        midjourneyDictionary7.setEnglish(saveWordEN7);
        midjourneyDictionary7.setPictureUrl(picture7);
        midjourneyDictionary7.setType(type);
        midjourneyDictionaryMapper.insert(midjourneyDictionary7);

        String saveWordEN8 = this.saveWord(imageLoader, 1332, 1967, 420, 132);
        String saveWordCN8 = this.saveWord(imageLoader, 1332, 2145, 420, 132);
        String picture8 = this.savePicture(imageLoader, 924, 1869, 390, 390, saveWordEN8);
        MidjourneyDictionary midjourneyDictionary8 = new MidjourneyDictionary();
        midjourneyDictionary8.setDescription(saveWordCN8);
        midjourneyDictionary8.setEnglish(saveWordEN8);
        midjourneyDictionary8.setPictureUrl(picture8);
        midjourneyDictionary8.setType(type);
        midjourneyDictionaryMapper.insert(midjourneyDictionary8);

    }

    @Override
    public String saveWord(ImageLoader imageLoader, int posX, int posY, int width, int height) throws Exception {
        //描述词
        ImageIcon iconImageWord = imageLoader.getIconImage(posX, posY, width, height);
        Image imageWord = iconImageWord.getImage();
        //输出图片
        BufferedImage bufferedImageWord = new BufferedImage(imageWord.getWidth(null), imageWord.getHeight(null), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2Word = bufferedImageWord.createGraphics();
        g2Word.drawImage(imageWord, 0, 0, null);
        g2Word.dispose();
        ImageIO.write(bufferedImageWord, "png", new File("/Users/<USER>/Desktop/temp/" + posX + posY + "Word.png"));
        byte[] bytesWord = ImageLoader.imageToBytes("/Users/<USER>/Desktop/temp/" + posX + posY + "Word.png");
        String word = OssUtils.uploadFileByByte(bytesWord, "static-kamal", "/temp/" + DateUtils.getCurrentyyyyMMdd(), UUID.randomUUID() + ".png");
        System.out.println(word);
        JSONObject jsonObject = ocrService.recognizeImage(word, false);
        String content = (String) jsonObject.get("content");
        String trim = content.trim();
        return trim;
    }


    @Override
    public String savePicture(ImageLoader imageLoader, int posX, int posY, int width, int height, String word) throws Exception {
        //描述图
        ImageIcon iconImagePicture = imageLoader.getIconImage(posX, posY, width, height);
        Image imagePicture = iconImagePicture.getImage();
        //输出图片
        BufferedImage bufferedImagePicture = new BufferedImage(imagePicture.getWidth(null), imagePicture.getHeight(null), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2Picture = bufferedImagePicture.createGraphics();
        g2Picture.drawImage(imagePicture, 0, 0, null);
        g2Picture.dispose();
        ImageIO.write(bufferedImagePicture, "png", new File("/Users/<USER>/Desktop/temp/" + word + ".png"));
        byte[] bytesPicture = ImageLoader.imageToBytes("/Users/<USER>/Desktop/temp/" + word + ".png");
        String pictureUrl = OssUtils.uploadFileByByte(bytesPicture, "static-kamal", "/ai/midjourney/", word + ".png");
        return pictureUrl;
    }

    @Override
    public List<Map<String, Object>> getMidjourneyDictionaryList() {

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, List<MidjourneyDictionary>> map = new HashMap<>();
//        LambdaQueryWrapper<MidjourneyDictionary> midjourneyDictionaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        midjourneyDictionaryLambdaQueryWrapper.isNull(MidjourneyDictionary::getSubtype);
        List<MidjourneyDictionary> midjourneyDictionaryList = midjourneyDictionaryMapper.selectList(null);
        for (MidjourneyDictionary midjourneyDictionary : midjourneyDictionaryList) {

            String type = midjourneyDictionary.getType();

            if (map.containsKey(type)) {
                map.get(type).add(midjourneyDictionary);
            } else {
                ArrayList<MidjourneyDictionary> midjourneyDictionaries = new ArrayList<>();
                midjourneyDictionaries.add(midjourneyDictionary);
                map.put(type, midjourneyDictionaries);
            }
        }
        for (Map.Entry<String, List<MidjourneyDictionary>> stringListEntry : map.entrySet()) {
            String key = stringListEntry.getKey();
            List<MidjourneyDictionary> value = stringListEntry.getValue();
            HashMap<String, Object> result = new HashMap<>();
            result.put("name", key);
            result.put("list", value);
            ArrayList<Map<String, Object>> mapArrayList = new ArrayList<>();
            Set<String> collect = value.stream().map(MidjourneyDictionary::getSubtype).collect(Collectors.toSet());
            if (collect != null && collect.size() > 1) {
                for (String subtype : collect) {
                    HashMap<String, Object> subtypeResult = new HashMap<>();
                    LambdaQueryWrapper<MidjourneyDictionary> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MidjourneyDictionary::getType, key).eq(MidjourneyDictionary::getSubtype, subtype);
                    List<MidjourneyDictionary> midjourneyDictionaries = midjourneyDictionaryMapper.selectList(queryWrapper);
                    subtypeResult.put("name", subtype);
                    subtypeResult.put("list", midjourneyDictionaries);
                    mapArrayList.add(subtypeResult);
                }
                result.put("list", mapArrayList);
            }
            if (key.equals("主体")) {
                list.add(0, result);
            } else if (key.equals("人物细节")) {
                list.add(1, result);
            } else {
                list.add(result);
            }
        }
        return list;
    }

    @Override
    public String imageOperation(MultipartFile file, String operation) throws Exception {

        String url = "http://region-45.seetacloud.com:45504";
        String token = "noasdf8un39fansdfl.yrg2u8adshbg.adskjf2fahef";
        ChatModel chatModel = null;
        Map<String, Object> json = redisService.getJson("model:" + operation);
        if (json != null) {
            chatModel = JsonHelper.parseObject(json, ChatModel.class);
        } else {
            chatModel = chatModelService.getById(operation);
        }

        if (chatModel != null) {
            token = chatModel.getAuthorization();
            url = chatModel.getUrl();
            chatModelService.doChat(operation, IdWorker.getIdStr(), null);
            redisService.set(Constant.MODEL_KEY + operation, JsonHelper.toJson(chatModel));
        }


        // 创建一个临时文件
        File tempFile = File.createTempFile("temp_", "_" + file.getOriginalFilename());
        //tempFile.deleteOnExit(); // 确保文件在JVM退出时被删除
        try {
            HttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            // 设置请求头
            httpPost.setHeader("token", token);
            // 构建请求体

            // 将MultipartFile的内容写入临时文件
            try (InputStream inputStream = file.getInputStream();
                 OutputStream outputStream = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            FileBody fileBody = new FileBody(tempFile, ContentType.DEFAULT_BINARY);
            HttpEntity reqEntity = MultipartEntityBuilder.create()
                    .addPart("file", fileBody)
                    .addTextBody("operation", operation)
                    .build();

            httpPost.setEntity(reqEntity);
            // 发送请求并获取响应
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null) {
                String result = EntityUtils.toString(resEntity, StandardCharsets.UTF_8);
                Map map = JSONObject.parseObject(result, Map.class);
                String data = (String) map.get("data");
                return data;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            tempFile.delete();
        }
        return null;
    }

    @Override
    public void deleteDrawingWorks(String drawingWorksId) {
        String userId = UserUtils.getUserId();
        DrawingWorks drawingWorks = drawingWorksMapper.selectById(drawingWorksId);
        if (drawingWorks == null || !drawingWorks.getUserId().equals(userId)) {
            throw new ServiceException("您无法删除该作品！");
        }
        drawingWorksMapper.deleteById(drawingWorksId);
    }

    @Override
    public void auditDrawingWorks(String drawingWorksId) {
        LambdaUpdateWrapper<DrawingWorks> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DrawingWorks::getId, drawingWorksId);
        updateWrapper.set(DrawingWorks::getState, 1);
        drawingWorksMapper.update(null, updateWrapper);

    }
}

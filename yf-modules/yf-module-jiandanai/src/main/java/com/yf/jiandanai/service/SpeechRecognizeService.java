package com.yf.jiandanai.service;

import com.yf.sign.vo.AuditResult;

import java.io.IOException;

/***
 * @title 阿里云智能语音交互
 * @Description
 * <AUTHOR>
 * @create 2023/7/29 14:50
 **/
public interface SpeechRecognizeService {

    /**
     * 获取阿里云智能语音交互token
     * @return
     * @throws IOException
     */
    String getAliyunAccessToken() throws IOException;

    /**
     * 智能语音交互-一句话识别
     * @return
     * @throws IOException
     */
    AuditResult speechRecognition(String url, String language) throws Exception;


}

package com.yf.jiandanai.factory;


import com.yf.jiandanai.service.OpenAIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ChatFactory {


    @Autowired
    private OpenAIService zhiYunOpenAIService;

    @Autowired
    private OpenAIService azureOpenAIService;

    @Autowired
    private OpenAIService sparkOpenAIService;

    @Autowired
    private OpenAIService baiduOpenAIService;

    @Autowired
    private OpenAIService zhipuOpenAIService;

    /**
     * 获取工厂实例
     * @return
     */
    public OpenAIService getService(String openAIType) {


        if ("azure".equalsIgnoreCase(openAIType)) {
            return azureOpenAIService;
        }

        if ("spark".equalsIgnoreCase(openAIType)) {
            return sparkOpenAIService;
        }

        if ("baidu".equalsIgnoreCase(openAIType)) {
            return baiduOpenAIService;
        }

        if ("zhipu".equalsIgnoreCase(openAIType)) {
            return zhipuOpenAIService;
        }

        if ("zhiyun".equalsIgnoreCase(openAIType)) {
            return zhiYunOpenAIService;
        }

        // 没有正确实例返回openAI
        return zhiYunOpenAIService;

    }
}

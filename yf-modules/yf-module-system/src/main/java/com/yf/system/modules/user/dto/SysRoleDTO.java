package com.yf.system.modules.user.dto;

import com.yf.base.api.annon.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 角色请求类
 * </p>
 * <AUTHOR>
 * @since 2020-04-13 16:57
 */
@Data
@ApiModel(value = "角色", description = "角色")
public class SysRoleDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "角色ID", required = true)
    private String id;


    @NotBlank(message = "角色名称不能为空！")
    @ApiModelProperty(value = "角色名称", required = true)
    private String roleName;

    @NotNull(message = "角色类型不能为空！")
    @Dict(dicCode = "role_type")
    @ApiModelProperty(value = "1学员2管理员", required = true)
    private Integer roleType;
    //private String roleType_dictText;

    @NotNull
    @Dict(dicCode = "data_scope")
    @ApiModelProperty(value = "数据权限", required = true)
    private Integer dataScope;
    //private String dataScope_dictText;

    @ApiModelProperty(value = "越大越高", required = true)
    private Integer roleLevel;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

    @ApiModelProperty(value = "更新时间", required = true)
    private Date updateTime;

    @ApiModelProperty(value = "创建人", required = true)
    private String createBy;

    @ApiModelProperty(value = "修改人", required = true)
    private String updateBy;

    @ApiModelProperty(value = "数据标识", required = true)
    private Integer dataFlag;

}

package com.yf.system.utils;


import com.yf.base.api.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * 日期工具类
 */
public class DateUtils {

    /**
     * 日期格式yyyy-MM-dd
     */
    public static final String pattern_date = "yyyy-MM-dd";

    /**
     * 日期格式yyyy-MM-dd
     */
    public static final String pattern_date2 = "yyyyMMdd";

    /**
     * 日期时间格式yyyy-MM-dd HH:mm:ss
     */
    public static final String pattern_time = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期时间格式yyyyMMddHHmmss
     */
    public static final String pattern_time2 = "yyyyMMddHHmmss";

    public static final int FIRST_DAY_OF_WEEK = Calendar.MONDAY;
    public static final String YYYYMM = "yyyy-MM";

    /**
     * 描述：日期格式化
     * @param date 日期
     * @return 输出格式为 yyyy-MM-dd 的字串
     */
    public static String formatDate(Date date) {
        return formatDate(date, pattern_date);
    }

    /**
     * 描述：日期格式化
     * @param date    日期
     * @param pattern 格式化类型
     * @return
     */
    public static String formatDate(Date date, String pattern) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(date);
    }

    public static String getCurrentyyyyMMddHHmmss() {
        return DateUtils.formatDate(new Date(), pattern_time);
    }

    public static String getCurrentyyyyMMdd() {
        return DateUtils.formatDate(new Date(), "yyyyMMdd");
    }

    public static String getCurrentHHmm() {
        return DateUtils.formatDate(new Date(), "HH:mm");
    }

    public static String getCurrentyyyyMMddWithHyphen() {
        return DateUtils.formatDate(new Date(), pattern_date);
    }

    /**
     * 描述：解析日期字串
     * @param dateStr 日期字串
     * @return 按 yyyy-MM-dd HH:mm:ss 格式解析
     */
    public static Date parseString(String dateStr) {
        return parseString(dateStr, pattern_date);
    }

    /**
     * 描述：解析日期字串
     * @param dateStr 日期字串
     * @param pattern 字串日期格式
     * @return 对应日期类型数据
     */
    public static Date parseString(String dateStr, String pattern) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        try {
            if (!StringUtils.isEmpty(dateStr)) {
                return dateFormat.parse(dateStr);
            }
        } catch (ParseException ex) {
            ex.printStackTrace();
            throw new ServiceException("日期" + dateStr + "不符合格式：" + pattern);
        }
        return null;
    }

    /**
     * @param str 传递的日期字符串
     * @Description:日期转换，将yyyy-MM-dd转为yyyyMMdd
     */
    public static String stringToDate(String str) {
        Date parse = null;
        String dateString = "";
        try {
            parse = new SimpleDateFormat("yyyy-MM-dd").parse(str);
            dateString = new SimpleDateFormat("yyyyMMdd").format(parse);
        } catch (ParseException e) {
            dateString = null;
        }
        return dateString;
    }

    /**
     * @param str 传递的日期字符串
     * @Description:日期转换，将接口返回的20180524转为2018-05-24
     */
    public static String dateConvertion(String str) {
        Date parse = null;
        String dateString = "";
        try {
            parse = new SimpleDateFormat("yyyyMMdd").parse(str);
            dateString = new SimpleDateFormat("yyyy-MM-dd").format(parse);
        } catch (ParseException e) {
            dateString = null;
        }

        return dateString;
    }

    /**
     * 获得入参日期几天后的日期
     * @param date 入参日期
     * @return 该日期几天后的日期
     */
    public static Date getNextDay(Date date, Integer num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, num);//增加日期天数
        return calendar.getTime();
    }

    /**
     * 获得入参日期几年后的日期
     * @param date 入参日期
     * @return 该日期几天后的日期
     */
    public static Date getNextYear(Date date, Integer num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, num);//增加日期天数
        return calendar.getTime();
    }

    /**
     * 获得入参日期周一的日期
     * @param date 入参日期
     * @return 入参日期的周一
     */
    public static Date getMonday(Date date) {
        //获得入参的日期
        Calendar cld = Calendar.getInstance(Locale.CHINA);
        cld.setFirstDayOfWeek(Calendar.MONDAY);//以周一为首日
        cld.setTimeInMillis(date.getTime());//当前时间

        cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);//周一
        return cld.getTime();
    }

    /**
     * 获得入参日期周日的日期
     * @param date 入参日期
     * @return 入参日期的周日
     */
    public static Date getSunday(Date date) {
        //获得入参的日期
        Calendar cld = Calendar.getInstance(Locale.CHINA);
        cld.setFirstDayOfWeek(Calendar.MONDAY);//以周一为首日
        cld.setTimeInMillis(date.getTime());//当前时间

        cld.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);//周日
        return cld.getTime();
    }

    /**
     * 获得入参日期下周一的日期
     * @param date 入参日期
     * @return 入参日期的下周一
     */
    public static Date getNextMonday(Date date) {
        //获得入参的日期
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);

        // 获得入参日期是一周的第几天
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        // 获得入参日期相对于下周一的偏移量（在国外，星期一是一周的第二天，所以下周一是这周的第九天）
        // 若入参日期是周日，它的下周一偏移量是1
        int nextMondayOffset = dayOfWeek == 1 ? 1 : 9 - dayOfWeek;

        // 增加到入参日期的下周一
        cd.add(Calendar.DAY_OF_MONTH, nextMondayOffset);
        return cd.getTime();
    }

    /**
     * 获得入参日期本周的星期几
     * @param date 入参日期
     * @param week 星期
     * @return
     */
    public static Date getThisWeek(Date date, Integer week) {
        //获得入参的日期
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);

        // 获得入参日期是一周的第几天
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 1) {
            dayOfWeek = 7;
        } else {
            dayOfWeek -= 1;
        }
        int nextMondayOffset = week - dayOfWeek;
        cd.add(Calendar.DAY_OF_MONTH, nextMondayOffset);
        return cd.getTime();
    }

    /**
     * 获得入参日期下周的星期几
     * @param date 入参日期
     * @param week 星期
     * @return
     */
    public static Date getNextWeek(Date date, Integer week) {
        //获得入参的日期
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);

        // 获得入参日期是一周的第几天
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == 1) {
            dayOfWeek = 7;
        } else {
            dayOfWeek -= 1;
        }
        int nextMondayOffset = week + 7 - dayOfWeek;
        cd.add(Calendar.DAY_OF_MONTH, nextMondayOffset);
        return cd.getTime();
    }

    /**
     * 获得入参日期下周日的日期
     * @param date 入参日期
     * @return 入参日期的下周日
     */
    public static Date getNextSunday(Date date) {
        //获得入参的日期
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);

        // 获得入参日期是一周的第几天
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
        // 获得入参日期相对于下周日的偏移量（在国外，星期一是一周的第二天，所以下周日相对于本周来说，是第15天）
        // 若入参日期是周日，它的下周日偏移量是7
        int nextMondayOffset = dayOfWeek == 1 ? 7 : 15 - dayOfWeek;

        // 增加到入参日期的下周日
        cd.add(Calendar.DAY_OF_MONTH, nextMondayOffset);
        return cd.getTime();
    }

    /**
     * 获得入参日期下个月的第一天
     * @param date 入参日期
     * @return 入参日期下个月的第一天
     */
    public static Date firstDayOfNextMonth(Date date) {
        //获得入参的日期
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);

        //获取下个月第一天：
        cd.add(Calendar.MONTH, 1);
        //设置为1号,当前日期既为次月第一天
        cd.set(Calendar.DAY_OF_MONTH, 1);

        return cd.getTime();
    }

    /**
     * 获得入参日期次年的第一天
     * @param date 入参日期
     * @return 入参日期次年的第一天
     */
    public static Date firstDayOfNextYear(Date date) {
        //获得入参的日期
        Calendar cd = Calendar.getInstance();
        cd.setTime(date);

        //获取次年第一天：
        cd.add(Calendar.YEAR, 1);
        //设置为1月1号,当前日期既为次年第一天
        cd.set(Calendar.MONTH, 0);
        cd.set(Calendar.DAY_OF_MONTH, 1);

        return cd.getTime();
    }

    /**
     * 获取两个日期之间的所有日期
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @return
     */
    public static List<String> getDays(String startTime, String endTime) {

        // 返回的日期集合
        List<String> days = new ArrayList<String>();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);

            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        }

        return days;
    }

    /**
     * 判断两个日期是否相同
     * @param d1 日期1
     * @param d2 日期2
     * @return 入参日期次年的第一天
     */
    public static boolean sameDate(Date d1, Date d2) {
        LocalDate localDate1 = ZonedDateTime.ofInstant(d1.toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = ZonedDateTime.ofInstant(d2.toInstant(), ZoneId.systemDefault()).toLocalDate();
        return localDate1.isEqual(localDate2);
    }


    public static void addObject(Vector c, String str) {
        if (!c.contains(str)) {
            c.add(str);
        }
    }

    /**
     * 根据日期 找到对应日期的 星期
     */
    public static Integer getDayOfWeekByDate(String strDate) {
        Date date = parseString(strDate);
        //获取当天日期
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        int week = now.get(Calendar.DAY_OF_WEEK) - 1;
        if (week == 0) {
            week = 7;
        }
        return week;
    }

    /**
     * 判断参数的格式是否为“yyyyMMdd”格式的合法日期字符串
     */
    public static boolean isValidDate(String str) {
        try {
            if (str != null && !str.equals("")) {
                if (str.length() == 8) {
                    // 闰年标志
                    boolean isLeapYear = false;
                    String year = str.substring(0, 4);
                    String month = str.substring(4, 6);
                    String day = str.substring(6, 8);
                    int vYear = Integer.parseInt(year);
                    // 判断年份是否合法
                    if (vYear < 1900 || vYear > 2200) {
                        return false;
                    }
                    // 判断是否为闰年
                    if (vYear % 4 == 0 && vYear % 100 != 0 || vYear % 400 == 0) {
                        isLeapYear = true;
                    }
                    // 判断月份
                    // 1.判断月份
                    if (month.startsWith("0")) {
                        String units4Month = month.substring(1, 2);
                        int vUnits4Month = Integer.parseInt(units4Month);
                        if (vUnits4Month == 0) {
                            return false;
                        }
                        if (vUnits4Month == 2) {
                            // 获取2月的天数
                            int vDays4February = Integer.parseInt(day);
                            if (isLeapYear) {
                                if (vDays4February > 29)
                                    return false;
                            } else {
                                if (vDays4February > 28)
                                    return false;
                            }
                        }
                    } else {
                        // 2.判断非0打头的月份是否合法
                        int vMonth = Integer.parseInt(month);
                        if (vMonth != 10 && vMonth != 11 && vMonth != 12) {
                            return false;
                        }
                    }
                    // 判断日期
                    // 1.判断日期
                    if (day.startsWith("0")) {
                        String units4Day = day.substring(1, 2);
                        int vUnits4Day = Integer.parseInt(units4Day);
                        if (vUnits4Day == 0) {
                            return false;
                        }
                    } else {
                        // 2.判断非0打头的日期是否合法
                        int vDay = Integer.parseInt(day);
                        if (vDay < 10 || vDay > 31) {
                            return false;
                        }
                    }
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    //通过java Calendar 实现时间+ 分钟
    public static String addTime(String pattern, String timeStr, String addnumber) {
        String str = null;
        try {
            DateFormat df = new SimpleDateFormat(pattern);
            Date date = df.parse(timeStr);
            //时间累计
            Calendar gc = new GregorianCalendar();
            gc.setTime(date);
            gc.add(GregorianCalendar.MINUTE, Integer.parseInt(addnumber));
            str = df.format(gc.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return str;
    }

    /**
     * @param nowTime   当前时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>   判断当前时间在时间区间内
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param oldTime 开始时间
     * @param newTime 结束时间
     * @return
     * <AUTHOR>   获取两个时间相差分钟数
     */
    public static long getTwoTimesMinute(String oldTime, String newTime) throws ParseException {

        SimpleDateFormat df = new SimpleDateFormat("HH:mm");
        long NTime = df.parse(newTime).getTime();
        //从对象中拿到时间
        long OTime = df.parse(oldTime).getTime();
        long diff = (NTime - OTime) / 1000 / 60;
        return diff;
    }

    /**
     * @param date 日期2020-07-18
     * @return 2020年7月18日
     */
    public static String getYMD(String date) throws ParseException {

        String year = date.substring(0, 4);
        String month = date.substring(5, 7);
        int i = Integer.parseInt(month);
        String monthStr = i + "";
        String day = date.substring(8, 10);
        int j = Integer.parseInt(day);
        String dayStr = j + "";
        String dateStr = year + "年" + monthStr + "月" + dayStr + "日";
        return dateStr;
    }

    /**
     * 获取最近一年的月份,例如现在7月
     * @return [2019-07, 2019-08, 2019-09, 2019-10, 2019-11, 2019-12, 2020-01, 2020-02, 2020-03, 2020-04, 2020-05, 2020-06]
     */
    public static List<String> getThisYearMonths() {
        //建一个容器
        List<String> months = new ArrayList<>();
        //获取日历对象
        Calendar calendar = Calendar.getInstance();
        //调整到12个月以前
        calendar.add(Calendar.MONTH, -12);
        //循环12次获取12个月份
        for (int i = 0; i < 12; i++) {
            //日历对象转为Date对象
            Date date = calendar.getTime();
            //将date转为字符串
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String dateStr = sdf.format(date);
            //向list集合中添加
            months.add(dateStr);
            //每次月份+1
            calendar.add(Calendar.MONTH, 1);
        }
        return months;
    }

    /**
     * 根据传入的参数，来对日期区间进行拆分，返回拆分后的日期List
     * @param statisticsType startDate
     *                       endDate
     * @return
     * @throws ParseException
     * <AUTHOR> @editor
     * @editcont
     */
    public static List<String> doDateByStatisticsType(String statisticsType, String startDate, String endDate) throws ParseException {
        List<String> listWeekOrMonth = new ArrayList<String>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date sDate = dateFormat.parse(startDate);
        Calendar sCalendar = Calendar.getInstance();
        sCalendar.setFirstDayOfWeek(Calendar.MONDAY);
        sCalendar.setTime(sDate);
        Date eDate = dateFormat.parse(endDate);
        Calendar eCalendar = Calendar.getInstance();
        eCalendar.setFirstDayOfWeek(Calendar.MONDAY);
        eCalendar.setTime(eDate);
        boolean bool = true;
        if (statisticsType.equals("week")) {
            while (sCalendar.getTime().getTime() < eCalendar.getTime().getTime()) {
                if (bool || sCalendar.get(Calendar.DAY_OF_WEEK) == 2 || sCalendar.get(Calendar.DAY_OF_WEEK) == 1) {
                    listWeekOrMonth.add(dateFormat.format(sCalendar.getTime()));
                    bool = false;
                }
                sCalendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            listWeekOrMonth.add(dateFormat.format(eCalendar.getTime()));
            if (listWeekOrMonth.size() % 2 != 0) {
                listWeekOrMonth.add(dateFormat.format(eCalendar.getTime()));
            }
        } else if (statisticsType.equals("month")) {
            while (sCalendar.getTime().getTime() < eCalendar.getTime().getTime()) {
                if (bool || sCalendar.get(Calendar.DAY_OF_MONTH) == 1 || sCalendar.get(Calendar.DAY_OF_MONTH) == sCalendar.getActualMaximum(Calendar.DAY_OF_MONTH)) {
                    listWeekOrMonth.add(dateFormat.format(sCalendar.getTime()));
                    bool = false;
                }
                sCalendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            listWeekOrMonth.add(dateFormat.format(eCalendar.getTime()));
            if (listWeekOrMonth.size() % 2 != 0) {
                listWeekOrMonth.add(dateFormat.format(eCalendar.getTime()));
            }
        } else if (statisticsType.equals("year")) {
            while (sCalendar.getTime().getTime() < eCalendar.getTime().getTime()) {
                if (bool || sCalendar.get(Calendar.DAY_OF_YEAR) == 1 || sCalendar.get(Calendar.DAY_OF_YEAR) == sCalendar.getActualMaximum(Calendar.DAY_OF_YEAR)) {
                    listWeekOrMonth.add(dateFormat.format(sCalendar.getTime()));
                    bool = false;
                }
                sCalendar.add(Calendar.DAY_OF_YEAR, 1);
            }
            listWeekOrMonth.add(dateFormat.format(eCalendar.getTime()));
            if (listWeekOrMonth.size() % 2 != 0) {
                listWeekOrMonth.add(dateFormat.format(eCalendar.getTime()));
            }
        }

        return listWeekOrMonth;
    }


    /**
     * 根据时间范围获得季度集
     * @return
     */
    public static Map<String, Date> getRangeSet_Q(String beginDate, String endDate) {
        LinkedHashMap<String, Date> map = new LinkedHashMap<>();
        List<String> rangeSet = null;
        SimpleDateFormat sdf = null;
        Date begin_date = null;
        Date end_date = null;
        String[] numStr = null;
        String Q = null;
        rangeSet = new ArrayList<String>();
        sdf = new SimpleDateFormat("yyyy-MM");
        try {
            begin_date = sdf.parse(beginDate);//定义起始日期
            end_date = sdf.parse(endDate);//定义结束日期
        } catch (ParseException e) {
            System.out.println("时间转化异常，请检查你的时间格式是否为yyyy-MM或yyyy-MM-dd");
        }
        Calendar dd = Calendar.getInstance();//定义日期实例
        dd.setTime(begin_date);//设置日期起始时间
        while (!dd.getTime().after(end_date)) {//判断是否到结束日期
            numStr = sdf.format(dd.getTime()).split("-", 0);
            Q = getQuarter(Integer.valueOf(numStr[1])) + "";
            //System.out.println(numStr[0].toString()+"年"+numStr[1].toString()+"月"+"为"+numStr[0].toString()+"年第"+Q+"季");
            rangeSet.add(Q);

            dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
            map.put(numStr[0].toString() + "年第" + Q + "季", dd.getTime());
        }
        return map;
    }

    /**
     * 根据月获得季度
     * @param month 月
     * @return 季度
     */
    private static int getQuarter(int month) {
        if (month == 1 || month == 2 || month == 3) {
            return 1;
        } else if (month == 4 || month == 5 || month == 6) {
            return 2;
        } else if (month == 7 || month == 8 || month == 9) {
            return 3;
        } else {
            return 4;
        }
    }

    /**
     * 获取两个时间之间的日期，按天计算
     * @param starttime
     * @param endtime
     * @return
     */
    public static List<String> getBetweenTime(String starttime, String endtime) {
        List<String> betweenTime = new ArrayList<String>();
        try {
            Date sdate = new SimpleDateFormat("yyyy-MM-dd").parse(starttime);
            Date edate = new SimpleDateFormat("yyyy-MM-dd").parse(endtime);

            SimpleDateFormat outformat = new SimpleDateFormat("yyyy-MM-dd");

            Calendar sCalendar = Calendar.getInstance();
            sCalendar.setTime(sdate);
            int year = sCalendar.get(Calendar.YEAR);
            int month = sCalendar.get(Calendar.MONTH);
            int day = sCalendar.get(Calendar.DATE);
            sCalendar.set(year, month, day, 0, 0, 0);

            Calendar eCalendar = Calendar.getInstance();
            eCalendar.setTime(edate);
            year = eCalendar.get(Calendar.YEAR);
            month = eCalendar.get(Calendar.MONTH);
            day = eCalendar.get(Calendar.DATE);
            eCalendar.set(year, month, day, 0, 0, 0);

            while (sCalendar.before(eCalendar)) {
                betweenTime.add(outformat.format(sCalendar.getTime()));
                sCalendar.add(Calendar.DAY_OF_YEAR, 1);
            }
            betweenTime.add(outformat.format(eCalendar.getTime()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return betweenTime;
    }

    /***
     * 获取两个时间段的年份/年第一天/年最后一天
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<Map> getYears(String startTime, String endTime) {
        List<Map> res = new ArrayList<Map>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy");
        DateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(Calendar.YEAR, 1);// 日期加1(包含结束)
            while (tempStart.before(tempEnd)) {
                String year = dateFormat.format(tempStart.getTime());
                String first = year + "-01-01 00:00:00";
                String last = year + "-12-31 23:59:59";
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("year", year);
                map.put("first", dateFormat2.parse(first));
                map.put("last", dateFormat2.parse(last));
                res.add(map);
                tempStart.add(Calendar.YEAR, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return res;
    }

    public static Map<String, String> getWeekDate() {
        Map<String, String> map = new HashMap<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");//注意后面有一个空格
        Calendar calStart = Calendar.getInstance();
        Calendar calEnd = Calendar.getInstance();
        calStart.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calEnd.setFirstDayOfWeek(Calendar.MONDAY);
        calEnd.set(Calendar.DAY_OF_WEEK, calEnd.getFirstDayOfWeek() + 6);
        //Timestamp weekStart = Timestamp.valueOf(format.format(calStart.getTime()) + " 00:00:00");//字符串类型转化为timestamp
        //Timestamp weekEnd = Timestamp.valueOf(format.format(calEnd.getTime()) + " 23:59:59");
        String weekStart = format.format(calStart.getTime());
        String weekEnd = format.format(calEnd.getTime());
        map.put("weekStart", weekStart);
        map.put("weekEnd", weekEnd);
        return map;
    }

    public static Map<String, String> getLastWeekDate() {
        Map<String, String> map = new HashMap<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");//注意后面有一个空格
        Calendar calStart = Calendar.getInstance();
        Calendar calEnd = Calendar.getInstance();
        calStart.add(Calendar.DAY_OF_MONTH, -7);//上一周
        calEnd.add(Calendar.DAY_OF_MONTH, -7);
        calStart.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calEnd.setFirstDayOfWeek(Calendar.MONDAY);
        calEnd.set(Calendar.DAY_OF_WEEK, calEnd.getFirstDayOfWeek() + 6);
        //Timestamp weekStart = Timestamp.valueOf(format.format(calStart.getTime()) + " 00:00:00");//字符串类型转化为timestamp
        //Timestamp weekEnd = Timestamp.valueOf(format.format(calEnd.getTime()) + " 23:59:59");
        String weekStart = format.format(calStart.getTime());
        String weekEnd = format.format(calEnd.getTime());
        map.put("lastWeekStart", weekStart);
        map.put("lastWeekEnd", weekEnd);
        return map;
    }

    public static Map<String, String> getMouthDate(String date) {
        Map<String, String> map = new HashMap<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        try {
            // 解析输入的日期字符串
            Calendar cal = Calendar.getInstance();
            cal.setTime(format.parse(date));

            // 设置为月份的第一天
            cal.set(Calendar.DAY_OF_MONTH, 1);
            String monthStart = format.format(cal.getTime());

            // 设置为月份的最后一天
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            String monthEnd = format.format(cal.getTime());

            map.put("monthStart", monthStart);
            map.put("monthEnd", monthEnd);
        } catch (Exception e) {
            // 处理异常，这里可以抛出或者返回一个错误信息
            e.printStackTrace();
        }

        return map;
    }

    public static Map<String, String> getLastMouthDate() {
        Map<String, String> map = new HashMap<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");//注意后面有一个空格
        Calendar calStart = Calendar.getInstance();
        Calendar calEnd = Calendar.getInstance();
        calStart.add(Calendar.MONTH, -1);//上一月
        calEnd.add(Calendar.MONTH, -1);
        calStart.set(Calendar.DAY_OF_MONTH, 1);
        calEnd.set(Calendar.DAY_OF_MONTH, calEnd.getActualMaximum(Calendar.DAY_OF_MONTH));
        //Timestamp monthStart = Timestamp.valueOf(format.format(calStart.getTime()) + " 00:00:00");//字符串类型转化为timestamp
        //Timestamp monthEnd = Timestamp.valueOf(format.format(calEnd.getTime()) + " 23:59:59");


        String monthStart = format.format(calStart.getTime());
        String monthEnd = format.format(calEnd.getTime());
        map.put("lastMonthStart", monthStart);
        map.put("lastMonthEnd", monthEnd);
        return map;
    }

    public static String getYesterday(String pattern) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        Date d = cal.getTime();
        SimpleDateFormat sp = new SimpleDateFormat(pattern);
        String ZUOTIAN = sp.format(d);//获取昨天日期
        return ZUOTIAN;
    }

    /**
     * 获取时间段内的自然周
     * @param dateStart
     * @param dateEnd
     * @return
     */
    public static List<Set<String>> getNatureWeeks(String dateStart, String dateEnd) {
        List<Set<String>> natureWeekList = new ArrayList<>();
        try {
            DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            int startDateNum = Integer.parseInt(dateStart);
            int endDateNum = Integer.parseInt(dateEnd);
            if (startDateNum > endDateNum) {
                return natureWeekList;
            } else if (startDateNum == endDateNum) {
                Set natureWeek = new LinkedHashSet();
                natureWeek.add(dateStart);
                natureWeekList.add(natureWeek);
                return natureWeekList;
            }
            Date startDate = dateFormat.parse(dateStart);
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(startDate);

            Date endDate = dateFormat.parse(dateEnd);
            Calendar calendar2 = new GregorianCalendar();
            calendar2.setTime(endDate);
            calendar2.add(Calendar.DATE, 7);
            String endDay = dateFormat.format(calendar2.getTime());
            int endDateNumAdd = Integer.parseInt(endDay);

            for (; ; ) {
                int dayOfWeeek = new Integer(calendar.get(Calendar.DAY_OF_WEEK));
                //周一(周日-周一...周五-周六依次用1-7表示)
                if (dayOfWeeek == 2) {
                    calendar.add(Calendar.DATE, 6);
                    int dateNum = Integer.parseInt(dateFormat.format(calendar.getTime()));
                    if (dateNum <= endDateNum) {
                        Set natureWeek = new LinkedHashSet();
                        calendar.add(Calendar.DATE, -6);
                        for (int i = 0; i < 7; i++) {
                            String day = dateFormat.format(calendar.getTime());
                            natureWeek.add(day);
                            calendar.add(Calendar.DATE, 1);
                        }
                        natureWeekList.add(natureWeek);

                    } else if (dateNum != endDateNumAdd) {
                        Set natureWeek = new LinkedHashSet();
                        calendar.add(Calendar.DATE, -6);
                        for (int i = 0; i < 7; i++) {
                            String day = dateFormat.format(calendar.getTime());
                            natureWeek.add(day);
                            calendar.add(Calendar.DATE, 1);
                            if (day.equals(String.valueOf(endDateNum))) {
                                break;
                            }
                        }
                        natureWeekList.add(natureWeek);
                        break;
                    } else {
                        break;
                    }
                } else {
                    int size = natureWeekList.size();
                    if (size == 0) {
                        Set natureWeek = new LinkedHashSet();
                        String day = dateFormat.format(calendar.getTime());
                        natureWeek.add(day);
                        natureWeekList.add(natureWeek);
                    } else {
                        Set<String> strings = natureWeekList.get(0);
                        String day = dateFormat.format(calendar.getTime());
                        strings.add(day);
                    }
                    calendar.add(Calendar.DATE, 1);
                }
            }
        } catch (ParseException e) {
            System.out.print("获取时间段内的自然周异常," + e.toString());
        }
        return natureWeekList;
    }

    /**
     * 默认日期格式
     */
    public static String DEFAULT_FORMAT = "yyyy-MM-dd";


    /**
     * 获取当年的第一天
     * @return
     */
    public static Date getCurrYearFirst() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearFirst(currentYear);
    }

    /**
     * 获取当年的最后一天
     * @return
     */
    public static Date getCurrYearLast() {
        Calendar currCal = Calendar.getInstance();
        int currentYear = currCal.get(Calendar.YEAR);
        return getYearLast(currentYear);
    }

    /**
     * 获取某年第一天日期
     * @param year 年份
     * @return Date
     */
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     * 获取某年最后一天日期
     * @param year 年份
     * @return Date
     */
    public static Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        Date currYearLast = calendar.getTime();
        return currYearLast;
    }

    /**
     * 获取某月的最后一天 如201712结果为2017-12-30
     * @param year
     * @param month
     * @return
     */
    public static String getLastDay(String year, String month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, Integer.parseInt(year));
        cal.set(Calendar.MONTH, Integer.parseInt(month) - 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DATE));
        return new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
    }


    /**
     * 获取当日开始时间
     * @return
     */
    public static Date getDayStartTime() {
        // 获取当前日期
        Date now = new Date();
        // 设置开始时间为当日的 00:00:00
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date startTime = calendar.getTime();
        return startTime;
    }

    /**
     * 获取当日结束时间
     * @return
     */
    public static Date getDayEndTime() {
        // 获取当前日期
        Date now = new Date();
        // 设置结束时间为当日的 23:59:59
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date endTime = calendar.getTime();
        return endTime;
    }


    /**
     * 获取当月开始时间
     * @return
     */
    public static Date getMonthStartTime() {
        // 获取当前日期
        Date now = new Date();
        // 设置开始时间为当月的 1 日的 00:00:00
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date startTime = calendar.getTime();
        return startTime;
    }

    /**
     * 获取当月结束时间
     * @return
     */
    public static Date getMonthEndTime() {
        // 获取当前日期
        Date now = new Date();
        // 设置开始时间为当月的 1 日的 00:00:00
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        // 设置结束时间为当月的最后一天的 23:59:59
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date endTime = calendar.getTime();
        return endTime;
    }


    public static Long dayDiff(Date startDate, Date endDate) {

        // 创建Calendar对象
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();

        // 设置Calendar对象的时间为两个日期对象的时间
        cal1.setTime(startDate);
        cal2.setTime(endDate);

        // 获取两个日期相差的天数
        long diff = cal2.getTimeInMillis() - cal1.getTimeInMillis();
        long diffDays = diff / (24 * 60 * 60 * 1000);
        return diffDays;
    }


    /**
     * 两个date日期相差分钟数
     * @param startDate
     * @param endDate
     * @return 分钟数
     */
    public static Long getMinuteCount(Date startDate, Date endDate) {

        // 创建Calendar对象并设置日期
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(startDate);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(endDate);

        // 计算两个日期之间的分钟数差异
        long minutesDiff = (cal2.getTimeInMillis() - cal1.getTimeInMillis()) / (1000 * 60);
        return minutesDiff;
    }


    /**
     * 获得入参时间num分钟后的时间
     * @param date 入参时间
     * @return 该时间num分钟后的时间
     */
    public static Date addMinutes(Date date, Integer num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, num);
        return calendar.getTime();
    }


    /**
     * 获取去年年份
     * @return
     */
    public static int lastYear() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        int lastYear = calendar.get(Calendar.YEAR);
        System.out.println(lastYear);
        return lastYear;
    }

    /**
     * 获取周几
     * @return
     */
    public static String getDayOfWeek(String dateStr){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = sdf.parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            String[] days = {"", "周日", "周一", "周二", "周三", "周四", "周五", "周六"};
            return days[dayOfWeek];
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }



    /**
     * 字符串 ISO 8601 格式的日期时间 转 yyyy-MM-dd HH:mm:ss 字符串
     * @return
     */
    public static String changeDateStr(String input){
        // 定义输入格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

        // 解析输入字符串为 OffsetDateTime 对象
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(input, inputFormatter);

        // 定义输出格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(DateUtils.pattern_date);

        // 格式化为所需的字符串格式
        String output = offsetDateTime.format(outputFormatter);
        return output;
    }


    /**
     * 字符串 ISO 8601 格式的日期时间 转 yyyy-MM-dd字符串
     * @return
     */
    public static Date change8601Date(String input){
        // 使用DateTimeFormatter解析ISO 8601格式的字符串
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(input, DateTimeFormatter.ISO_ZONED_DATE_TIME);

        // 将ZonedDateTime转换为java.util.Date
        Date date = Date.from(zonedDateTime.toInstant());
        return date;
    }



    /**
     * 获取上个月最后一天的结束时间，上个月最后一天的23:59:59
     * @return
     */
    public static Date getLastMonthEndTime() {
        // 创建一个Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 设置时间为当前时间
        calendar.setTime(new Date());

        // 将日期设置为上个月最后一天
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为当月的第一天
        calendar.add(Calendar.DATE, -1); // 减去一天，得到上个月的最后一天

        // 设置时间为23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        // 获取上个月最后一天的结束时间
        return calendar.getTime();
    }



    /**
     * 获取前两个月的年月
     * @return
     */
    public static Map<String,Integer> getYearAndMonth() {
        // 获取当前日期的Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 将当前日期月份减去2
        calendar.add(Calendar.MONTH, -2);

        // 获取年份和月份
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // Calendar中的月份是从0开始的

        HashMap<String, Integer> map = new HashMap<>();
        map.put("year",year);
        map.put("month",month);
        return map;
    }


    /**
     * 获取近12个月的月份
     * @return
     */
    public static List<String> getLastYearMonths() {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 创建一个列表来存储近12个月的日期
        List<String> last12Months = new ArrayList<>();

        // 循环获取近12个月的日期
        for (int i = 0; i < 12; i++) {
            // 获取当前日期减去i个月的日期
            LocalDate date = today.minusMonths(i);
            // 格式化日期为"年-月"的格式，例如"2023-01"
            String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            // 将格式化后的日期添加到列表中
            last12Months.add(formattedDate);
        }

        ArrayList<String> list = new ArrayList<>();
        // 打印列表中的日期
        for (String date : last12Months) {
            list.add(date);
        }
        return list;
    }


    /**
     * 获取近12个月的月份
     * @return
     */
    public static List<String> getNearMonths(int num) {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 创建一个列表来存储近num个月的日期
        List<String> last12Months = new ArrayList<>();

        // 循环获取近12个月的日期
        for (int i = 0; i < num; i++) {
            // 获取当前日期减去i个月的日期
            LocalDate date = today.minusMonths(i);
            // 格式化日期为"年-月"的格式，例如"2023-01"
            String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            // 将格式化后的日期添加到列表中
            last12Months.add(formattedDate);
        }

        ArrayList<String> list = new ArrayList<>();
        // 打印列表中的日期
        for (String date : last12Months) {
            list.add(date);
        }
        return list;
    }

    /**
     * 描述：d当天
     * @param date 日期
     * @return
     */
    public static boolean isToday(String date) {
        // 使用ISO_LOCAL_DATE格式化器，它对应于"yyyy-MM-dd"格式
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;
        // 解析字符串为LocalDate对象
        LocalDate inputDate = LocalDate.parse(date, formatter);
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 比较两个日期是否相等
        return inputDate.equals(today);
    }


    /**
     * 描述：上册，下册
     * @param date 日期
     * @return
     */
    public static String timePeriodChecker(String date) {

        LocalDate currentDate = LocalDate.parse(date);
        // 定义上册和下册的日期范围
        LocalDate firstHalfStart = LocalDate.of(currentDate.getYear(), Month.FEBRUARY, 1);
        LocalDate firstHalfEnd = LocalDate.of(currentDate.getYear(), Month.AUGUST, 14);
        LocalDate secondHalfStart = LocalDate.of(currentDate.getYear(), Month.AUGUST, 15);
        LocalDate secondHalfEnd = LocalDate.of(currentDate.getYear(), Month.DECEMBER, 31);

        // 判断当前日期属于哪个区间
        if (!currentDate.isBefore(firstHalfStart) && !currentDate.isAfter(firstHalfEnd)) {
            return "上册";
        } else if (!currentDate.isBefore(secondHalfStart) && !currentDate.isAfter(secondHalfEnd)) {
            return "下册";
        } else {
            // 如果当前日期在1月1日至1月31日之间，属于下册
            return "下册";
        }
    }


    public static List<String> getTimeRange(String volume) {
        int currentYear = LocalDate.now().getYear();
        List<String> list = new ArrayList<>();
        if ("上册".equals(volume)) {
            String start = LocalDate.of(currentYear, 2, 1).toString() + " 00:00:00";
            String end = LocalDate.of(currentYear, 8, 14).toString() + " 00:00:00";
            list.add(start);
            list.add(end);
            return list;
        } else if ("下册".equals(volume)) {
            String start = LocalDate.of(currentYear, 8, 15).toString() + " 00:00:00";
            String end = LocalDate.of(currentYear + 1, 1, 31).toString() + " 00:00:00";
            list.add(start);
            list.add(end);
            return list;
        }else {
            return list;
        }
    }


    public static String convertMinutes(Long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = (seconds + 59) / 60; // 向上取整，确保不足一分钟也算一分钟
        long hours = minutes / 60;

        minutes = minutes % 60; // 计算剩余分钟
        hours = hours % 24;     // 计算剩余小时（如果需要24小时制）

        if (hours == 0){
            return  minutes + "分钟";
        }else {
            return hours + "小时" + minutes + "分钟";
        }
    }

    public static String convertSeconds(Long milliseconds) {
        long seconds = milliseconds / 1000; // 转换为秒
        long hours = seconds / 3600;         // 计算小时
        seconds %= 3600;                     // 剩余秒数
        long minutes = seconds / 60;         // 计算分钟
        seconds %= 60;

        if (hours == 0){
            if (minutes == 0){
                return seconds + "秒";
            }else {
                return  minutes + "分钟" + seconds + "秒";
            }
        }else {
            return hours + "小时" + minutes + "分钟" + seconds + "秒";
        }
    }


    /**
     * 描述：根据年限和上下册算出时间
     * @param year 年份
     * @param volume 学期
     * @return
     */
    public static List<String> calculateSemester(String year, String volume) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        int currentYear = Integer.parseInt(year);

        // 计算当前时间段
        LocalDate startDate, endDate;
        if ("1".equals(volume)) {
            startDate = LocalDate.of(currentYear, 8, 15);
            endDate = LocalDate.of(currentYear + 1, 1, 31);
        } else {
            startDate = LocalDate.of(currentYear + 1, 2, 1);
            endDate = LocalDate.of(currentYear + 1, 8, 14);
        }

        // 计算上一个时间段
        LocalDate previousStartDate, previousEndDate;
        if ("1".equals(volume)) {
            previousStartDate = LocalDate.of(currentYear - 1, 8, 15);
            previousEndDate = LocalDate.of(currentYear, 1, 31);
        } else {
            previousStartDate = LocalDate.of(currentYear, 2, 1);
            previousEndDate = LocalDate.of(currentYear, 8, 14);
        }
        ArrayList<String> timeList = new ArrayList<>();
        timeList.add(startDate.format(formatter));
        timeList.add(endDate.format(formatter));
        timeList.add(previousStartDate.format(formatter));
        timeList.add(previousEndDate.format(formatter));
        return timeList;
    }


    /**
     * 描述：比较选出早的时间
     * @param date1
     * @param date2
     * @return
     */
    public static String compareDates(String date1, String date2) {
        LocalDate localDate1 = LocalDate.parse(date1);
        LocalDate localDate2 = LocalDate.parse(date2);
        int result = localDate1.compareTo(localDate2);
        if (result < 0) {
            return date1;
        } else if (result > 0) {
            return date2;
        } else {
            return date2;
        }
    }



    /**
     * 描述：当前时间到第二天剩余多少秒
     * @return
     */
    public static Long timeRemaining() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 获取明天的开始时间（00:00）
        LocalDateTime tomorrow = now.plusDays(1).toLocalDate().atStartOfDay();

        // 计算当前时间到明天的剩余时间
        Duration duration = Duration.between(now, tomorrow);

        // 获取剩余秒数
        long remainingSeconds = duration.getSeconds();
        return remainingSeconds;
    }



}
package com.yf.system.modules.group.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import com.yf.base.api.api.dto.BaseIdsReqDTO;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.system.modules.group.dto.SysGroupDTO;
import com.yf.system.modules.group.dto.response.GroupListRespDTO;
import com.yf.system.modules.group.service.SysGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户组控制器
 * </p>
 * <AUTHOR>
 * @since 2023-08-14 10:20
 */
@Api(tags = {"用户组"})
@RestController
@RequestMapping("/api/sys/group")
public class SysGroupController extends BaseController {

    @Autowired
    private SysGroupService baseService;

    /**
     * 添加或修改
     * @param reqDTO
     * @return
     */
    @RequiresPermissions(value = {"sys:group:add", "sys:group:update"}, logical = Logical.OR)
    @ApiOperation(value = "添加或修改")
    @PostMapping("/save")
    public ApiRest save(@RequestBody SysGroupDTO reqDTO) {
        baseService.save(reqDTO);
        return super.success();
    }

    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @RequiresPermissions(value = {"sys:group:delete"})
    @ApiOperation(value = "批量删除")
    @PostMapping("/delete")
    public ApiRest delete(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        baseService.delete(reqDTO.getIds());
        return super.success();
    }


    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @RequiresPermissions(value = {"sys:group:list"})
    @ApiOperation(value = "分页查找")
    @PostMapping("/paging")
    public ApiRest<IPage<GroupListRespDTO>> paging(@RequestBody PagingReqDTO<SysGroupDTO> reqDTO) {

        //分页查询并转换
        IPage<GroupListRespDTO> page = baseService.paging(reqDTO);

        return super.success(page);
    }
}

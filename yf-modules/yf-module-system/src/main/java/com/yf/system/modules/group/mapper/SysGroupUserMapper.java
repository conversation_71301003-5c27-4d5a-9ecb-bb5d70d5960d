package com.yf.system.modules.group.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.system.modules.group.dto.request.GroupUserListReqDTO;
import com.yf.system.modules.group.dto.response.GroupUserListRespDTO;
import com.yf.system.modules.group.entity.SysGroupUser;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 组内用户Mapper
 * </p>
 * <AUTHOR>
 * @since 2023-08-14 10:20
 */
public interface SysGroupUserMapper extends BaseMapper<SysGroupUser> {

    /**
     * 分页查找
     * @param page
     * @param query
     * @return
     */
    IPage<GroupUserListRespDTO> paging(Page page, @Param("query") GroupUserListReqDTO query);
}

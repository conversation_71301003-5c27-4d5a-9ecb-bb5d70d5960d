package com.yf.system.modules.user.task;


import com.yf.job.service.JobService;
import com.yf.system.modules.user.service.SysUserService;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static java.lang.Thread.sleep;

/**
 * 批量修改用户固化的学校消息
 * <AUTHOR>
 */
@Log4j2
@Component
public class BatchUpdateSchoolInfoJob implements Job {

    @Autowired
    private SysUserService sysUserService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {


        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        String group = detail.getKey().getGroup();
        String data = String.valueOf(detail.getJobDataMap().get(JobService.TASK_DATA));

        log.info("++++++++++定时任务：批量修改用户固化的学校消息");
        log.info("++++++++++jobName:{}", name);
        log.info("++++++++++jobGroup:{}", group);
        log.info("++++++++++taskData:{}", data);


        // 填充用户信息
        try {
            sleep(5000);
            sysUserService.batchUpdateSchoolInfo(data);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


}

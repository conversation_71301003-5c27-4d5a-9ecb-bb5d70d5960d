package com.yf.system.modules.website.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.system.modules.website.entity.WebsiteContent;
import com.yf.system.modules.website.entity.WebsiteNavigation;


/**
 * <p>
 * 网站内容业务类
 * </p>
 */
public interface WebsiteService {

    /**
     * 分页查询数据,导航列表，包含内容
     * @param reqDTO
     * @return
     */
    IPage<WebsiteNavigation> websiteNavigationPage(PagingReqDTO<WebsiteNavigation> reqDTO);


    /**
     * 保存导航
     */
    void saveWebsiteNavigation(WebsiteNavigation websiteNavigation);

    /**
     * 删除导航
     */
    void deleteWebsiteNavigation(String websiteNavigationId);

    /**
     * 保存网站内容
     */
    void saveWebsiteContent(WebsiteContent websiteContent);

    /**
     * 删除网站内容
     */
    void deleteWebsiteContent(String websiteContentId);

    /**
     * 查看导航下的内容
     */
    IPage<WebsiteContent> websiteContentPage(PagingReqDTO<WebsiteContent> reqDTO);

    /**
     * 查看内容详情
     */
    WebsiteContent websiteContentDetail(String websiteContentId);
}

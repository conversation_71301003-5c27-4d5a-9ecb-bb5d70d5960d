package com.yf.system.modules.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.system.modules.points.entity.UserTaskCompletion;
import com.yf.system.modules.points.mapper.UserTaskCompletionMapper;
import com.yf.system.modules.points.service.UserTaskCompletionService;
import org.springframework.stereotype.Service;

/**
 * 用户任务完成记录服务实现类
 * <AUTHOR>
 */
@Service
public class UserTaskCompletionServiceImpl extends ServiceImpl<UserTaskCompletionMapper, UserTaskCompletion> implements UserTaskCompletionService {
} 
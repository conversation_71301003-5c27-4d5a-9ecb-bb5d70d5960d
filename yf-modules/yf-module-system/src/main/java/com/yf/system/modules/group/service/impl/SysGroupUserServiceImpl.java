package com.yf.system.modules.group.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.base.api.exception.ServiceException;
import com.yf.system.modules.enter.job.EnterTriggerJob;
import com.yf.system.modules.group.dto.request.GroupBatchReqDTO;
import com.yf.system.modules.group.dto.request.GroupUserListReqDTO;
import com.yf.system.modules.group.dto.response.GroupUserListRespDTO;
import com.yf.system.modules.group.entity.SysGroupUser;
import com.yf.system.modules.group.mapper.SysGroupUserMapper;
import com.yf.system.modules.group.service.SysGroupUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 组内用户业务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-08-14 10:20
 */
@Service
public class SysGroupUserServiceImpl extends ServiceImpl<SysGroupUserMapper, SysGroupUser> implements SysGroupUserService {


    @Autowired
    private EnterTriggerJob enterTriggerJob;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchJoin(GroupBatchReqDTO reqDTO) {

        List<String> userIds = reqDTO.getUserIds();
        List<String> groupIds = reqDTO.getGroupIds();
        if (CollectionUtils.isEmpty(userIds)) {
            throw new SecurityException("参数错误，至少要传入一个用户！");
        }
        if (CollectionUtils.isEmpty(groupIds)) {
            throw new SecurityException("参数错误，至少要传入一个群组！");
        }

        // 批量加入
        for (String groupId : groupIds) {
            this.joinOneGroup(groupId, userIds, reqDTO.getFlag());
        }

        // 刷新各个入口权限
        enterTriggerJob.create(null, groupIds);
    }

    @Override
    public void delete(List<String> ids) {
        // 先进行移除
        QueryWrapper<SysGroupUser> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(SysGroupUser::getId, ids);
        this.remove(wrapper);

        // 刷新各个入口权限
        enterTriggerJob.create(null, ids);
    }

    @Override
    public IPage<GroupUserListRespDTO> paging(PagingReqDTO<GroupUserListReqDTO> reqDTO) {

        // 请求参数
        GroupUserListReqDTO params = reqDTO.getParams();
        if (params == null) {
            throw new ServiceException("必须传入用户组ID");
        }

        return baseMapper.paging(reqDTO.toPage(), params);
    }

    @Override
    public void removeUser(List<String> userIds) {
        // 移除
        QueryWrapper<SysGroupUser> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(SysGroupUser::getUserId, userIds);
        this.remove(wrapper);
    }


    /**
     * 把一些用户加入到某个组
     * @param groupId
     * @param userIds
     */
    private void joinOneGroup(String groupId, List<String> userIds, Integer flag) {

        this.removeFromGroup(groupId, userIds);

        // 移除不后续
        if (flag == null || flag.equals(0)) {
            return;
        }

        // 再加入
        List<SysGroupUser> list = new ArrayList<>();
        for (String userId : userIds) {
            SysGroupUser gu = new SysGroupUser();
            gu.setGroupId(groupId);
            gu.setUserId(userId);
            list.add(gu);
        }

        this.saveBatch(list);


        // 刷新各个入口权限
        enterTriggerJob.create(null, Collections.singletonList(groupId));
    }

    /**
     * 把一些用户加入到某个组
     * @param groupId
     * @param userIds
     */
    private void removeFromGroup(String groupId, List<String> userIds) {

        QueryWrapper<SysGroupUser> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(SysGroupUser::getGroupId, groupId)
                .in(SysGroupUser::getUserId, userIds);
        this.remove(wrapper);

        // 刷新各个入口权限
        enterTriggerJob.create(null, Collections.singletonList(groupId));
    }
}

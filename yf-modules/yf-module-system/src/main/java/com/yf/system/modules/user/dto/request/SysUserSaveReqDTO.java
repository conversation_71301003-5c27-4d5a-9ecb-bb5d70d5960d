package com.yf.system.modules.user.dto.request;

import com.yf.system.modules.user.dto.SysUserDTO;
import com.yf.system.modules.user.entity.SysUserHonor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 管理员登录请求类
 * </p>
 * <AUTHOR>
 * @since 2020-04-13 16:57
 */
@Data
@ApiModel(value = "管理员保存请求类", description = "管理员保存请求类")
public class SysUserSaveReqDTO extends SysUserDTO {

    @ApiModelProperty(value = "角色列表", required = true)
    private List<String> roles;

    @ApiModelProperty(value = "年级", required = true)
    private String gradeName;
    @ApiModelProperty(value = "班级", required = true)
    private String clazzName;
    @ApiModelProperty(value = "组", required = true)
    private String groupName;
    @ApiModelProperty(value = "激活码 0-未激活，1-已激活", required = true)
    private Integer isActivate;
    @ApiModelProperty(value = "在班级 0-未进班，1-已进班", required = true)
    private Integer isInTheClass;
    @ApiModelProperty(value = "教师端导入功能权限 1-有订单导入，有新增用户 ,2-有激活码导入，无新增用户")
    private Integer importAccess;
    @ApiModelProperty(value = "AI助手使用权限 0-无，1-有")
    private Integer assistantAccess;
    @ApiModelProperty(value = "在学校 0-不在，1-在")
    private Integer inSchool;
    @ApiModelProperty(value = "弹窗 0-不弹窗，1-弹窗")
    private Integer needWindow;
    /**
     * 信息科技-荣耀
     */
    @ApiModelProperty(value = "信息科技-个人经历")
    private List<SysUserHonor> userHonors;

    @ApiModelProperty(value = "信息科技-订单id,筛选用")
    private String orderId;

    @ApiModelProperty(value = "有权限的年级册别")
    private List<String> gradeValueIds;

    @ApiModelProperty(value = "部门完整名称", required = true)
    private String wholeName;

    @ApiModelProperty(value = "考试Id", required = true)
    private String examId;

}

package com.yf.system.modules.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yf.system.modules.log.dto.request.SysLogReqDTO;
import com.yf.system.modules.log.dto.response.SysLogRespDTO;
import com.yf.system.modules.log.entity.SysLog;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 系统日志Mapper
 * </p>
 * <AUTHOR>
 * @since 2020-04-28 10:23
 */
public interface SysLogMapper extends BaseMapper<SysLog> {

    /**
     * 分页查询日志
     * @param page
     * @param query
     * @return
     */
    IPage<SysLogRespDTO> paging(Page page, @Param("query") SysLogReqDTO query);
}

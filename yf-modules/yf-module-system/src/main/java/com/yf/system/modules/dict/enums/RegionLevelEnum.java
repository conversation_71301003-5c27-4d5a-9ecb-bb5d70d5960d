package com.yf.system.modules.dict.enums;

import com.yf.system.modules.depart.enums.DepartType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 行政区域层级
 * <AUTHOR>
 * @since 2024-09-13
 */
@Getter
@AllArgsConstructor
public enum RegionLevelEnum {

    /**
     * 省
     */
    PROVINCE(1, DepartType.PROVINCE),

    /**
     * 市
     */
    CITY(2, DepartType.CITY),

    /**
     * 区县
     */
    AREA(3, DepartType.COUNTY);

    final Integer val;
    final Integer departType;

    private static final Map<Integer, RegionLevelEnum> REGION_LEVEL_MAP = new HashMap<>(RegionLevelEnum.values().length);

    static {
        for (RegionLevelEnum entry : RegionLevelEnum.values()) {
            REGION_LEVEL_MAP.put(entry.getVal(), entry);
        }
    }

    public static RegionLevelEnum get(Integer val) {
        return REGION_LEVEL_MAP.get(val);
    }
}

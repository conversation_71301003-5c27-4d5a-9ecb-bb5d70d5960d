package com.yf.system.modules.website.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 网站内容实体类
 * </p>
 */
@Data
@TableName("website_content")
@Table(name = "website_content")//actable自动建表插件注解
public class WebsiteContent extends Model<WebsiteContent> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 导航名称Id
     */
    @TableField
    private String websiteNavigationId;

    /**
     * 栏目分类
     */
    @TableField("cat_id")
    private String catId;

    @TableField(fill = FieldFill.INSERT)
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date createDate;//创建日期
    @TableField(fill = FieldFill.UPDATE)
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date modifyDate;//修改日期

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;


    /**
     * 标题
     */
    @TableField
    private String title;

    /**
     * 摘要
     */
    @TableField
    private String digest;

    /**
     * 标签
     */
    @TableField
    private String label;

    /**
     * 图片
     */
    @TableField
    private String picture;

    /**
     * 内容
     */
    @TableField
    private String content;

    /**
     * 链接
     */
    @TableField
    private String url;

    /**
     * 备注
     */
    @TableField
    private String remark;

    /**
     * 排序，越大越靠前
     */
    @TableField
    @DefaultValue("0")
    private Integer sort;


    /**
     * 是否显示 1-显示，0-不显示
     */
    @TableField
    @DefaultValue("1")
    private Integer isShow;

    /**
     * 状态
     */
    @TableField
    private String status;


    /**
     * 数据标识
     */
    @TableField("data_flag")
    @DefaultValue("0")
    private Integer dataFlag;

    @TableLogic
    @TableField
    @DefaultValue("0")
    private Boolean isDeleted;//是否删除

}

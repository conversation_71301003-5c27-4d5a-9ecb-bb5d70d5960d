package com.yf.system.modules.enter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

/**
 * <p>
 * 指定人员实体类
 * </p>
 * <AUTHOR>
 * @since 2023-09-15 14:35
 */
@Data
@TableName("el_enter_person")
public class EnterPerson extends Model<EnterPerson> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 1考试2课程3报名
     */
    @TableField("ref_type")
    private Integer refType;

    /**
     * 关联ID
     */
    @TableField("ref_id")
    private String refId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

}

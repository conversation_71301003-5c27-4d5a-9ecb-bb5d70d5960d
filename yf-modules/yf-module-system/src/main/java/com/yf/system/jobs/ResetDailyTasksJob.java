package com.yf.system.jobs;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yf.system.modules.points.service.PointsTaskService;
import com.yf.system.modules.points.service.UserPointsService;
import com.yf.system.modules.task.entity.SchedulerTask;
import com.yf.system.modules.task.mapper.SchedulerTaskMapper;
import com.yf.system.utils.DateUtils;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.util.Date;

/**
 * 积分过期定时任务
 * <AUTHOR>
 */
@Log4j2
@Component
public class ResetDailyTasksJob implements Job {


    @Autowired
    private PointsTaskService pointsTaskService;


    @Resource
    private SchedulerTaskMapper schedulerTaskMapper;

    @SneakyThrows
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        //让程序睡5-10秒，避免多个定时任务出现幻读，多次执行
        Thread.sleep((int) (Math.random() * (9999 - 5000 + 1) + 5000));
        //获取当前年月日时间戳
        Long time = DateUtils.parseString(DateUtils.formatDate(new Date())).getTime();
        JobDetail detail = jobExecutionContext.getJobDetail();
        String name = detail.getKey().getName();
        LambdaQueryWrapper<SchedulerTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SchedulerTask::getName, name);
        SchedulerTask schedulerTask = schedulerTaskMapper.selectOne(queryWrapper);
        Long timestamp = schedulerTask.getTimestamp();
        if (!time.equals(timestamp)) {
            schedulerTask.setTimestamp(time);
            schedulerTask.setIp(InetAddress.getLocalHost().getHostAddress());
            schedulerTaskMapper.insertOrUpdate(schedulerTask);
            try {
                String group = detail.getKey().getGroup();

                log.info("++++++++++禁用用户：积分过期定时任务");
                log.info("++++++++++jobName:{}", name);
                log.info("++++++++++jobGroup:{}", group);

                pointsTaskService.resetDailyTasks();
            } catch (Exception e) {
                e.printStackTrace();
                schedulerTask.setTimestamp(timestamp);
                schedulerTaskMapper.insertOrUpdate(schedulerTask);
            }
        }else {
            log.info("积分过期定时任务已经执行过！");
        }
    }

}

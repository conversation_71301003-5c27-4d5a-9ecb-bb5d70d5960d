package com.yf.system.modules.user.dto;

import com.yf.base.api.annon.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 学校信息
 * </p>
 * <AUTHOR>
 */
@Data
@ApiModel(value = "学校信息", description = "学校信息")
public class SchoolInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String county;

    @ApiModelProperty(value = "学校")
    private String school;

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "班级")
    private String clazzName;

    @ApiModelProperty(value = "班级部门id")
    private String clazzId;

    @ApiModelProperty(value = "入学年份")
    private Integer enrollmentYear;
}

package com.yf.system.modules.website.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.system.modules.website.entity.WebsiteContent;
import com.yf.system.modules.website.entity.WebsiteNavigation;
import com.yf.system.modules.website.service.WebsiteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 网站编辑控制器
 * </p>
 */
@Api(tags = {"网站编辑控制器"})
@RestController
@RequestMapping("/api/sys/website")
public class WebsiteController extends BaseController {

    @Autowired
    private WebsiteService websiteService;


    /**
     * 分页查询数据,导航列表，包含内容
     * @param reqDTO
     * @return
     */
    @PostMapping("/websiteNavigationPage")
    public ApiRest websiteNavigationPage(@RequestBody PagingReqDTO<WebsiteNavigation> reqDTO) {
        IPage<WebsiteNavigation> websiteNavigationIPage = websiteService.websiteNavigationPage(reqDTO);
        return super.success(websiteNavigationIPage);
    }

    /**
     * 保存导航
     */
    @PostMapping("/saveWebsiteNavigation")
    public ApiRest saveWebsiteNavigation(@RequestBody WebsiteNavigation websiteNavigation) {
        websiteService.saveWebsiteNavigation(websiteNavigation);
        return super.success();
    }


    /**
     * 删除导航
     */
    @PostMapping("/deleteWebsiteNavigation")
    public ApiRest deleteWebsiteNavigation(@RequestParam String websiteNavigationId) {
        websiteService.deleteWebsiteNavigation(websiteNavigationId);
        return super.success();
    }

    /**
     * 保存网站内容
     */
    @ApiOperation(value = "保存网站内容")
    @PostMapping("/saveWebsiteContent")
    public ApiRest saveWebsiteContent(@RequestBody WebsiteContent websiteContent) {
        websiteService.saveWebsiteContent(websiteContent);
        return super.success();
    }

    /**
     * 删除网站内容
     */
    @ApiOperation(value = "删除网站内容")
    @PostMapping("/deleteWebsiteContent")
    public ApiRest deleteWebsiteContent(@RequestParam String websiteContentId) {
        websiteService.deleteWebsiteContent(websiteContentId);
        return super.success();
    }

    /**
     * 查看导航下的内容
     */
    @ApiOperation(value = "查看导航下的内容")
    @PostMapping("/websiteContentPage")
    public ApiRest websiteContentPage(@RequestBody PagingReqDTO<WebsiteContent> reqDTO) {
        IPage<WebsiteContent> websiteContentPage = websiteService.websiteContentPage(reqDTO);
        return super.success(websiteContentPage);
    }

    /**
     * 查看内容详情
     */
    @PostMapping("/websiteContentDetail")
    public ApiRest websiteContentDetail(@RequestParam String websiteContentId) {
        WebsiteContent websiteContent = websiteService.websiteContentDetail(websiteContentId);
        return super.success(websiteContent);
    }

}

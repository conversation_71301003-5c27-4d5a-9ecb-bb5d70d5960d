package com.yf.system.modules.enter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.base.utils.BeanMapper;
import com.yf.system.modules.enter.dto.EnterPersonDTO;
import com.yf.system.modules.enter.entity.EnterPerson;
import com.yf.system.modules.enter.mapper.EnterPersonMapper;
import com.yf.system.modules.enter.service.EnterPersonService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 指定人员业务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-09-15 14:35
 */
@Service
public class EnterPersonServiceImpl extends ServiceImpl<EnterPersonMapper, EnterPerson> implements EnterPersonService {

    @Override
    public void saveAll(Integer refType, String refId, List<String> userIds) {

        // 先删除
        QueryWrapper<EnterPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(EnterPerson::getRefType, refType)
                .eq(EnterPerson::getRefId, refId);
        this.remove(wrapper);

        // 再增加
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        List<EnterPerson> list = new ArrayList<>();

        for (String userId : userIds) {
            EnterPerson person = new EnterPerson();
            person.setRefType(refType);
            person.setRefId(refId);
            person.setUserId(userId);
            list.add(person);
        }

        this.saveBatch(list);
    }

    @Override
    public List<String> listUserIds(Integer refType, String refId) {
        QueryWrapper<EnterPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(EnterPerson::getRefType, refType)
                .eq(EnterPerson::getRefId, refId);
        List<EnterPerson> list = this.list(wrapper);
        List<String> ids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (EnterPerson item : list) {
                ids.add(item.getUserId());
            }
        }
        return ids;
    }

    @Override
    public List<EnterPersonDTO> listUserList(Integer refType, String refId) {
        QueryWrapper<EnterPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(EnterPerson::getRefType, refType)
                .eq(EnterPerson::getRefId, refId);
        List<EnterPerson> list = this.list(wrapper);
        return BeanMapper.mapList(list, EnterPersonDTO.class);
    }

    @Override
    public void delete(Integer refType, List<String> refIds) {
        QueryWrapper<EnterPerson> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(EnterPerson::getRefType, refType)
                .in(EnterPerson::getRefId, refIds);
        this.remove(wrapper);
    }
}

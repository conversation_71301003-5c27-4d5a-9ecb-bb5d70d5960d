package com.yf.system.modules.share.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnType;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 代理返佣记录
 * </p>
 * <AUTHOR>
 */
@Data
@TableName("el_sys_rebate_record")
public class SysRebateRecord extends Model<SysRebateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;


    /**
     * 创建日期
     */
    @TableField(fill = FieldFill.INSERT)
    @ColumnType(MySqlTypeConstant.DATETIME)
    private Date createDate;


    /**
     * 代理会员Id
     */
    @TableField
    private String sharerId;


    /**
     * 购买人id
     */
    @TableField
    private String buyerId;

    /**
     * 购买人
     */
    @TableField
    private String buyerName;

    /**
     * 代理会员级别Id
     */
    @TableField
    private String distributionRuleId;

    /**
     * 返佣类型 1-直接返佣，2-团队奖励
     */
    @TableField
    private Integer rebateType;

    /**
     * 返佣金额
     */
    @TableField
    private BigDecimal rebateAmount;

    /**
     * 购买套餐
     */
    @TableField
    private String goodsName;

    /**
     * 可提现日期
     */
    @TableField
    private String withdrawalDate;

    /**
     * 审计状态
     */
    @TableField
    private String state;


}

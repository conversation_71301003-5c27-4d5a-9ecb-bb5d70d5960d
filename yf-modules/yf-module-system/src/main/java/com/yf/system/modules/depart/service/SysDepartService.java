package com.yf.system.modules.depart.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.system.modules.depart.dto.DepartImportDTO;
import com.yf.system.modules.depart.dto.SysDepartDTO;
import com.yf.system.modules.depart.dto.request.DepartQueryReqDTO;
import com.yf.system.modules.depart.dto.response.SysClassTreeDTO;
import com.yf.system.modules.depart.dto.response.SysDepartTreeDTO;
import com.yf.system.modules.depart.entity.SysDepart;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 部门信息业务类
 * </p>
 * <AUTHOR>
 * @since 2020-09-02 17:25
 */
public interface SysDepartService extends IService<SysDepart> {

    /**
     * 保存
     * @param reqDTO
     */
    void save(SysDepartDTO reqDTO);


    /**
     * 查找部门树结构
     * @return
     */
    List<SysDepartTreeDTO> findTree(boolean self);

    /**
     * 排序
     * @param id
     * @param sort
     */
    void sort(String id, Integer sort);

    /**
     * 同步部门信息
     * @param str 以逗号隔开的部门
     * @return
     */
    String syncDepart(String str);

    /**
     * 删除部门
     * @param ids
     */
    void delete(List<String> ids);

    /**
     * 根据编码查找
     * @param code
     * @return
     */
    String findByCode(String code);

    /**
     * 根据编码查找
     * @param code
     * @return
     */
    SysDepart findDepartByCode(String code);


    String findDomainByCode(String code);

    /**
     * 根据部门id查看自定义配置
     * @param code
     * @return
     */
    SysDepartDTO findConfiguration(String code);


    /**
     * 查找部门树结构
     * @return
     */
    List<SysDepartTreeDTO> findOrderTree(boolean self);


    /**
     * 教师所在学校的班级列表
     * @param departType 信息科技-标识 0-全部，1-学校，2-年级，3-班级
     * @return
     */
    List<SysDepartTreeDTO> classList(Integer departType);


    /**
     * 教师端-班级管理-年级班级列表
     * @return
     */
    List<SysClassTreeDTO> gradeClassList();





    /**
     * 学生端登录-学校列表
     * @param reqDTO
     * @return
     */
    List<SysClassTreeDTO> schoolList(SysClassTreeDTO reqDTO);


    /**
     * 填充部门编号
     * @param depart
     */
    void fillCode(SysDepart depart);


    /**
     * 校验同级部门是否存在
     * @param name
     * @param id
     * @param parentId
     * @param gradeValueId
     * @return
     */
    boolean checkExists(String name, String id, String parentId, String gradeValueId);


    /**
     * 根据编码找学校
     * @param deptCode
     * @return
     */
    SysDepart findSchool(String deptCode);


    /**
     * 根据编码找学校
     * @param deptCode
     * @param departType
     * @return
     */
    SysDepart findDeptByCode(String deptCode, Integer departType);


    /**
     * 教师端-班级管理- 年级-班级-分组列表
     * @return
     */
    List<SysClassTreeDTO> departTree();


    /**
     * 移动部门及其用户到新的部门节点方法
     * @param departId
     * @param pid
     * @return
     */
    void moveDept(String departId, String pid,Boolean firstDept,String province,String city,String district);


    /**
     * 信息科技-删除班级
     * @param id
     */
    void deleteClass(String id);


    /**
     * 信息科技-班级升年级操作
     *        将班级上的年级表示更新为下一年级
     *        毕业的班级标识isPrevious=1
     *        毕业的用户设置毕业时间
     */
    void gradeUpJob();


    /**
     * 教师端-班级管理-年级班级列表-新
     * @return
     */
    List<SysClassTreeDTO> gradeClazzList(Boolean isGroup);



    /**
     * 部门更改名称后，修改子集固化的信息
     * @param parentId
     * @param deptName
     * @return
     */
    void updateChildren(String parentId, String deptName);



    /**
     * 部门管理-部门父级列表
     * @return
     */
    List<SysDepartTreeDTO> departList();


    /**
     * 部门管理-部门详情树形结构
     * @param deptCode
     * @return
     */
    List<SysDepartTreeDTO> departDetailTree(String deptCode);


    /**
     * 部门管理-学校管理列表
     * @param reqDTO
     * @return
     */
    IPage<SysDepartTreeDTO> schoolPage(PagingReqDTO<DepartQueryReqDTO> reqDTO);


    /**
     * 部门管理-班级管理树
     * @param deptCode
     * @return
     */
    List<SysDepartTreeDTO> clazzTree(String deptCode);


    /**
     * 部门管理-学校管理-筛选部门数（截止到学校上级）
     * @return
     */
    List<SysDepartTreeDTO> selectDepartTree();


    /**
     * 部门管理-学校管理-学校导入
     * @param list
     * @param deptCode
     * @return
     */
    Map<String, Object> importSchool(List<DepartImportDTO> list, String deptCode);




    /**
     * 部门管理-根据部门编码查询子集一层
     * @param deptCode
     * @param showMore
     * @return
     */
    List<SysDepartTreeDTO> selectDepartChildren(String deptCode, Integer showMore);


    /**
     * 学校管理-根据部门编码查询子集一层，不包含学校及以下
     * @param deptCode
     * @return
     */
    List<SysDepartTreeDTO> departChildrenList(String deptCode);

    /**
     * 根据编程找完整部门名称
     * @param id
     * @param wholeName
     * @return
     */
    String findWholeName(String id,String wholeName);



    /**
     * 部门名称模糊搜索
     * @param name
     * @return
     */
    List<SysDepartDTO> selectDepartList(String name);



    /**
     * 部门名称详情
     * @param deptCode
     * @return
     */
    SysDepartDTO selectDepart(String deptCode);


    /**
     * 部门管理-学校管理列表导出excel
     * @param reqDTO
     * @return
     */
    void schoolExport(PagingReqDTO<DepartQueryReqDTO> reqDTO);


    /**
     * 部门完整结构名称
     * @param departId
     * @return
     */
    SysDepartDTO schoolDetail(String departId);


    /**
     * 生成省市县部门-一次性调用
     * @return
     */
    void saveDepart();


    /**
     * 学校管理-根据行政区查部门树形结构列表
     * @param reqDTO
     * @return
     */
    List<SysDepartTreeDTO> departTreeByDistrict(SysDepartDTO reqDTO);

    /**
     * 学校管理-根据行政区查部门
     * @param reqDTO
     * @return
     */
    SysDepartDTO departByDistrict(SysDepartDTO reqDTO);


    /**
     * 部门管理-往届班级管理树
     * @param deptCode
     * @return
     */
    List<SysDepartTreeDTO> previousClazzTree(String deptCode);


    /**
     * 教师端-班级管理-设置班级显示列表
     * @return
     */
    List<SysClassTreeDTO> gradeShowClazzList();
}

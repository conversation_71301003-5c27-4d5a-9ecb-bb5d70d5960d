package com.yf.system.modules.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.system.modules.user.dto.SysUserLevelDTO;
import com.yf.system.modules.user.entity.SysUserLevel;

import java.util.List;

/**
 * <p>
 * 用户会员等级
 * </p>
 * <AUTHOR>
 */
public interface SysUserLevelService extends IService<SysUserLevel> {

    /**
     * 列表
     * @return
     */
    List<SysUserLevelDTO> levelList();

    /**
     * 根据级别查等级规则
     * @param level
     * @return
     */
    SysUserLevelDTO findLevelRule(Integer level);

    /**
     * 后台开通会员添加算力
     * @param points
     * @param userId
     * @param pastDate
     * @return
     */
    void openVipLevel(Integer points, String userId, String pastDate);
}

package com.yf.system.modules.enter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.system.modules.enter.entity.EnterGroup;
import com.yf.system.modules.enter.mapper.EnterGroupMapper;
import com.yf.system.modules.enter.service.EnterGroupService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 指定群组业务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-09-15 14:35
 */
@Service
public class EnterGroupServiceImpl extends ServiceImpl<EnterGroupMapper, EnterGroup> implements EnterGroupService {


    @Override
    public void saveAll(Integer refType, String refId, List<String> groups) {
        // 先删除
        QueryWrapper<EnterGroup> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(EnterGroup::getRefType, refType)
                .eq(EnterGroup::getRefId, refId);
        this.remove(wrapper);

        // 再增加
        if (CollectionUtils.isEmpty(groups)) {
            return;
        }
        List<EnterGroup> list = new ArrayList<>();

        for (String groupId : groups) {
            EnterGroup group = new EnterGroup();
            group.setRefType(refType);
            group.setRefId(refId);
            group.setGroupId(groupId);
            list.add(group);
        }

        this.saveBatch(list);
    }

    @Override
    public List<String> listGroupIds(Integer refType, String refId) {
        QueryWrapper<EnterGroup> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(EnterGroup::getRefType, refType)
                .eq(EnterGroup::getRefId, refId);
        List<EnterGroup> list = this.list(wrapper);
        List<String> ids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (EnterGroup item : list) {
                ids.add(item.getGroupId());
            }
        }
        return ids;
    }

    @Override
    public List<String> listUserIds(Integer refType, String refId) {
        return baseMapper.listUserIds(refType, refId);
    }

    @Override
    public void delete(Integer refType, List<String> refIds) {
        QueryWrapper<EnterGroup> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(EnterGroup::getRefType, refType)
                .in(EnterGroup::getRefId, refIds);
        this.remove(wrapper);
    }
}

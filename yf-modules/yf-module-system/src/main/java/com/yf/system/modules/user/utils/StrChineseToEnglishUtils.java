package com.yf.system.modules.user.utils;


import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串中文转换拼音工具类
 * <AUTHOR>
 */
@Component
public class StrChineseToEnglishUtils {

    private static String defaultPassword;

    @Value("${user.default-password}")
    public void setDefaultPassword(String defaultPassword) {
        StrChineseToEnglishUtils.defaultPassword = defaultPassword;
    }

    // 将汉字转换为全拼
    public static String getPinYin(String src) {

        char[] t1 = null;
        t1 = src.toCharArray();
        String[] t2 = new String[t1.length];
        HanyuPinyinOutputFormat t3 = new HanyuPinyinOutputFormat();

        t3.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        t3.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        t3.setVCharType(HanyuPinyinVCharType.WITH_V);
        String t4 = "";
        int t0 = t1.length;
        try {
            for (int i = 0; i < t0; i++) {
                // 判断是否为汉字字符
                if (Character.toString(t1[i]).matches(
                        "[\\u4E00-\\u9FA5]+")) {
                    t2 = PinyinHelper.toHanyuPinyinStringArray(t1[i], t3);
                    t4 += t2[0];
                } else
                    t4 += Character.toString(t1[i]);
            }
            // System.out.println(t4);
            return t4;
        } catch (BadHanyuPinyinOutputFormatCombination e1) {
            e1.printStackTrace();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return t4;
    }

    // 返回中文的首字母
    public static String getPinYinHeadChar(String str) {

        String convert = "";
        for (int j = 0; j < str.length(); j++) {
            char word = str.charAt(j);
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(word);
            if (pinyinArray != null) {
                convert += pinyinArray[0].charAt(0);
            } else {
                convert += word;
            }
        }
        return convert;
    }

    // 将字符串转移为ASCII码
    public static String getCnASCII(String cnStr) throws UnsupportedEncodingException {
        StringBuffer strBuf = new StringBuffer();
        byte[] bGBK = cnStr.getBytes("UTF-8");
        for (int i = 0; i < bGBK.length; i++) {
            strBuf.append(Integer.toHexString(bGBK[i] & 0xff));
        }
        return strBuf.toString();
    }

    // 判断一个字符串是否含有数字
    public static boolean hasDigit(String content) {
        boolean flag = false;
        Pattern p = Pattern.compile(".*\\d+.*");
        Matcher m = p.matcher(content);
        if (m.matches()) {
            flag = true;
        }
        return flag;
    }

    //截取数字  【读取字符串中第一个连续的字符串，不包含后面不连续的数字】
    public static String getNumbers(String content) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            return matcher.group(0);
        }
        return "";
    }

    // 截取非数字
    public static String splitNotNumber(String content) {
        Pattern pattern = Pattern.compile("\\D+");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            return matcher.group(0);
        }
        return "";
    }


    // 定义正则表达式来匹配汉字的Unicode范围
    private static final String CHINESE_REGEX = "^[\\u4e00-\\u9fa5]+$";

    public static boolean isChinese(String input) {
        // 编译正则表达式
        Pattern pattern = Pattern.compile(CHINESE_REGEX);
        // 创建匹配器对象
        Matcher matcher = pattern.matcher(input);
        // 返回是否匹配
        return matcher.matches();
    }


    /** 验证字符串是否仅包含字母或数字
     * @param str 要验证的字符串
     * @return 如果字符串仅包含字母或数字，则返回true；否则返回false
     */
    public static boolean isAlphaNumeric(String str) {
        // 字符串为空或null，返回false
        if (str == null || str.isEmpty()) {
            return false;
        }

        // 正则表达式匹配字母和数字
        return str.matches("[a-zA-Z0-9]+");
    }


    public static boolean judgePassword(String password) {
        if (StringUtils.isBlank(password) || password.length() < 6 || password.length() >18) {
            return false;
        }
        // 密码内容类型数
        int num = 0;
        // 需要校验的类型正则
        String[] regexs = {".*\\d.*$", ".*[a-z].*$", ".*[A-Z].*$", ".*[!@#$%^&\\.()].*$"};
        for (String regex : regexs) {
            // 编译正则表达式
            Pattern pattern = Pattern.compile(regex);
            // 创建匹配器对象
            Matcher matcher = pattern.matcher(password);
            // 返回是否匹配
            if (matcher.matches()) {
                num += 1;
            }
        }
        return num >= 2;
    }

    public static boolean isWeakPassword(String password) {

        // 密码长度至少应为6个字符
        if (password.length() < 6) {
            return true;
        }

        // 检查是否包含字母
        if (!password.matches(".*[A-Z].*") && !password.matches(".*[a-z].*")) {
            return true;
        }

        // 检查是否包含数字
        if (!password.matches(".*[0-9].*")) {
            return true;
        }

//        // 检查是默认密码
//        if (password.equals(defaultPassword)) {
//            return true;
//        }

        // 如果以上条件都不满足，则密码不是较弱的密码
        return false;
    }



}

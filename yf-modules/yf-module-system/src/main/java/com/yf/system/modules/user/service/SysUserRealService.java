package com.yf.system.modules.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yf.base.api.api.dto.PagingReqDTO;
import com.yf.system.modules.user.dto.SysUserRealDTO;
import com.yf.system.modules.user.dto.request.RealAuditReqDTO;
import com.yf.system.modules.user.entity.SysUserReal;

import java.util.List;

/**
 * <p>
 * 实名认证业务接口类
 * </p>
 * <AUTHOR>
 * @since 2023-07-13 15:08
 */
public interface SysUserRealService extends IService<SysUserReal> {

    /**
     * 分页查询数据
     * @param reqDTO
     * @return
     */
    IPage<SysUserRealDTO> paging(PagingReqDTO<SysUserRealDTO> reqDTO);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    void delete(List<String> ids);

    /**
     * 查找详情
     * @param id
     * @return
     */
    SysUserRealDTO detail(String id);


    /**
     * 用户提交实名认证
     * @param realDTO
     */
    void submit(SysUserRealDTO realDTO);

    /**
     * 管理员审核认证
     * @param reqDTO
     * @param auditBy
     */
    void audit(RealAuditReqDTO reqDTO, String auditBy);

    /**
     * 同步状态
     * @param userId
     * @param realName
     * @param idCard
     * @param face
     * @return
     */
    boolean sync(String userId, String realName, String idCard, String face);

    /**
     * 自动审核
     * @param ids
     */
    void autoAudit(List<String> ids);
}

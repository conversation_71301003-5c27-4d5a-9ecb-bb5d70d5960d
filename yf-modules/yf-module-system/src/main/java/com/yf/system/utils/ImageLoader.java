package com.yf.system.utils;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/***
 * @title ImageLoader
 * @Description 这个类用于从整个图片中加载每个项目的图片
 * <AUTHOR>
 * @create 2023/7/11 10:38
 **/

public class ImageLoader {

    private static BufferedImage sourceImg;

    public ImageLoader(String imagePath) throws IOException {
        sourceImg = ImageIO.read(new File(imagePath));

    }

    /**
     * 处理Image
     * <p>
     * 以左上角顶点为基准
     * @param posX   左上角顶点的x轴
     * @param posY   左上角顶点的x轴
     * @param width  图片的宽度
     * @param height 图片的高度
     * @return img
     */
    public Image getImage(int posX, int posY, int width, int height) {
        BufferedImage targetImg = this.sourceImg.getSubimage(posX, posY, width, height);
        Image img = new ImageIcon(targetImg).getImage();
        return img;
    }

    /**
     * 处理ImageIcon
     * <p>
     * 以左上角顶点为基准
     * @param posX   左上角顶点的x轴
     * @param posY   左上角顶点的y轴
     * @param width  图片的宽度
     * @param height 图片的高度
     * @return img
     */
    public static ImageIcon getIconImage(int posX, int posY, int width, int height) {
        BufferedImage targetImg = sourceImg.getSubimage(posX, posY, width, height);
        ImageIcon img = new ImageIcon(targetImg);
        return img;
    }

    /**
     * 感觉处理出来的图片不如 上面两个的。
     * @param button
     * @param posX
     * @param posY
     * @param width
     * @param height
     * @param tip
     * @return
     */
    public static JButton changeIconSize(JButton button, int posX, int posY, int width, int height, String tip) {
        button.setBounds(0, 0, width, height);
        ImageIcon buttonImg = getIconImage(posX, posY, width, height);
        buttonImg.getImage();
        // 改变图片的大小
        Image temp = buttonImg.getImage().getScaledInstance(button.getWidth(), button.getHeight(), Image.SCALE_DEFAULT);
        button = new JButton(new ImageIcon(temp));
        // 以下是设置按钮为透明的，这样图片才能填充满整个按钮
        button.setOpaque(false); // 设置边缘不显示
        button.setContentAreaFilled(false); // 显示为透明按钮
        // button.setFocusPainted(false); //去掉按钮文字周围的焦点框
        button.setBorderPainted(false);// 去除按钮的边框
        // button.setBorder(null); //设置边缘为空
        // button.setMargin(new Insets(0, 0, 0, 0));//是设置边距的。
        button.setToolTipText(tip); // 提示

        return button;
    }

    /**
     * @param button
     * @param url
     * @param width
     * @param height
     * @param tip
     * @return
     */
    public static JButton changeIconSize(JButton button, String url, int width, int height, String tip) {
        button.setBounds(0, 0, width, height);
        ImageIcon buttonImg = new ImageIcon(url);
        // 改变图片的大小
        Image temp = buttonImg.getImage().getScaledInstance(button.getWidth(), button.getHeight(),
                buttonImg.getImage().SCALE_DEFAULT);
        button = new JButton(new ImageIcon(temp));
        button.setToolTipText(tip); // 提示

        return button;
    }


    public static void imageRegionFinder(String url) throws IOException {
        // Load the image
        File inputFile = new File(url);
        BufferedImage image = ImageIO.read(inputFile);

        // Find the regions
        List<Rectangle> regions = new ArrayList<>();
        Color bgColor = new Color(image.getRGB(0, 0)); // assume the top left pixel is the background color
        for (int y = 0; y < image.getHeight(); y++) {
            for (int x = 0; x < image.getWidth(); x++) {
                Color color = new Color(image.getRGB(x, y));
                if (!color.equals(bgColor)) {
                    Rectangle region = findRegion(image, x, y, bgColor);
                    regions.add(region);
                }
            }
        }

        // Print the regions
        for (int i = 0; i < regions.size(); i++) {
            Rectangle region = regions.get(i);
            System.out.println("Region " + i + ": x=" + region.x + ", y=" + region.y + ", width=" + region.width + ", height=" + region.height);
        }
    }

    private static Rectangle findRegion(BufferedImage image, int startX, int startY, Color bgColor) {
        int x = startX;
        int y = startY;
        int width = 0;
        int height = 0;
        while (x < image.getWidth() && new Color(image.getRGB(x, y)).equals(bgColor)) {
            x++;
        }
        while (y < image.getHeight() && new Color(image.getRGB(startX, y)).equals(bgColor)) {
            y++;
        }
        width = x - startX;
        height = y - startY;
        return new Rectangle(startX, startY, width, height);
    }

    /**
     * 通过图片路径将图片文件转化为字符数组
     * @param url 图片路径
     * @return byte[]
     */
    public static byte[] imageToBytes(String url) {
        ByteArrayOutputStream byteOutput = new ByteArrayOutputStream();
        BufferedImage bufferedImage = null;
        try {
            bufferedImage = ImageIO.read(new File(url));
            ImageIO.write(bufferedImage, "jpg", byteOutput);
            return byteOutput.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (byteOutput != null)
                    byteOutput.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


}

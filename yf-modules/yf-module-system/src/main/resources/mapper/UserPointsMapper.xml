<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.system.modules.points.mapper.UserPointsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.system.modules.points.dto.UserPointsDTO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="points" property="points" />
        <result column="create_time" property="createTime" />
        <result column="ref_remark" property="refRemark" />
        <result column="ref_id" property="refId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`user_id`,`points`,`create_time`,`ref_remark`,`ref_id`
    </sql>
    

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.system.modules.group.mapper.SysGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.system.modules.group.dto.SysGroupDTO">
        <id column="id" property="id" />
        <result column="group_name" property="groupName" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="dept_code" property="deptCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`group_name`,`create_by`,`create_time`,`dept_code`
    </sql>

    <resultMap id="ListResultMap" type="com.yf.system.modules.group.dto.response.GroupListRespDTO">
        <result column="userCount" property="userCount" />
    </resultMap>

    <select id="paging" resultMap="ListResultMap">
        SELECT gp.*,(SELECT COUNT(DISTINCT gu.user_id) FROM el_sys_group_user gu LEFT JOIN el_sys_user uc ON uc.id=gu.user_id WHERE gu.group_id=gp.id AND uc.id IS NOT NULL) AS userCount FROM el_sys_group gp
        <where>
            <if test="query!=null">
                <if test="query.groupName!=null and query.groupName!=''">
                    AND gp.group_name LIKE CONCAT('%',#{query.groupName},'%')
                </if>
            </if>
        </where>
    </select>

</mapper>

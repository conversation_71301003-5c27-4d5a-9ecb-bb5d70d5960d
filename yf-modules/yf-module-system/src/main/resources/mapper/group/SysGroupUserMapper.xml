<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.system.modules.group.mapper.SysGroupUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.system.modules.group.dto.SysGroupUserDTO">
        <id column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="user_id" property="userId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`group_id`,`user_id`
    </sql>

    <resultMap id="ListResultMap" type="com.yf.system.modules.group.dto.response.GroupUserListRespDTO">
        <result column="user_name" property="userName" />
        <result column="real_name" property="realName" />
        <result column="dept_name" property="deptName" />
        <result column="mobile" property="mobile" />
    </resultMap>

    <select id="paging" resultMap="ListResultMap">
        SELECT gu.id,gu.user_id,uc.user_name,uc.real_name,uc.mobile,dept.dept_name
        FROM el_sys_user uc
        LEFT JOIN el_sys_group_user gu ON gu.user_id=uc.id
        LEFT JOIN el_sys_depart dept ON uc.dept_code=dept.dept_code
        WHERE gu.group_id=#{query.groupId}
        <if test="query.realName!=null and query.realName!=''">
            AND (uc.user_name LIKE CONCAT('%',#{query.realName},'%') OR uc.real_name LIKE CONCAT('%',#{query.realName},'%'))
        </if>
    </select>


</mapper>

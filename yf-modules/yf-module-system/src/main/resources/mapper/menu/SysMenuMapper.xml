<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.system.modules.menu.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.system.modules.menu.entity.SysMenu">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="menu_type" property="menuType" />
        <result column="permission_tag" property="permissionTag" />
        <result column="path" property="path" />
        <result column="component" property="component" />
        <result column="redirect" property="redirect" />
        <result column="name" property="name" />
        <result column="meta_title" property="metaTitle" />
        <result column="meta_icon" property="metaIcon" />
        <result column="meta_active_menu" property="metaActiveMenu" />
        <result column="meta_no_cache" property="metaNoCache" />
        <result column="hidden" property="hidden" />
        <result column="sort" property="sort" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="data_flag" property="dataFlag" />
        <result column="ext" property="ext" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`parent_id`,`menu_type`,`permission_tag`,`path`,`component`,`redirect`,`name`,`meta_title`,`meta_icon`,`meta_active_menu`,`meta_no_cache`,`hidden`,`sort`,`create_time`,`update_time`,`create_by`,`update_by`,`data_flag`
    </sql>



    <select id="findUserMenu" resultType="String">
        SELECT DISTINCT rm.menu_id
        FROM el_sys_role_menu rm
        LEFT JOIN el_sys_user_role ro ON ro.role_id = rm.role_id
        WHERE ro.user_id=#{userId}
    </select>

    <select id="findAllMenu" resultMap="BaseResultMap">
        SELECT mu.* FROM el_sys_menu mu
        <where>
            <if test="query!=null">
                <if test="query.name!=null and query.name!=''">
                    AND (mu.`meta_title` LIKE CONCAT('%',#{query.name},'%') OR mu.`name` LIKE CONCAT('%',#{query.name},'%'))
                </if>
            </if>
        </where>

        ORDER BY mu.sort ASC
    </select>


    <select id="findPermissions" resultType="String">
        SELECT DISTINCT permission_tag FROM el_sys_role_menu rm
        LEFT JOIN el_sys_menu sm ON sm.id=rm.menu_id
        LEFT JOIN el_sys_user_role ur ON rm.role_id=ur.role_id
        WHERE ur.user_id=#{userId} AND permission_tag IS NOT NULL
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.system.modules.enter.mapper.EnterDistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.system.modules.enter.dto.EnterDistDTO">
        <id column="id" property="id" />
        <result column="ref_type" property="refType" />
        <result column="ref_id" property="refId" />
        <result column="user_id" property="userId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`ref_type`,`ref_id`,`user_id`
    </sql>

</mapper>

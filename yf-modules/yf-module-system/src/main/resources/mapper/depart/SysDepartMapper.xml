<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.system.modules.depart.mapper.SysDepartMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.system.modules.depart.entity.SysDepart">
        <id column="id" property="id" />
        <result column="dept_type" property="deptType" />
        <result column="parent_id" property="parentId" />
        <result column="dept_name" property="deptName" />
        <result column="dept_code" property="deptCode" />
        <result column="dept_level" property="deptLevel" />
        <result column="sort" property="sort" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="data_flag" property="dataFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`dept_type`,`parent_id`,`dept_name`,`dept_code`,`dept_level`,`sort`,`create_time`,`update_time`,`create_by`,`update_by`,`data_flag`
    </sql>


    <resultMap id="TreeResultMap"
               type="com.yf.system.modules.depart.dto.response.SysDepartTreeDTO"
               extends="BaseResultMap">
        <collection property="children" column="{id=id,deptCodes=deptCodes,likeCode=likeCode}" select="findChildren" ></collection>
    </resultMap>


    <select id="findChildren" resultMap="TreeResultMap">
        SELECT dp.*,"${deptCodes}" AS deptCodes,"${likeCode}" AS likeCode FROM el_sys_depart dp WHERE parent_id=#{id}
        <if test="deptCodes!=null and deptCodes!=''">
            AND (
             dept_code IN (${deptCodes})
             <if test="likeCode!=null and likeCode!=''">
                 OR dept_code LIKE CONCAT(#{likeCode}, '%')
             </if>
            )
        </if>

        ORDER BY `sort` ASC
    </select>

    <select id="findTree" resultMap="TreeResultMap">
        SELECT dp.*,"${query.deptCodes}" AS deptCodes,"${query.likeCode}" AS likeCode FROM el_sys_depart dp
        WHERE
        1 = 1
        <if test="query!=null">
            <if test="query.parentId!=null and query.parentId!=''">
                AND dp.parent_id=#{query.parentId}
            </if>

            <if test="query.isPrevious!=null">
                AND dp.is_previous=#{query.isPrevious}
            </if>
            <if test="query.graduationYear!=null">
                AND dp.graduation_year=#{query.graduationYear}
            </if>

            <if test="query.departType!=null">
                AND dp.depart_type=#{query.departType}
            </if>
            <if test="query.gradeValueId!=null and query.gradeValueId!=''">
                AND dp.grade_value_id=#{query.gradeValueId}
            </if>
            <if test="query.deptCodes!=null">
                AND (
                 dp.dept_code IN (${query.deptCodes})
                 <if test="query.likeCode!=null and query.likeCode!=''">
                     OR dp.dept_code LIKE CONCAT(#{query.likeCode}, '%')
                 </if>
                )
            </if>

            <if test="query.deptCode!=null and query.deptCode!=''">
                AND dp.dept_code=#{query.deptCode}
            </if>
            <if test="query.deptName!=null and query.deptName!=''">
                AND dp.dept_name LIKE CONCAT('%',#{query.deptName},'%')
            </if>
        </if>

        ORDER BY `sort` ASC
    </select>


    <select id="findOrderTree"  resultMap="TreeResultMap">
        SELECT dp.*,"${query.deptCodes}" AS deptCodes,"${query.likeCode}" AS likeCode FROM el_sys_depart dp
        WHERE

        <if test="query!=null">

            <if test="query.deptCodes!=null">

                dp.dept_code IN (${query.deptCodes})


            </if>
        </if>

        ORDER BY `sort` ASC

    </select>


    <select id="findClassList" resultMap="TreeResultMap">
        SELECT dp.*,"${query.deptCodes}" AS deptCodes,"${query.likeCode}" AS likeCode FROM el_sys_depart dp
        WHERE 1=1
        <if test="query!=null">
            <if test="query.departType!=null ">
                AND dp.depart_type=#{query.departType}
            </if>

            <if test="query.deptCodes!=null">
                AND (
                dp.dept_code IN (${query.deptCodes})
                <if test="query.likeCode!=null and query.likeCode!=''">
                    OR dp.dept_code LIKE CONCAT(#{query.likeCode}, '%')
                </if>
                )
            </if>

            <if test="query.deptCode!=null and query.deptCode!=''">
                AND dp.dept_code=#{query.deptCode}
            </if>
            <if test="query.deptName!=null and query.deptName!=''">
                AND dp.dept_name LIKE CONCAT('%',#{query.deptName},'%')
            </if>
        </if>

        ORDER BY `sort` ASC

    </select>


    <select id="findSchoolTree" resultMap="SchoolTreeResultMap">
        SELECT dp.*,"${query.deptCodes}" AS deptCodes,"${query.likeCode}" AS likeCode FROM el_sys_depart dp
        WHERE dp.parent_id='0'

        <if test="query!=null">

            <if test="query.deptCodes!=null">
                AND (
                dp.dept_code IN (${query.deptCodes})
                <if test="query.likeCode!=null and query.likeCode!=''">
                    OR dp.dept_code LIKE CONCAT(#{query.likeCode}, '%')
                </if>
                )
            </if>

            <if test="query.deptCode!=null and query.deptCode!=''">
                AND dp.dept_code=#{query.deptCode}
            </if>
            <if test="query.deptName!=null and query.deptName!=''">
                AND dp.dept_name LIKE CONCAT('%',#{query.deptName},'%')
            </if>
        </if>

        ORDER BY `sort` ASC

    </select>


    <resultMap id="SchoolTreeResultMap" type="com.yf.system.modules.depart.dto.response.SysDepartTreeDTO" extends="BaseResultMap">
        <collection property="children" javaType="ArrayList" ofType="com.yf.system.modules.depart.dto.response.SysDepartTreeDTO"
                    column="{id=id, deptCodes=deptCodes, likeCode=likeCode, departType=depart_type}"
                    select="findSchoolChildren" notNullColumn="departType"/>
    </resultMap>


    <select id="findSchoolChildren" resultMap="SchoolTreeResultMap">
        SELECT dp.*,"${deptCodes}" AS deptCodes,"${likeCode}" AS likeCode FROM el_sys_depart dp
        WHERE parent_id = #{id}  AND dp.depart_type != 1
            <if test="deptCodes!=null and deptCodes!=''">
                AND (
                dept_code IN (${deptCodes})
                <if test="likeCode!=null and likeCode!=''">
                    OR dept_code LIKE CONCAT(#{likeCode}, '%')
                </if>
                )
            </if>
        ORDER BY `sort` ASC
    </select>


    <select id="departList" resultType="com.yf.system.modules.depart.dto.response.SysDepartTreeDTO">
        SELECT dp.* FROM el_sys_depart dp
        WHERE 1 = 1
        <if test="query!=null">
            <if test="query.parentId!=null and query.parentId!=''">
                AND dp.parent_id=#{query.parentId}
            </if>

            <if test="query.departType!=null">
                AND dp.depart_type=#{query.departType}
            </if>
            <if test="query.excludeDepartType!=null">
                AND dp.depart_type != #{query.excludeDepartType}
            </if>

            <if test="query.excludeDepartTypes!=null and query.excludeDepartTypes.size >0 ">
                AND dp.depart_type not IN
                <foreach collection="query.excludeDepartTypes" open="(" close=")" separator="," item="departType">#{departType}</foreach>
            </if>
            <if test="query.deptCodes!=null">
                AND (
                dp.dept_code IN (${query.deptCodes})
                <if test="query.likeCode!=null and query.likeCode!=''">
                    OR dp.dept_code LIKE CONCAT(#{query.likeCode}, '%')
                </if>
                )
            </if>

            <if test="query.deptCode!=null and query.deptCode!=''">
                AND dp.dept_code=#{query.deptCode}
            </if>
            <if test="query.deptName!=null and query.deptName!=''">
                AND dp.dept_name LIKE CONCAT('%',#{query.deptName},'%')
            </if>
        </if>

        ORDER BY `sort` ASC

    </select>



    <select id="schoolPage" resultType="com.yf.system.modules.depart.dto.response.SysDepartTreeDTO">
        SELECT dp.* FROM el_sys_depart dp
        WHERE 1 = 1
        <if test="query!=null">
            <if test="query.parentId!=null and query.parentId!=''">
                AND dp.parent_id=#{query.parentId}
            </if>

            <if test="query.departType!=null">
                AND dp.depart_type=#{query.departType}
            </if>

            <if test="query.province!=null and query.province!=''">
                AND dp.province=#{query.province}
            </if>


            <if test="query.city!=null and query.city!=''">
                AND dp.city=#{query.city}
            </if>


            <if test="query.county!=null and query.county!=''">
                AND dp.county=#{query.county}
            </if>

            <if test="query.deptCodes!=null">
                AND (
                dp.dept_code IN (${query.deptCodes})
                <if test="query.likeCode!=null and query.likeCode!=''">
                    OR dp.dept_code LIKE CONCAT(#{query.likeCode}, '%')
                </if>
                )
            </if>

            <if test="query.deptCode!=null and query.deptCode!=''">
                AND dp.dept_code LIKE CONCAT(#{query.deptCode}, '%')
            </if>
            <if test="query.deptName!=null and query.deptName!=''">
                AND dp.dept_name LIKE CONCAT('%',#{query.deptName},'%')
            </if>
            <if test="query.creatorIds!=null">
                AND dp.create_by IN
                <foreach collection="query.creatorIds" open="(" close=")" separator="," item="creatorId">#{creatorId}</foreach>
            </if>

        </if>

        ORDER BY dp.create_time DESC

    </select>


</mapper>

package com.yf.ability.captcha.controller;


import com.wf.captcha.SpecCaptcha;
import com.yf.ability.captcha.dto.request.CheckCaptchaReqDTO;
import com.yf.ability.captcha.service.OldCaptchaService;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.WritableByteChannel;
import javax.servlet.http.HttpServletResponse;
import java.nio.channels.WritableByteChannel;


/**
 * <p>
 * 图形验证码生成
 * </p>
 * <AUTHOR>
 * @since 2019-04-16 10:14
 */
@Api(tags = {"图形验证码"})
@RestController
@RequestMapping("/api/common/captcha")
public class OldCaptchaController extends BaseController {

    @Autowired
    private OldCaptchaService captchaService;



    @RequestMapping(value = "/gen", method = RequestMethod.GET)
    @ApiOperation(value = "生成图形验证码", notes = "传入一个key，务必保证key是全系统唯一，建议使用UUID算法，直接使用<img src='/api/common/captcha?key=uuid'>调用")
    public void captcha(HttpServletResponse response, @RequestParam("key") String key) throws Exception {

        // 设置请求头为输出图片类型
        response.setContentType("image/gif");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        // 算术类型
        SpecCaptcha captcha = new SpecCaptcha(130, 48,4);
        captcha.setCharType(SpecCaptcha.TYPE_ONLY_NUMBER);

        // 将 captcha 图像数据输出到字节数组
        // 获取 Base64 编码的字符串
        String base64Image = captcha.toBase64();

        // 去掉 Data URL 的前缀
        String base64Data = base64Image.substring(base64Image.indexOf(",") + 1);

        // 将 Base64 字符串解码为字节数组
        byte[] bytes = java.util.Base64.getDecoder().decode(base64Data);

        // 使用 NIO 写入图片流
        WritableByteChannel channel = Channels.newChannel(response.getOutputStream());
        ByteBuffer buffer = ByteBuffer.allocate(bytes.length); // 分配与字节数组相同大小的缓冲区
        buffer.put(bytes); // 将字节数组写入缓冲区
        buffer.flip(); // 切换到读取模式
        channel.write(buffer); // 写入到通道
        channel.close();

        // 存入REDIS
        captchaService.saveCaptcha(key, captcha.text().toLowerCase());

    }


    @PostMapping("/check")
    @ApiOperation(value = "校验验证码", notes = "验证对应key的验证码是否正确，用于纯前端校验的场景，如学习防呆")
    public ApiRest<Boolean> check(@RequestBody CheckCaptchaReqDTO reqDTO) throws Exception {
        boolean result = captchaService.checkCaptcha(reqDTO.getCaptchaKey(), reqDTO.getCaptchaValue());
        return super.success(result);
    }

}

package com.yf.ability.upload.providers.local.utils;


import com.aliyun.oss.*;
import com.aliyun.oss.model.CopyObjectResult;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.utils.DateUtils;
import com.yf.base.utils.file.LocalFileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;
import java.util.Date;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import static org.springframework.util.FileCopyUtils.BUFFER_SIZE;

/**
 * 文件上传工具
 * <AUTHOR>
 * @date 2019-07-30 21:00
 */
@Component
public class OssUtils {

    private static String endpoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String ossBucketName;
    private static String ossUrl;

    @Value("${aliyun.oss.endpoint}")
    public void setEndpoint(String endpoint) {
        OssUtils.endpoint = endpoint;
    }

    @Value("${aliyun.oss.accessKeyId}")
    public void setAccessKeyId(String accessKeyId) {
        OssUtils.accessKeyId = accessKeyId;
    }

    @Value("${aliyun.oss.accessKeySecret}")
    public void setAccessKeySecret(String accessKeySecret) {
        OssUtils.accessKeySecret = accessKeySecret;
    }

    @Value("${aliyun.oss.bucket}")
    public void setOssBucketName(String ossBucketName) {
        OssUtils.ossBucketName = ossBucketName;
    }

    @Value("${aliyun.oss.url}")
    public void setOssUrl(String ossUrl) {
        OssUtils.ossUrl = ossUrl;
    }

    public static OSSClient getOSSClient(String bucketName) {
        return (OSSClient) new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

    /**
     * 后缀分割符号
     */
    private static final String SUFFIX_SPLIT = ".";

    /**
     * 重命名文件
     * @param fileName
     * @return
     */
    public static String renameFile(String fileName) {

        // 没后缀随机产生文件名
        if (!fileName.contains(SUFFIX_SPLIT)) {
            return IdWorker.get32UUID();
        }

        //文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf("."));

        //以系统时间命名
        return IdWorker.getIdStr() + suffix;

    }


    /**
     * 处理新的文件路径，为上传文件预设目录，如：2021/01/01/xxx.jpg，要注意的是，前面没有斜杠
     * @param file 文件
     * @return
     */
    public static String processPath(MultipartFile file) {

        // 创建OSSClient实例。
        String fileName = file.getOriginalFilename();

        // 需要重命名
        fileName = OssUtils.renameFile(fileName);

        //获得上传的文件夹
        String dir = DateUtils.formatDate(new Date(), "yyyy/MM/dd/");

        return new StringBuffer(dir).append(fileName).toString();

    }

    /**
     * 处理新的文件路径，为上传文件预设目录，如：2021/01/01/xxx.jpg，要注意的是，前面没有斜杠
     * @param fileName 文件
     * @return
     */
    public static String processPath(String fileName) {

        // 需要重命名
        fileName = OssUtils.renameFile(fileName);

        //获得上传的文件夹
        String dir = DateUtils.formatDate(new Date(), "yyyy/MM/dd/");

        return new StringBuffer(dir).append(fileName).toString();

    }

    /**
     * 检查文件夹是否存在，不存在则创建
     * @param fileName
     * @return
     */
    public static void checkDir(String fileName) {
        int index = fileName.lastIndexOf("/");
        if (index == -1) {
            return;
        }

        String dir = fileName.substring(0, index);
        try {
            LocalFileUtils.makeDirs(dir);
        } catch (IOException e) {
            throw new ServiceException("创建文件夹失败：" + dir);
        }

    }

    public static String uploadFile(MultipartFile file, String bucketName, String path, String objectName) throws IOException {
        path = nomalizePath(path);
        // 创建OSSClient实例。
        OSSClient ossClient = getOSSClient(bucketName);
        // 上传内容到指定的存储空间（bucketName）并保存为指定的文件名称（objectName）。
        byte[] bytes = file.getBytes();
        System.out.println("音频字节大小：" + bytes.length);
        ossClient.putObject(bucketName, path + objectName, new ByteArrayInputStream(file.getBytes()));
        // 关闭OSSClient。
        ossClient.shutdown();
        //String resize = resize(bucketName, path+"o"+objectName,path+objectName,190,190);
        return generateDownloadUrlByBucket(bucketName, path + objectName);
    }

    private static String nomalizePath(String path) {
        if (path.startsWith("/"))//如果是以/开头
            path = path.substring(1, path.length());
        if (!path.endsWith("/"))//如果不是以/结尾
            path = path.concat("/");
        return path;
    }


    /**
     * 本地文件转byte数组
     * @param filePath
     * @return
     */
    public static byte[] getBytes(String filePath) {
        byte[] buffer = null;
        try {
            File file = new File(filePath);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }

    public static String generateDownloadUrlByBucket(String bucketName, String objectName) {
        if (StringUtils.isEmpty(objectName))
            return null;
        else
            return ossUrl + objectName;
    }


    /**
     * 上传文件方法
     */
    public static String uploadFileByByte(byte[] buffer, String bucketName, String path, String objectName) throws IOException {
        path = nomalizePath(path);
        // 创建OSSClient实例。
        OSSClient ossClient = getOSSClient(bucketName);
        // 设置Content-Disposition为inline。
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentDisposition("inline");
        // 上传内容到指定的存储空间（bucketName）并保存为指定的文件名称（objectName）。
        ossClient.putObject(bucketName, path + objectName, new ByteArrayInputStream(buffer), objectMetadata);
        // 关闭OSSClient。
        ossClient.shutdown();
        return generateDownloadUrlByBucket(bucketName, path + objectName);
    }


    /**
     * 上传文件方法
     */
    public static String uploadCDNFileByByte(byte[] buffer, String bucketName, String path, String objectName) throws IOException {
        path = nomalizePath(path);
        // 创建OSSClient实例。
        OSSClient ossClient = getOSSClient(bucketName);
        // 上传内容到指定的存储空间（bucketName）并保存为指定的文件名称（objectName）。
        ossClient.putObject(bucketName, path + objectName, new ByteArrayInputStream(buffer));
        // 关闭OSSClient。
        ossClient.shutdown();
        return ossUrl + path + objectName;
    }

    /**
     * 下载文件
     * @return
     */
    public static byte[] getObjectBytes(String bucketName, String objectName) throws IOException {
        // 创建OSSClient实例。
        OSSClient ossClient = getOSSClient(bucketName);
        // 调用ossClient.getObject返回一个OSSObject实例，该实例包含文件内容及文件元信息。
        OSSObject ossObject = ossClient.getObject(bucketName, objectName);
        // 调用ossObject.getObjectContent获取文件输入流，可读取此输入流获取其内容。
        InputStream content = ossObject.getObjectContent();
        byte[] bytes = new byte[1024];
        try {
            bytes = OssUtils.toByteArray(content);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
            if (content != null) {
                content.close();
            }
            // 关闭OSSClient。
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return bytes;
    }

    /**
     * io:输入流转byte[]
     * @param input
     * @return
     * @throws IOException
     */
    public static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024 * 4];
        int n = 0;
        while (-1 != (n = input.read(buffer))) {
            output.write(buffer, 0, n);
        }
        return output.toByteArray();
    }

    /**
     * 根据url下载文件流
     * @param urlStr
     * @return
     */
    public static InputStream getInputStreamFromUrl(String urlStr,Boolean isDecode) {
        InputStream inputStream = null;
        try {
            //url解码
            URL url = new URL(java.net.URLDecoder.decode(urlStr, "UTF-8"));
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置超时间为3秒
            conn.setConnectTimeout(3 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            //得到输入流
            inputStream = conn.getInputStream();
        } catch (IOException e) {

        }
        return inputStream;
    }


    /**
     * zip解压
     * @param srcFile     zip源文件
     * @param destDirPath 解压后的目标文件夹
     * @throws RuntimeException 解压失败会抛出运行时异常
     */
    public static void unZip(File srcFile, String destDirPath) throws RuntimeException {
        long start = System.currentTimeMillis();
        // 判断源文件是否存在
        if (!srcFile.exists()) {
            throw new RuntimeException(srcFile.getPath() + "所指文件不存在");
        }
        // 开始解压
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(srcFile);
            Enumeration<?> entries;
            entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
//				System.out.println("解压" + entry.getName());
                // 如果是文件夹，就创建个文件夹
                if (entry.isDirectory()) {
                    String dirPath = destDirPath + File.separator + entry.getName();
                    File dir = new File(dirPath);
                    dir.mkdirs();
                } else {
                    // 如果是文件，就先创建一个文件，然后用io流把内容copy过去
                    File targetFile = new File(destDirPath + File.separator + entry.getName());
                    // 保证这个文件的父文件夹必须要存在
                    if (!targetFile.getParentFile().exists()) {
                        targetFile.getParentFile().mkdirs();
                    }
                    targetFile.createNewFile();
                    // 将压缩文件内容写入到这个文件中
                    InputStream is = zipFile.getInputStream(entry);
                    FileOutputStream fos = new FileOutputStream(targetFile);
                    int len;
                    byte[] buf = new byte[BUFFER_SIZE];
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                    }
                    // 关流顺序，先打开的后关闭
                    fos.close();
                    is.close();
                }
            }
            long end = System.currentTimeMillis();
            System.out.println("解压完成，耗时：" + (end - start) + " ms");
        } catch (Exception e) {
            throw new RuntimeException("unzip error from ZipUtils", e);
        } finally {
            if (zipFile != null) {
                try {
                    zipFile.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 读取json文件，返回json串
     * @param fileName
     * @return
     */
    public static String readJsonFile(String fileName) {
        String jsonStr = "";
        try {
            File jsonFile = new File(fileName);
            FileReader fileReader = new FileReader(jsonFile);

            Reader reader = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            int ch = 0;
            StringBuffer sb = new StringBuffer();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            fileReader.close();
            reader.close();
            jsonStr = sb.toString();
            return jsonStr;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }


    public static File asFile(InputStream inputStream, String suffixName) throws IOException {
        //临时转存本机文件夹
        File linuxTemporaryUrl = new File(new File("").getCanonicalPath() + File.separator + "temp");
        if (!linuxTemporaryUrl.exists()) {
            linuxTemporaryUrl.mkdirs();
        }
        File tmp = File.createTempFile("sb3-", suffixName, linuxTemporaryUrl);
        OutputStream os = new FileOutputStream(tmp);
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
            os.write(buffer, 0, bytesRead);
        }
        inputStream.close();
        os.close();
        return tmp;
    }


    /**
     * 下载文件
     * @return
     */
    public static byte[] downLoadFile(String bucketName, String objectName, HttpServletResponse response) throws IOException {
        // 创建OSSClient实例。
        OSSClient ossClient = getOSSClient(bucketName);
        // 调用ossClient.getObject返回一个OSSObject实例，该实例包含文件内容及文件元信息。
        OSSObject ossObject = ossClient.getObject(bucketName, objectName);
        // 调用ossObject.getObjectContent获取文件输入流，可读取此输入流获取其内容。
        InputStream content = ossObject.getObjectContent();
        //HTTP协议 header是ISO8859-1
        String name = new String(objectName.getBytes("utf-8"), "ISO-8859-1");
        //设置 下载 响应头 "Content-Disposition", "attachment; filename=*****"
        response.setHeader("Content-Disposition", "attachment; filename=\"" + name + "\"; id=\"" + objectName + "\"");
        //使用nio将inputStream转换byte[]
        byte[] bytes = new byte[0];
        try {
            bytes = stream(content, response.getOutputStream());
        } catch (UnsupportedOperationException e) {
            e.printStackTrace();
        } finally {
            // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
            if (content != null) {
                content.close();
            }
            // 关闭OSSClient。
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return bytes;
    }


    /**
     * nio:输入流转byte[]
     * @param input
     * @return
     * @throws IOException
     */
    public static byte[] stream(InputStream input, OutputStream output) throws IOException {
        try (
                ReadableByteChannel inputChannel = Channels.newChannel(input);
                WritableByteChannel outputChannel = Channels.newChannel(output);
        ) {
            ByteBuffer buffer = ByteBuffer.allocate(10240);
            long size = 0;
            while (inputChannel.read(buffer) != -1) {
                buffer.flip();
                size += outputChannel.write(buffer);
                buffer.clear();
            }
            return buffer.array();
        }
    }


    public static String getRanadomFileName(String fileName) {
        int i = fileName.lastIndexOf(".");
        String str = fileName.substring(i);
        fileName = new Date().getTime() + str;
        return fileName;
    }


    /**
     * 上传文件方法
     */
    public static String uploadFile(MultipartFile file, String bucketName, String objectName) throws IOException {
        // 创建OSSClient实例。
        OSSClient ossClient = getOSSClient(bucketName);
        // 上传内容到指定的存储空间（bucketName）并保存为指定的文件名称（objectName）。
        ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(file.getBytes()));
        // 关闭OSSClient。
        ossClient.shutdown();
        return generateDownloadUrlByBucket(bucketName, objectName);
    }

    /**
     * copy文件方法
     */
    public static void copyFile(String bucketName, String sourceKey, String destinationKey) throws IOException {
        // 创建OSSClient实例。
        OSSClient ossClient = getOSSClient(bucketName);
        try {
            // 拷贝文件。
            CopyObjectResult result = ossClient.copyObject(bucketName, sourceKey, bucketName, destinationKey);
            System.out.println("ETag: " + result.getETag() + " LastModified: " + result.getLastModified());
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        // 关闭OSSClient。
        ossClient.shutdown();
    }

    public static String generatePresignedUrl(String fullUrl) throws MalformedURLException {
        OSSClient ossClient = getOSSClient(null);

        // Set the expiration time of the URL
        Date expiration = new Date(System.currentTimeMillis() + 60 * 1000); // 1 hour
        URL ossUrl = new URL(fullUrl);
        String objectName = ossUrl.getPath().substring(1).replaceFirst(ossBucketName + "/", "");
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(ossBucketName, objectName);
        generatePresignedUrlRequest.setExpiration(expiration);

        URL url = ossClient.generatePresignedUrl(generatePresignedUrlRequest);
        ossClient.shutdown();

        return url.toString();
    }


    public static String getFileSize(String url) {
        long contentLength = 0L;
        // 创建OSS客户端实例
        OSSClient ossClient = getOSSClient(null);
        try {
            URL ossUrl = new URL(url);
            String objectName = ossUrl.getPath().substring(1).replaceFirst(ossBucketName + "/", "");
            // 获取文件的元数据
            ObjectMetadata metadata = ossClient.getObjectMetadata(ossBucketName, objectName);
            // 获取文件大小
            contentLength = metadata.getContentLength();
            return formatFileSize(contentLength);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } finally {
            // 关闭OSS客户端
            if (ossClient != null) {
                ossClient.shutdown();
            }
            if (contentLength == 0){
                return "0K";
            }
        }
        return "0K";
    }

    public static String formatFileSize(long data) {

        if (data > 0) {

            double size = data * 1d;

            double kiloByte = size / 1024;
            if (kiloByte < 1 && kiloByte > 0) {
                //不足1K
                return String.format("%.2fByte", size);
            }
            double megaByte = kiloByte / 1024;
            if (megaByte < 1) {
                //不足1M
                return String.format("%.2fK", kiloByte);
            }

            double gigaByte = megaByte / 1024;
            if (gigaByte < 1) {
                //不足1G
                return String.format("%.2fM", megaByte);
            }

            double teraByte = gigaByte / 1024;
            if (teraByte < 1) {
                //不足1T
                return String.format("%.2fG", gigaByte);
            }

            return String.format("%.2fT", teraByte);
        }
        return "0K";
    }


}

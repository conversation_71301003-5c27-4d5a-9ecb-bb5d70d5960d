package com.yf.ability.upload.providers.cos.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 腾讯云上传配置数据传输类
 * </p>
 * <AUTHOR>
 * @since 2021-08-25 20:41
 */
@Data
@ApiModel(value = "腾讯云上传配置", description = "腾讯云上传配置")
public class CosConfig implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "ID", required = true)
    private String id;

    @ApiModelProperty(value = "存储Bucket")
    private String bucket;

    @ApiModelProperty(value = "秘钥ID")
    private String secretId;

    @ApiModelProperty(value = "秘钥密码")
    private String secretKey;

    @ApiModelProperty(value = "节点")
    private String region;

    @ApiModelProperty(value = "媒体列队")
    private String mediaQueue;

    @ApiModelProperty(value = "访问路径")
    private String url;

}

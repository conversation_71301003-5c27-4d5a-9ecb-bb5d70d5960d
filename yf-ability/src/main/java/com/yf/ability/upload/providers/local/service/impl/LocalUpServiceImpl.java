package com.yf.ability.upload.providers.local.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.yf.ability.Constant;
import com.yf.ability.config.dto.CfgPropDTO;
import com.yf.ability.config.enums.ConfigType;
import com.yf.ability.config.enums.ProviderType;
import com.yf.ability.config.service.CfgPropService;
import com.yf.ability.face.provides.ExamConfig;
import com.yf.ability.upload.providers.local.config.LocalConfig;
import com.yf.ability.upload.providers.local.dto.UploadReqDTO;
import com.yf.ability.upload.providers.local.dto.UploadRespDTO;
import com.yf.ability.upload.providers.local.service.LocalUpService;
import com.yf.ability.upload.providers.local.utils.HttpRequestUtils;
import com.yf.ability.upload.providers.local.utils.OssUtils;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.utils.jackson.JsonHelper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Base64;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 文件上传业务类
 * <AUTHOR>
 * @date 2019-07-30 21:02
 */
@Log4j2
@Service
public class LocalUpServiceImpl implements LocalUpService {


    @Autowired
    private Environment env;

    @Autowired
    private CfgPropService cfgPropService;

    @Value("${local-upload.file-dir}")
    private String localUploadFileDir;

    @Value("${local-upload.prefix-url}")
    private String localUploadPrefixUrl;


    @Override
    public UploadRespDTO upload(UploadReqDTO reqDTO) {

        //获取容器环境变量
        String examServerIp = StringUtils.isEmpty(env.getProperty("EXAM_SERVER_IP")) ? "127.0.0.1" : env.getProperty("EXAM_SERVER_IP");

        // 上传文件夹
        String fileDir = localUploadFileDir;

        // 上传文件url前缀
        String localUploadPrefix = localUploadPrefixUrl.replace("examExample", examServerIp);

        // 真实物理地址
        String filePath = null;

        //获取配置
        CfgPropDTO dto = cfgPropService.detail(ConfigType.EXAM, null);
        ExamConfig examConfig = JsonHelper.parseObject(dto.getData(), ExamConfig.class);
        String examCenterId = examConfig.getExamCenterId(); //拼接考点ID
        try {
            if (StringUtils.isNotEmpty(reqDTO.getBase64File())) {
                // 处理Base64文件
                byte[] fileBytes = Base64.getDecoder().decode(reqDTO.getBase64File());

                // 处理文件名
                String fileName = IdWorker.getIdStr() + ".jpg";
                filePath = OssUtils.processPath(fileName);
                String fullPath = fileDir + examCenterId + "/" + filePath;

                // 创建文件夹
                OssUtils.checkDir(fullPath);

                // 将字节数组写入文件
                try (FileOutputStream fos = new FileOutputStream(fullPath)) {
                    fos.write(fileBytes);
                }
            } else {
                // 处理MultipartFile文件
                MultipartFile file = reqDTO.getFile();
                if (file != null && !file.isEmpty()) {
                    filePath = OssUtils.processPath(file.getOriginalFilename());
                    String fullPath = fileDir + examCenterId + "/" + filePath;

                    // 创建文件夹
                    OssUtils.checkDir(fullPath);

                    // 上传文件
                    FileCopyUtils.copy(file.getInputStream(), new FileOutputStream(fullPath));
                } else {
                    throw new ServiceException("文件上传失败：无有效的文件或Base64字符串。");
                }
            }

            return this.generateResult(localUploadPrefix, localUploadFileDir.substring(1) + examCenterId + "/" + filePath);

        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("文件上传失败：" + e.getMessage());
        }
    }



    @Override
    public String upload(String localFile) {
        // 查找上传配置
        LocalConfig conf = this.getConfig();

        // 上传文件夹
        String fileDir = conf.getLocalDir();

        // 真实物理地址
        String fullPath;

        try {

            FileInputStream is = new FileInputStream(localFile);

            // 新文件
            String filePath = OssUtils.renameFile(localFile);
            // 文件保存地址
            fullPath = fileDir + filePath;
            // 创建文件夹
            OssUtils.checkDir(fullPath);
            // 上传文件
            FileCopyUtils.copy(is, new FileOutputStream(fullPath));

            return conf.getUrl() + Constant.FILE_PREFIX + filePath;

        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("文件上传失败：" + e.getMessage());
        }
    }


    @Override
    public void download(HttpServletRequest request, HttpServletResponse response) throws IOException {

        // 查找上传配置
        //LocalConfig conf = this.getConfig();

        // 获取真实的文件路径
        //String filePath = this.getRealPath(request.getRequestURI().substring(5));//截取 /xxkj
        File file = new File(request.getRequestURI().substring(5));

        if (!file.exists()) {
            throw new ServiceException("文件不存在！");
        }

        // 断点下载
        this.chunkDownload(request, response, file);
    }

    /**
     * 获取配置文件
     * @return
     */
    @Override
    public LocalConfig getConfig() {
        CfgPropDTO dto = cfgPropService.detail(ConfigType.UPLOAD, ProviderType.LOCAL);
        LocalConfig cfg = JsonHelper.parseObject(dto.getData(), LocalConfig.class);
        return cfg;
    }

    @Override
    public String localFilePath(String path) {
        return getRealPath(path);
    }

    @Override
    public byte[] downLoad(String url, HttpServletResponse response) {
        String name = url.substring(url.lastIndexOf("/") + 1);
        byte[] bytes = HttpRequestUtils.doGetDownLoad(url);
        response.setHeader("Content-Disposition", "attachment; filename=\"" + name + "\"; id=\"" + name + "\"");
        return bytes;
    }


    /**
     * 构造返回
     * @param fileName
     * @return
     */
    private UploadRespDTO generateResult(String domain, String fileName) {
        // 返回结果
        return new UploadRespDTO(domain + fileName);
    }


    /**
     * 获取真实物理文件地址
     * @param uri
     * @return
     */
    public String getRealPath(String uri) {

        String regx = Constant.FILE_PREFIX + "(.*)";

        // 查找全部变量
        Pattern pattern = Pattern.compile(regx);
        Matcher m = pattern.matcher(uri);
        if (m.find()) {
            String str = m.group(1);
            return localUploadFileDir + str;
        }

        return null;
    }

    /**
     * 支持以断点的方式输出文件，提供文件在线预览和视频在线播放
     * @param request
     * @param response
     * @param file
     * @throws IOException
     */
    public void chunkDownload(HttpServletRequest request,
                              HttpServletResponse response,
                              File file) throws IOException {

        //只读模式
        RandomAccessFile randomFile = new RandomAccessFile(file, "r");
        long contentLength = randomFile.length();
        String range = request.getHeader("Range");


        Tika tika = new Tika();
        String mimeType = tika.detect(file);
        response.setContentType(mimeType);
        response.setHeader("Accept-Ranges", "bytes");
        response.setHeader("Last-Modified", new Date().toString());

        // 断点下载支持
        long requestStart = 0, length = 0;

        // 第一次只返回长度让浏览器来请求更多数据
        if (StringUtils.isEmpty(range)) {
            length = contentLength;
            response.setHeader("Content-length", contentLength + "");
        } else {
            response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
            String[] ranges = range.split("=");
            long requestEnd = 0;
            if (ranges.length > 1) {
                String[] rangeData = ranges[1].split("-");
                requestStart = Long.parseLong(rangeData[0]);
                if (rangeData.length > 1) {
                    requestEnd = Long.parseLong(rangeData[1]);
                }
            }

            if (requestEnd > 0) {
                length = requestEnd - requestStart + 1;
                response.setHeader("Content-Range", "bytes " + requestStart + "-" + requestEnd + "/" + contentLength);
            } else {
                length = contentLength - requestStart;
                response.setHeader("Content-Range", "bytes " + requestStart + "-" + (contentLength - 1) + "/" + contentLength);
            }
            response.setHeader("Content-length", "" + length);
        }


        BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());
        randomFile.seek(requestStart);
        byte[] buffer = new byte[1024];
        while (length > 0) {
            int len = randomFile.read(buffer);
            if (length < buffer.length) {
                out.write(buffer, 0, (int) length);
            } else {
                out.write(buffer, 0, len);
                if (len < buffer.length) {
                    break;
                }
            }
            length -= buffer.length;
        }
        out.flush();
        out.close();
        randomFile.close();
    }

}

package com.yf.ability.live.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 直播信息DTO
 * <AUTHOR>
 */
@Data
public class LiveBuildDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "RTMP推流地址")
    private String pushUrl;

    @ApiModelProperty(value = "RTMP拉流地址")
    private String pullUrl;

    @ApiModelProperty(value = "M3U8地址")
    private String m3u8Url;

    @ApiModelProperty(value = "FLV地址")
    private String flvUrl;

    @ApiModelProperty(value = "UDP地址")
    private String udpUrl;
}

package com.yf.ability.config.controller;

import com.yf.ability.config.service.CfgSwitchService;
import com.yf.ability.log.annon.LogInject;
import com.yf.ability.log.enums.LogType;
import com.yf.base.api.api.ApiRest;
import com.yf.base.api.api.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 * 通用配置控制器
 * </p>
 * <AUTHOR>
 * @since 2020-04-17 09:12
 */
@Api(tags = {"功能配置"})
@RestController
@RequestMapping("/api/sys/config/switch")
public class CfgSwitchController extends BaseController {

    @Autowired
    private CfgSwitchService baseService;


    /**
     * 保存功能开关
     * @param map
     * @return
     */
    @LogInject(title = "保存功能配置", logType = LogType.SYSTEM)
    @RequiresPermissions(value = {"sys:config:update"})
    @ApiOperation(value = "添加或修改")
    @PostMapping("/save")
    public ApiRest save(@RequestBody Map<String, Object> map) {
        baseService.save(map);
        return super.success();
    }

    /**
     * 查找配置详情
     * @return
     */
    @ApiOperation(value = "查找详情")
    @PostMapping("/detail")
    public ApiRest detail() {
        Map<String, Object> map = baseService.allMap();
        return super.success(map);
    }
}

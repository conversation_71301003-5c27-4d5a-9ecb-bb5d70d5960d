package com.yf.ability.login.cropwechat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "crop-wechat.login")
public class CropWechatConfig {

    /**
     * 企业ID
     */
    private String cropId;

    /**
     * 应用ID
     */
    private String agentId;

    /**
     * 应用ID对应的秘钥
     */
    private String cropSecret;

    /**
     * 登录跳转地址
     */
    private String redirect;

    /**
     * 绑定企业微信回调地址
     */
    private String bindRedirect;

}

package com.yf.ability.excel;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.sax.handler.RowHandler;
import com.yf.ability.excel.annotation.ExcelField;
import com.yf.ability.face.provides.ExamConfig;
import com.yf.base.api.exception.ServiceException;
import com.yf.base.utils.jackson.JsonHelper;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ooxml.POIXMLDocumentPart;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing.CTMarker;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.rmi.ServerException;
import java.util.*;

/**
 * 导入Excel表格，支持.xls和.xlsx，基于hutool进行封装的
 * <AUTHOR>
 */
@Log4j2
public class ImportExcel {

    /**
     * 标题行号
     */
    private int headerNum;

    /**
     * 名称对应实体字段映射
     */
    private List<String> headerList = new ArrayList<>();

    /**
     * 提取出的数据列表
     */
    private List<Map<String, Object>> dataList = new ArrayList<>();


    /**
     * 处理数据
     * @return
     */
    private RowHandler createRowHandler() {
        return (sheetIndex, rowIndex, rowList) -> {

            // 提取表头出来
            if (rowIndex == headerNum) {
                for (Object obj : rowList) {
                    String title = String.valueOf(obj);
                    headerList.add(title);
                }
                return;
            }

            // 没有表头直接返回
            if (CollectionUtils.isEmpty(headerList)) {
                return;
            }


            // 对应数据成名称-值
            Map<String, Object> map = new HashMap<>(16);
            for (int i = 0; i < rowList.size(); i++) {
                if (i >= headerList.size()) {
                    break;
                }
                map.put(headerList.get(i), rowList.get(i));
            }


            // 加入列表
            this.addToList(map);

        };
    }

    /**
     * 将Map放入List
     * @param map
     */
    private void addToList(Map<String, Object> map) {
        boolean empty = true;

        // 全部key值为空，则可能是表格某个格子
        if (map == null || map.isEmpty()) {
            return;
        }

        // 循环值
        for (String key : map.keySet()) {
            Object val = map.get(key);
            if (val != null && !StringUtils.isEmpty(val.toString())) {
                empty = false;
            }
        }

        if (!empty) {
            dataList.add(map);
        }
    }

    /**
     * 读取文件，默认第二行为表头
     * @param file
     */
    public ImportExcel(MultipartFile file) {
        // 默认从第二行开始读表头
        this(file, 1,0);
    }


    /**
     * 读取文件，表头行号索引从0开始，即excel行号-1
     * @param file
     * @param headerNum
     * @param readSheetIndex 指定要读取的工作表索引，从0开始计数
     */
    public ImportExcel(MultipartFile file, int headerNum, int readSheetIndex) {

        // 头部行货
        this.headerNum = headerNum;

        String fileName = file.getOriginalFilename();
        if (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx")) {
            throw new ServiceException("导入的文件格式错误，必须为.xls或.xlsx");
        }

        try {
            ExcelUtil.readBySax(file.getInputStream(), readSheetIndex, createRowHandler());
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("数据流读取失败！");
        }
    }


    /**
     * 反射java类，形成一个表头名称<-->java字段的Map
     * @param clazz
     * @return
     */
    private Map<String, Field> processFields(Class clazz) {

        // 获取当前类字段
        Field[] fields = clazz.getDeclaredFields();

        // 标题对应注解
        Map<String, Field> fieldMap = new HashMap<>(16);


        for (Field field : fields) {
            if (!field.isAccessible()) {
                // 关闭反射访问安全检查，为了提高速度
                field.setAccessible(true);
            }

            // 只导出有注解的字段
            boolean export = field.isAnnotationPresent(ExcelField.class);
            if (!export) {
                continue;
            }

            // 加入列表
            ExcelField ann = field.getAnnotation(ExcelField.class);
            fieldMap.put(ann.title(), field);
        }

        return fieldMap;

    }

    /**
     * 返回对应实体列表数据
     * @param cls
     * @param <E>
     * @return
     */
    public <E> List<E> getDataList(Class<E> cls) throws Exception {

        // 名称对应关系
        Map<String, Field> fieldMap = this.processFields(cls);

        List<E> list = new ArrayList<>();
        // 收集所有必填字段
        Set<String> requiredFields = new HashSet<>();
        for (Map.Entry<String, Field> entry : fieldMap.entrySet()) {
            String key = entry.getKey();
            Field field = entry.getValue();
            ExcelField ann = field.getAnnotation(ExcelField.class);
            if (ann != null && ann.required()) {
                requiredFields.add(key);
            }
        }

        for (Map<String, Object> map : dataList) {

            // 使用JSON对象进行数据接收
            Map<String, Object> json = new HashMap<>(16);
            Boolean exist = false;
            for (String key : map.keySet()) {

                // 丢弃不存在的映射
                if (!fieldMap.containsKey(key)) {
                    continue;
                }

                // 创建实例并赋值
                Field field = fieldMap.get(key);

                // 原始表格数据
                Object val = map.get(key);

                // 注解参数
                ExcelField ann = field.getAnnotation(ExcelField.class);
                if (ann != null && ann.required()) {
                    // 判断字段值是否为空（这里可以根据实际情况进行更复杂的校验）
                    if (val == null || (val instanceof String && ((String) val).isEmpty())) {
                        // 如果是必填字段且值为空，抛出异常或记录错误
                        throw new ServiceException("导入失败， [" + ann.title() + "] 是 必填项,如已填写，请下载最新导入模版重试！");
                    }
                    // 从必填字段集合中移除已填写的字段
                    requiredFields.remove(key);
                }
                // 处理静态数据过滤：此处和导出一样，过滤器需要与导出写反的，如：正常=0,禁用=1
                this.transFilter(ann, val);

                // TODO 数据字典反向，暂时不处理，就是根据数据字典标题查值

                // 使用JSON转换，使用反射也可，效率问题待研究
                json.put(field.getName(), val);
                exist = true;
            }

            // 检查是否有未填写的必填字段
            if (!requiredFields.isEmpty()) {
                for (String fieldName : requiredFields) {
                    Field field = fieldMap.get(fieldName);
                    ExcelField ann = field.getAnnotation(ExcelField.class);
                    throw new ServiceException("导入失败， [" + ann.title() + "] 是 必填项,如已填写，请下载最新导入模版重试！");
                }
            }

            if (exist){
                list.add(JsonHelper.parseObject(json, cls));
            }
        }

        if (list.isEmpty()){
            throw new ServiceException("导入失败，请检查数据或下载最新导入模版重试！");
        }
        return list;
    }

    /**
     * 返回对应实体列表数据
     * @param cls
     * @param <E>
     * @return
     */
    public <E> List<E> getDataListExamUser(Class<E> cls, ExamConfig examConfig) throws Exception {
        Integer examIdCard = examConfig.getExamIdCard();
        Integer examAddress = examConfig.getExamAddress();
        // 名称对应关系
        Map<String, Field> fieldMap = this.processFields(cls);

        List<E> list = new ArrayList<>();

        // 获取数据字典配置
        for (Map<String, Object> map : dataList) {

            // 使用JSON对象进行数据接收
            Map<String, Object> json = new HashMap<>(16);
            Boolean exist = false;
            for (String key : map.keySet()) {

                // 丢弃不存在的映射
                if (!fieldMap.containsKey(key)) {
                    continue;
                }

                // 创建实例并赋值
                Field field = fieldMap.get(key);
                String fieldName = field.getName();

                // 原始表格数据
                Object val = map.get(key);

                // 注解参数
                ExcelField ann = field.getAnnotation(ExcelField.class);

                // 如果该字段是必填项
                if (
                        (fieldName.equals("idCard") && examIdCard != null && examIdCard == 1) ||
                        (fieldName.equals("certAddress") && examAddress != null && examAddress == 1)
                ) {
                    if (val == null || (val instanceof String && ((String) val).isEmpty())) {
                        // 如果是必填字段且值为空，抛出异常或记录错误
                        throw new ServiceException("导入失败， [" + key + "] 是必填项，如已填写，请下载最新导入模版重试！");
                    }
                }
                // 判断字段值是否为空（这里可以根据实际情况进行更复杂的校验）
                if (ann != null && ann.required()) {
                    if (val == null || (val instanceof String && ((String) val).isEmpty())) {
                        // 如果是必填字段且值为空，抛出异常或记录错误
                        throw new ServiceException("导入失败， [" + key + "] 是必填项，如已填写，请下载最新导入模版重试！");
                    }
                }
                // 处理静态数据过滤：此处和导出一样，过滤器需要与导出写反的，如：正常=0,禁用=1
                this.transFilter(ann, val);

                // TODO 数据字典反向，暂时不处理，就是根据数据字典标题查值

                // 使用JSON转换，使用反射也可，效率问题待研究
                json.put(field.getName(), val);
                exist = true;
            }
            if (exist) {
                list.add(JsonHelper.parseObject(json, cls));
            }
        }

        if (list.isEmpty()) {
            throw new ServiceException("导入失败，请检查数据或下载最新导入模版重试！");
        }
        return list;
    }


    /**
     * 数据字典转换
     * @param ann
     * @param val
     * @return
     */
    private Object transFilter(ExcelField ann, Object val) {

        String filter = ann.filter();

        // 不需要处理
        if (StringUtils.isEmpty(filter)) {
            return val;
        }

        String[] arr = filter.split(",");

        if (arr.length == 0) {
            return val;
        }

        for (String item : arr) {
            String[] arr1 = item.split("=");
            if (String.valueOf(val).equals(arr1[0])) {
                return arr1[1];
            }
        }

        // 进行转换
        return val;
    }
    /**
     *
     * 根据路径或文件名选择Excel版本
     *
     *
     * @param filePathOrName
     * @param in
     * @return
     * @throws IOException
     * @see [类、类#方法、类#成员]
     */
    public static Workbook chooseWorkbook(String filePathOrName, InputStream in)
            throws IOException
    {
        /** 根据版本选择创建Workbook的方式 */
        Workbook wb = null;
        boolean isExcel2003 = ExcelVersionUtil.isExcel2003(filePathOrName);

        if (isExcel2003)
        {
            //wb = new HSSFWorkbook(in);
            throw new ServerException("请使用 .xlsx 格式Excel！");
        }
        else
        {
            wb = new XSSFWorkbook(in);
        }

        return wb;
    }


    /**
     * 获取图片和位置 (xlsx)
     * @return
     * @throws IOException
     */
    public static Map<String, XSSFPictureData> getPictures (Workbook wb) throws IOException {
        Map<String, XSSFPictureData> map = null;
        try {
            map = new HashMap<>();
            /** 得到第一个shell */
            XSSFSheet sheet = (XSSFSheet) wb.getSheetAt(0);
            List<POIXMLDocumentPart> list = sheet.getRelations();
            for (POIXMLDocumentPart part : list) {
                if (part instanceof XSSFDrawing) {
                    XSSFDrawing drawing = (XSSFDrawing) part;
                    List<XSSFShape> shapes = drawing.getShapes();
                    for (XSSFShape shape : shapes) {
                        System.out.println(shape.getClass());
                        if (shape instanceof XSSFPicture) {
                            XSSFPicture picture = (XSSFPicture) shape;
                            if (picture != null) {
                                XSSFClientAnchor preferredSize = null;
                                try {//强转XSSFPicture类型可能会出现picture.getPreferredSize()空指针
                                    preferredSize = picture.getPreferredSize();
                                    if (preferredSize != null) {
                                        XSSFClientAnchor anchor = picture.getPreferredSize();
                                        CTMarker marker = anchor.getFrom();
                                        Integer row = marker.getRow();
                                        Integer col = marker.getCol();
                                        System.out.println(row+"-"+col);
                                        System.out.println(marker.getCol());
                                        map.put(row+"-"+col, picture.getPictureData());
                                    }
                                } catch (Exception e) {
                                    //e.printStackTrace();
                                }
                            }
                        }

                    }
                }
            }
        } catch (RuntimeException e) {
            e.printStackTrace();
            throw new ServerException("表格图片有误，请检查无误后再导入。");
        }
        return map;
    }

    static class ExcelVersionUtil
    {

        /**
         *
         * 是否是2003的excel，返回true是2003
         *
         *
         * @param filePath
         * @return
         * @see [类、类#方法、类#成员]
         */
        public static boolean isExcel2003(String filePath)
        {
            return filePath.matches("^.+\\.(?i)(xls)$");

        }

        /**
         *
         * 是否是2007的excel，返回true是2007
         *
         *
         * @param filePath
         * @return
         * @see [类、类#方法、类#成员]
         */
        public static boolean isExcel2007(String filePath)
        {
            return filePath.matches("^.+\\.(?i)(xlsx)$");

        }

    }


    /**
     * 返回对应实体列表数据

     * @return
     */
    public List<String> getHeaderList() throws Exception {
        return headerList;
    }
}

package com.yf.ability;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;


/**
 * 公众号accessToken同步刷新配置
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.redis")
public class AccessTokenSynchConfig {

    private List<String> accessTokenSynch;

}

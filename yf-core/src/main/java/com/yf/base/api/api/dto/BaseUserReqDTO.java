package com.yf.base.api.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 用户通用请求类
 * </p>
 * <AUTHOR>
 * @since 2019-04-20 12:15
 */
@Data
@ApiModel(value = "用户通用请求类", description = "用户通用请求类")
@AllArgsConstructor
@NoArgsConstructor
public class BaseUserReqDTO extends BaseDTO {

    @ApiModelProperty(value = "用户ID")
    private String userId;

}

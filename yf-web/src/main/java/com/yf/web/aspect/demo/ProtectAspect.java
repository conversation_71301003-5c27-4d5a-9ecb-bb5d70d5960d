package com.yf.web.aspect.demo;


import com.yf.ability.BaseConfig;
import com.yf.ability.demo.utils.ProtectUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 数据保护AOP，保护系统数据不被删除，用于演示环境，可以自行删除此类
 * <AUTHOR>
 */
@Order(0)
@Aspect
@Component
@Slf4j
public class ProtectAspect {

    @Autowired
    private ProtectUtils protectUtils;

    @Autowired
    private BaseConfig baseConfig;


    /**
     * 判断接口是否允许调用
     * @param pjp
     * @throws Throwable
     */
    @Before("execution(public *  com.yf..*.*Controller.*(..))")
    public void doBefore(JoinPoint pjp) throws Throwable {
        if (baseConfig.isDemo()) {
            protectUtils.checkForbidden(pjp);
        }
    }

    /**
     * 切入注解
     * @param pjp
     * @return
     * @throws Throwable
     */
    @Around("@annotation(com.yf.base.api.annon.DataProtect)")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {

        // 演示模式才运行
        if (baseConfig.isDemo()) {
            return protectUtils.protect(pjp);
        }

        // 直接返回
        return pjp.proceed();
    }
}
